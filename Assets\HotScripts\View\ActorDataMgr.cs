using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;

using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Utils;
using Apq.Web5.JsonReturn;

using CsvTables;

using Cysharp.Threading.Tasks;

using DataStructure;

using DTO.ActorDataDTO;

using LuaInterface;

using Newtonsoft.Json;

using UnityEngine;

namespace View
{
    /// <summary>
    /// 角色数据管理器
    /// </summary>
    [DisallowMultipleComponent]
    public class ActorDataMgr : MonoBehaviour
    {
        /// <summary>
        /// 角色数据(内存)
        /// </summary>
        public BubblingDic<int, ActorDataItem> Data { get; set; }

        /// <summary>
        /// 是否使用本地文件
        /// </summary>
        public bool IsUseLocalFile { get; set; } = true;

        /// <summary>
        /// 是否已从服务器加载完成
        /// </summary>
        public BubblingList<bool> ServerLoaded { get; set; }

        /// <summary>
        /// 是否已从本地加载完成
        /// </summary>
        public BubblingList<bool> LocalLoaded { get; set; }

        public void Awake()
        {
            Data = new(nameof(Data));
            ServerLoaded = new(nameof(ServerLoaded));
            LocalLoaded = new(nameof(LocalLoaded));

            ServerLoaded.Changed += ServerLoaded_Changed;
            LocalLoaded.Changed += LocalLoaded_Changed;
            Data.ChangedBubbling += Data_ChangedBubbling;
        }

        public void OnDestroy()
        {
            ServerLoaded.Changed -= ServerLoaded_Changed;
            LocalLoaded.Changed -= LocalLoaded_Changed;
            Data.ChangedBubbling -= Data_ChangedBubbling;
        }

        private void ServerLoaded_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is true)
            {
                // 引发Lua中的事件
                LuaManager.Instance.LuaState_.Call("EventManager.Fire2", 109, ServerLoaded.Value, LocalLoaded.Value,
                    true);
            }
        }

        private void LocalLoaded_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is true)
            {
                // 引发Lua中的事件
                LuaManager.Instance.LuaState_.Call("EventManager.Fire2", 109, ServerLoaded.Value, LocalLoaded.Value,
                    true);
            }
        }

        private void Data_ChangedBubbling(ChangeBubblingEventArgs e)
        {
            var first = e.BubblingList.FirstOrDefault();
            if (first is ActorDataItem data && Util.IsEquals(data.Parent, Data))
            {
                //Debug.Log($"Data_ChangedBubbling {data.DataCatalog}");
                // 引发Lua中的事件
                LuaManager.Instance.LuaState_.Call("EventManager.Fire2", 110, data.DataCatalog,
                    true);
            }
        }

        public async void Start()
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            // 从服务器读取数据
            var lst = await GetDataFromServer(actorId);

            // 读取配置中的所有初始值(自动给予的数据)
            var lstDefault = ReadInit();

            // 加入新的初始值
            lstDefault.Where(x => lst.All(d => d.DataCatalog != x.DataCatalog))
                .ToList().ForEach(x =>
                {
                    lst.Add(x);
                });

            // 使用本地存储
            if (IsUseLocalFile)
            {
                // 从本地读取出来的所有数据
                var lstLocal = new List<ActorDataItem>();
                SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<ActorDataCatalogCsv>().Pb.CSVTable.ToList().ForEach(
                    x =>
                    {
                        var item = ActorDataCatalogCsv.CreateDateItem(x.Id);
                        if (item.LoadFromLocalStorage(actorId))
                        {
                            lstLocal.Add(item);
                        }
                    });
                lst.ForEach(x =>
                {
                    // 如果本地值“更”新，替换值
                    var item = lstLocal.FirstOrDefault(l => l.DataCatalog == x.DataCatalog);
                    if (item != null && ((x.UpdateTime ?? DateTime.MinValue) < item.UpdateTime))
                    {
                        //DataCatalog = dto.DataCatalog;	// 这个不会变
                        x.ValueType = item.ValueType;
                        x.ExpireMode = item.ExpireMode;
                        x.StartTime = item.StartTime;
                        x.EndTime = item.EndTime;
                        //Remark = dto.Remark;	// 本地不存说明
                        x.UpdateTime = item.UpdateTime;

                        x.LongValues.Replace(item.LongValues);
                        x.DoubleValues.Replace(item.DoubleValues);
                        x.StrValues.Replace(item.StrValues);
                    }
                });

                // 加上仅存在本地，且未过期的数据
                lst.AddRange(lstLocal.Where(l => lst.All(x =>
                    l.IsUnExpired && x.DataCatalog != l.DataCatalog)));
            }

            // 所有数据项加入Data中
            lst.ForEach(x =>
            {
                Data[x.DataCatalog] = x;
            });

            ServerLoaded.Value = true;
            LocalLoaded.Value = true;

            // 加载后立即保存到本地
            if (IsUseLocalFile)
            {
                Data.Keys.ToList().ForEach(c => Data[c].SaveToLocalStorage(actorId).Forget());
            }
            // 加载后立即保存到服务器
            SaveToServer().Forget();

            // 定时保存到服务器
            Task_SaveToServer(300, this.GetCancellationTokenOnDestroy()).Forget();
        }

        /// <summary>
        /// 数据保存到服务器
        /// </summary>
        /// <param name="interval">间隔时长(秒)</param>
        /// <param name="token"></param>
        public async UniTaskVoid Task_SaveToServer(int interval, CancellationToken token)
        {
            try
            {
                for (; ; await UniTask.NextFrame())
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(interval), cancellationToken: token);
                    SaveToServer().Forget();
                    if (token.IsCancellationRequested) break;
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
                // ignore
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 读取配置中的所有初始值(自动给予的数据)
        /// </summary>
        public List<ActorDataItem> ReadInit()
        {
            var lst = new List<ActorDataItem>();

            SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<ActorDataCatalogCsv>().Pb.CSVTable
                .Where(x => x.IsAutoGive).ToList().ForEach(csvRow =>
            {
                if (!Data.ContainsKey(csvRow.Id))
                {
                    var item = ActorDataCatalogCsv.GetInit(csvRow.Id);
                    if (item != null) lst.Add(item);
                }
            });

            //SaveToServer().Forget();
            return lst;
        }

        #region 读写服务器

        /// <summary>
        /// 从服务器获取角色数据
        /// </summary>
        public async UniTask<List<ActorDataItem>> GetDataFromServer(long ActorId)
        {
            var rtn = new List<ActorDataItem>();

            var url = LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/ListActorData";
            var ps = new Dictionary<string, string>
            {
                ["ApiVersion"] = "v1",
                ["ActorId"] = LuaDataSrvClient.Instance.GetActorID().ToString(),
            };
            var httpRsp = await UnityHttpHelper.GetResponseString(url, HttpMethod.Get, ps);

            if (httpRsp.Success)
            {
                var httpRtn = JsonConvert.DeserializeObject<JsonTableRtn<ActorDataListItemDTO>>(httpRsp.Rsp);
                ServerLoaded.Value = false;
                rtn.AddRange(httpRtn.rows.Select(x =>
                    new ActorDataItem().LoadFromActorDataListItemDTO(x)).ToList());
            }

            return rtn;
        }

        /// <summary>
        /// 将所有数据保存到服务器
        /// </summary>
        /// <param name="isClear">是否清空所有数据</param>
        public async UniTask SaveToServer(bool isClear = false)
        {
            var url = LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/SaveActorDataMulti";
            var json = JsonConvert.SerializeObject(Data.Values.ToActorDataSaveDTO(LuaDataSrvClient.Instance.GetActorID(), isClear));
            await UnityHttpHelper.PostJson(url, json);

            Data.Values.ToList().ForEach(x => x.IsSaveToServer = true);
        }

        /// <summary>
        /// 将多个数据保存到服务器
        /// </summary>
        /// <param name="dataCatalogs">哪些数据类别</param>
        public async UniTask SaveMultiToServer(params int[] dataCatalogs)
        {
            var lst = Data.Values
                .Where(x => dataCatalogs.Contains(x.DataCatalog)).ToList();

            var url = LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/SaveActorDataMulti";
            var json = JsonConvert.SerializeObject(lst.ToActorDataSaveDTO(LuaDataSrvClient.Instance.GetActorID()));
            await UnityHttpHelper.PostJson(url, json);

            lst.ForEach(x => x.IsSaveToServer = true);
        }

        /// <summary>
        /// 将一个数据保存到服务器
        /// </summary>
        /// <param name="dataCatalog">数据类别</param>
        public async UniTask SaveToServer(int dataCatalog)
        {
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                await SaveToServer(item);
            }
        }

        /// <summary>
        /// 将一个数据保存到服务器
        /// </summary>
        public async UniTask SaveToServer(ActorDataItem item)
        {
            var url = LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/SaveActorData";
            var ps = new Dictionary<string, string>
                {
                    { "ApiVersion", "V1" },
                    { "ActorId", LuaDataSrvClient.Instance.GetActorID().ToString() },
                };
            var json = JsonConvert.SerializeObject(item.ToActorDataListItemDTO());
            await UnityHttpHelper.PostJson(url, json, ps);

            item.IsSaveToServer = true;
        }

        #endregion

        /// <summary>
        /// 保存数据后移除管理器
        /// </summary>
        public async UniTaskVoid SaveAndRemove()
        {
            // 保存到本地
            if (IsUseLocalFile)
            {
                var actorId = LuaDataSrvClient.Instance.GetActorID();
                Data.Values.ToList().ForEach(x =>
                    x.SaveToLocalStorage(actorId).Forget());
            }

            // 保存到服务器
            await SaveToServer();

            SingletonMgr.Instance.RemoveBehaviour<ActorDataMgr>();
        }

        /// <summary>
        /// 是否有指定的数据
        /// </summary>
        public bool HasDataCatalog(int dataCatalog)
        {
            return Data.ContainsKey(dataCatalog);
        }

        /// <summary>
        /// 移除一个或多个数据
        /// </summary>
        public void RemoveDataCatalog(params int[] dataCatalogs)
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            // 待移除的键
            var lst = Data.Keys.Where(dataCatalogs.Contains).ToList();

            // 移除前的准备
            lst.ForEach(x =>
            {
                var v = Data[x];
                v.ValueType = 0;

                // 从本地存储中移除
                if (IsUseLocalFile)
                {
                    v.SaveToLocalStorage(actorId).Forget();
                }
            });

            // 从服务器移除
            SaveMultiToServer(dataCatalogs).Forget();

            // 从内存移除
            lst.ForEach(x =>
            {
                Data.Remove(x);
            });
        }

        #region 获取或设置值

        #region Get

        /// <summary>
        /// 获取整数(索引位置)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="index">索引位置</param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public int GetIntByIndex(int dataCatalog, int index = 0, bool onlyValid = true)
        {
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                if (onlyValid && !item.IsUnExpired) return default;

                return item.LongValues
                    .Where((_, i) => i == index).Select(l => (int)l).FirstOrDefault();
            }

            return default;
        }

        /// <summary>
        /// 获取整数(列表)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public List<int> GetInt(int dataCatalog, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.LongValues.Select(l => (int)l).ToList();
            }

            return new List<int>();
        }

        /// <summary>
        /// 获取整数(数组)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public int[] GetIntArray(int dataCatalog, bool onlyValid = true)
        {
            return GetInt(dataCatalog, onlyValid).ToArray();
        }

        /// <summary>
        /// 获取整数(索引位置)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="index">索引位置</param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public long GetLongByIndex(int dataCatalog, int index = 0, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.LongValues.Where((_, i) => i == index).FirstOrDefault();
            }

            return default;
        }

        /// <summary>
        /// 获取整数(列表)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public List<long> GetLong(int dataCatalog, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.LongValues.ToList();
            }

            return new List<long>();
        }

        /// <summary>
        /// 获取浮点数(索引位置)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="index">索引位置</param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public double GetDoubleByIndex(int dataCatalog, int index = 0, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.DoubleValues.Where((_, i) => i == index).FirstOrDefault();
            }

            return default;
        }

        /// <summary>
        /// 获取浮点数(列表)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public List<double> GetDouble(int dataCatalog, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.DoubleValues.ToList();
            }

            return new List<double>();
        }

        /// <summary>
        /// 获取浮点数(数组)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public double[] GetDoubleArray(int dataCatalog, bool onlyValid = true)
        {
            return GetDouble(dataCatalog, onlyValid).ToArray();
        }

        /// <summary>
        /// 获取字符串(列表)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="index">索引位置</param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public string GetStringByIndex(int dataCatalog, int index = 0, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.StrValues.Where((_, i) => i == index).FirstOrDefault();
            }

            return default;
        }

        /// <summary>
        /// 获取字符串(列表)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public List<string> GetString(int dataCatalog, bool onlyValid = true)
        {
            // 有值且没过期
            if (Data.TryGetValue(dataCatalog, out var item) && (!onlyValid || item.IsUnExpired))
            {
                return item.StrValues.ToList();
            }

            return new List<string>();
        }

        /// <summary>
        /// 获取字符串(数组)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        public string[] GetStringArray(int dataCatalog, bool onlyValid = true)
        {
            return GetString(dataCatalog, onlyValid).ToArray();
        }

        #endregion

        #region GetOrGen

        /// <summary>
        /// 获取整数(列表)或生成
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public List<int> GetIntOrGen(int dataCatalog, bool onlyValid = true, Func<int[]> gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetInt(dataCatalog, gen());
                }
            }

            return GetInt(dataCatalog, onlyValid);
        }

        /// <summary>
        /// 获取整数(列表)或生成
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public List<long> GetLongOrGen(int dataCatalog, bool onlyValid = true, Func<long[]> gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetLong(dataCatalog, gen());
                }
            }

            return GetLong(dataCatalog, onlyValid);
        }

        /// <summary>
        /// 获取浮点数(列表)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public List<double> GetDoubleOrGen(int dataCatalog, bool onlyValid = true, Func<double[]> gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetDouble(dataCatalog, gen());
                }
            }

            return GetDouble(dataCatalog, onlyValid);
        }

        /// <summary>
        /// 获取字符串(列表)或生成
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public List<string> GetStringOrGen(int dataCatalog, bool onlyValid = true, Func<string[]> gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetString(dataCatalog, gen());
                }
            }

            return GetString(dataCatalog, onlyValid);
        }

        #endregion

        #region GetOrGen For Lua

        /// <summary>
        /// 获取整数(数组)或生成
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public int[] LuaGetIntArrayOrGen(int dataCatalog, bool onlyValid = true, LuaFunction gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetInt(dataCatalog, gen.Invoke<int[]>());
                }
            }

            return GetIntArray(dataCatalog, onlyValid);
        }

        /// <summary>
        /// 获取浮点数(数组)
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public double[] LuaGetDoubleArrayOrGen(int dataCatalog, bool onlyValid = true, LuaFunction gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetDouble(dataCatalog, gen.Invoke<double[]>());
                }
            }

            return GetDoubleArray(dataCatalog, onlyValid);
        }

        /// <summary>
        /// 获取字符串(数组)或生成
        /// </summary>
        /// <param name="dataCatalog"></param>
        /// <param name="onlyValid">是否仅取效数据</param>
        /// <param name="gen">生成方法</param>
        public string[] LuaGetStringArrayOrGen(int dataCatalog, bool onlyValid = true, LuaFunction gen = default)
        {
            // 无值或过期了
            if (!Data.TryGetValue(dataCatalog, out var item) || (onlyValid && !item.IsUnExpired))
            {
                if (gen != null)
                {
                    SetString(dataCatalog, gen.Invoke<string[]>());
                }
            }

            return GetStringArray(dataCatalog, onlyValid);
        }

        #endregion

        #region Set

        /// <summary>
        /// 设置整数值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetLong(int dataCatalog, params long[] values)
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            var needCreate = true;
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                needCreate = !item.IsUnExpired;

                if (needCreate)
                {
                    item.ValueType = 0;
                    SaveToServer(item).Forget();
                    if (IsUseLocalFile)
                    {
                        item.SaveToLocalStorage(actorId).Forget();
                    }
                }
            }

            if (needCreate)
            {
                item = ActorDataCatalogCsv.GetInit(dataCatalog);
                if (item != null)
                {
                    Data[dataCatalog] = item;
                }
            }

            if (item != null)
            {
                item.UpdateTime = SingletonMgr.Instance.GlobalMgr.GetServerNow();
                item.LongValues.Replace(values);
                item.IsSaveToServer = false;

                if (IsUseLocalFile)
                {
                    item.SaveToLocalStorage(actorId).Forget();
                }
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置整数值(第一个)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetLongFirst(int dataCatalog, long value = 0)
        {
            return SetLongByIndex(dataCatalog, 0, value);
        }

        /// <summary>
        /// 设置整数值(索引位置)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetLongByIndex(int dataCatalog, int index = 0, long value = 0)
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            var needCreate = true;
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                needCreate = !item.IsUnExpired;

                if (needCreate)
                {
                    item.ValueType = 0;
                    SaveToServer(item).Forget();
                    if (IsUseLocalFile)
                    {
                        item.SaveToLocalStorage(actorId).Forget();
                    }
                }
            }

            if (needCreate)
            {
                item = ActorDataCatalogCsv.GetInit(dataCatalog);
                if (item != null)
                {
                    Data[dataCatalog] = item;
                }
            }

            if (item != null)
            {
                item.UpdateTime = SingletonMgr.Instance.GlobalMgr.GetServerNow();
                item.LongValues[index] = value;
                item.IsSaveToServer = false;

                if (IsUseLocalFile)
                {
                    item.SaveToLocalStorage(actorId).Forget();
                }
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置整数值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetInt(int dataCatalog, params int[] values)
        {
            return SetLong(dataCatalog, values.Select(n => (long)n).ToArray());
        }

        /// <summary>
        /// 设置整数值(第一个)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetIntFirst(int dataCatalog, int value = 0)
        {
            return SetLongFirst(dataCatalog, value);
        }

        /// <summary>
        /// 设置整数值(索引位置)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetIntByIndex(int dataCatalog, int index = 0, int value = 0)
        {
            return SetLongByIndex(dataCatalog, index, value);
        }

        /// <summary>
        /// 设置浮点值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetDouble(int dataCatalog, params double[] values)
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            var needCreate = true;
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                needCreate = !item.IsUnExpired;

                if (needCreate)
                {
                    item.ValueType = 0;
                    SaveToServer(item).Forget();
                    if (IsUseLocalFile)
                    {
                        item.SaveToLocalStorage(actorId).Forget();
                    }
                }
            }

            if (needCreate)
            {
                item = ActorDataCatalogCsv.GetInit(dataCatalog);
                if (item != null)
                {
                    Data[dataCatalog] = item;
                }
            }

            if (item != null)
            {
                item.UpdateTime = SingletonMgr.Instance.GlobalMgr.GetServerNow();
                item.DoubleValues.Replace(values);
                item.IsSaveToServer = false;

                if (IsUseLocalFile)
                {
                    item.SaveToLocalStorage(actorId).Forget();
                }
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置浮点值(第一个)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetDoubleFirst(int dataCatalog, double value = 0)
        {
            return SetDoubleByIndex(dataCatalog, 0, value);
        }

        /// <summary>
        /// 设置浮点值(索引位置)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetDoubleByIndex(int dataCatalog, int index = 0, double value = 0)
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            var needCreate = true;
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                needCreate = !item.IsUnExpired;

                if (needCreate)
                {
                    item.ValueType = 0;
                    SaveToServer(item).Forget();
                    if (IsUseLocalFile)
                    {
                        item.SaveToLocalStorage(actorId).Forget();
                    }
                }
            }

            if (needCreate)
            {
                item = ActorDataCatalogCsv.GetInit(dataCatalog);
                if (item != null)
                {
                    Data[dataCatalog] = item;
                }
            }

            if (item != null)
            {
                item.UpdateTime = SingletonMgr.Instance.GlobalMgr.GetServerNow();
                item.DoubleValues[index] = value;
                item.IsSaveToServer = false;

                if (IsUseLocalFile)
                {
                    item.SaveToLocalStorage(actorId).Forget();
                }
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置字符串值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetString(int dataCatalog, params string[] values)
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            var needCreate = true;
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                needCreate = !item.IsUnExpired;

                if (needCreate)
                {
                    item.ValueType = 0;
                    SaveToServer(item).Forget();
                    if (IsUseLocalFile)
                    {
                        item.SaveToLocalStorage(actorId).Forget();
                    }
                }
            }

            if (needCreate)
            {
                item = ActorDataCatalogCsv.GetInit(dataCatalog);
                if (item != null)
                {
                    Data[dataCatalog] = item;
                }
            }

            if (item != null)
            {
                item.UpdateTime = SingletonMgr.Instance.GlobalMgr.GetServerNow();
                item.StrValues.Replace(values);
                item.IsSaveToServer = false;

                if (IsUseLocalFile)
                {
                    item.SaveToLocalStorage(actorId).Forget();
                }
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置字符串值(第一个)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetStringFirst(int dataCatalog, string value = "")
        {
            return SetStringByIndex(dataCatalog, 0, value);
        }

        /// <summary>
        /// 设置字符串值(索引位置)
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetStringByIndex(int dataCatalog, int index = 0, string value = "")
        {
            var actorId = LuaDataSrvClient.Instance.GetActorID();

            var needCreate = true;
            if (Data.TryGetValue(dataCatalog, out var item))
            {
                needCreate = !item.IsUnExpired;

                if (needCreate)
                {
                    item.ValueType = 0;
                    SaveToServer(item).Forget();
                    if (IsUseLocalFile)
                    {
                        item.SaveToLocalStorage(actorId).Forget();
                    }
                }
            }

            if (needCreate)
            {
                item = ActorDataCatalogCsv.GetInit(dataCatalog);
                if (item != null)
                {
                    Data[dataCatalog] = item;
                }
            }

            if (item != null)
            {
                item.UpdateTime = SingletonMgr.Instance.GlobalMgr.GetServerNow();
                item.StrValues[index] = value;
                item.IsSaveToServer = false;

                if (IsUseLocalFile)
                {
                    item.SaveToLocalStorage(actorId).Forget();
                }
                return true;
            }

            return false;
        }

        #endregion

        #region SetArray For Lua

        /// <summary>
        /// 设置整数值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetIntArray(int dataCatalog, int[] values)
        {
            return SetInt(dataCatalog, values);
        }

        /// <summary>
        /// 设置浮点值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetDoubleArray(int dataCatalog, double[] values)
        {
            return SetDouble(dataCatalog, values);
        }

        /// <summary>
        /// 设置字符串值
        /// </summary>
        /// <returns>是否设置成功</returns>
        public bool SetStringArray(int dataCatalog, string[] values)
        {
            return SetString(dataCatalog, values);
        }

        #endregion

        #endregion
    }
}
