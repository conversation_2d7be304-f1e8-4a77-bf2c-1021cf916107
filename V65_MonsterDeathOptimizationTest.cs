// V66.0 - 怪物死亡批量处理优化测试脚本
// 用于验证多只怪物同时死亡时的极速批量处理优化效果

using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using Debug = UnityEngine.Debug;

public class V66_MonsterDeathOptimizationTest : MonoBehaviour
{
    [Header("V66.0 测试配置")]
    [SerializeField] private int testMonsterCount = 200; // 测试怪物数量（提升到200）
    [SerializeField] private bool enablePerformanceTest = true; // 是否启用性能测试
    [SerializeField] private bool enableBatchTest = true; // 是否启用批量处理测试
    
    private Stopwatch stopwatch = new Stopwatch();
    private List<float> frameTimes = new List<float>();
    private float lastFrameTime;
    
    void Start()
    {
        if (enablePerformanceTest)
        {
            Debug.Log($"V66.0 开始怪物死亡批量处理优化测试，测试怪物数量: {testMonsterCount}");
            StartPerformanceMonitoring();
        }
    }
    
    void Update()
    {
        if (enablePerformanceTest)
        {
            // 记录帧时间
            float currentTime = Time.realtimeSinceStartup;
            if (lastFrameTime > 0)
            {
                float frameTime = currentTime - lastFrameTime;
                frameTimes.Add(frameTime);
                
                // 检测卡顿（超过33ms，即低于30FPS）
                if (frameTime > 0.033f)
                {
                    Debug.LogWarning($"V65.0 检测到卡顿: {frameTime * 1000:F2}ms (FPS: {1f/frameTime:F1})");
                }
            }
            lastFrameTime = currentTime;
        }
        
        // 按键测试
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestBatchMonsterDeath();
        }

        if (Input.GetKeyDown(KeyCode.Y))
        {
            TestExtremeMonsterDeath(); // V66.0 - 极限测试
        }

        if (Input.GetKeyDown(KeyCode.R))
        {
            ReportPerformanceStats();
        }
    }
    
    /// <summary>
    /// 开始性能监控
    /// </summary>
    private void StartPerformanceMonitoring()
    {
        frameTimes.Clear();
        lastFrameTime = Time.realtimeSinceStartup;
        Debug.Log("V65.0 性能监控已启动，按T键测试批量怪物死亡，按R键查看性能报告");
    }
    
    /// <summary>
    /// 测试批量怪物死亡
    /// </summary>
    private void TestBatchMonsterDeath()
    {
        var gameManager = FindObjectOfType<GameManager>();
        if (gameManager == null)
        {
            Debug.LogError("V65.0 未找到GameManager，无法进行测试");
            return;
        }
        
        Debug.Log($"V65.0 开始测试批量怪物死亡，当前怪物数量: {gameManager.Monsters.Count}");
        
        stopwatch.Restart();
        
        // 模拟大量怪物同时死亡
        var monstersToKill = new List<MonsterThing>();
        int killCount = Math.Min(testMonsterCount, gameManager.Monsters.Count);
        
        for (int i = 0; i < killCount; i++)
        {
            if (i < gameManager.Monsters.Count)
            {
                monstersToKill.Add(gameManager.Monsters[i]);
            }
        }
        
        Debug.Log($"V65.0 准备杀死 {monstersToKill.Count} 只怪物");
        
        // 记录开始时的帧率
        float startTime = Time.realtimeSinceStartup;
        int startFrameCount = Time.frameCount;
        
        // 批量杀死怪物
        foreach (var monster in monstersToKill)
        {
            if (monster != null && monster.Hp.Value > 0)
            {
                monster.Hp.Value = 0; // 直接设置血量为0触发死亡
            }
        }
        
        stopwatch.Stop();
        
        // 计算性能指标
        float endTime = Time.realtimeSinceStartup;
        int endFrameCount = Time.frameCount;
        float totalTime = endTime - startTime;
        int framesPassed = endFrameCount - startFrameCount;
        float avgFPS = framesPassed / totalTime;
        
        Debug.Log($"V66.0 批量死亡测试完成:");
        Debug.Log($"  - 杀死怪物数量: {monstersToKill.Count}");
        Debug.Log($"  - 处理时间: {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"  - 总耗时: {totalTime * 1000:F2}ms");
        Debug.Log($"  - 平均FPS: {avgFPS:F1}");
        Debug.Log($"  - 剩余怪物数量: {gameManager.Monsters.Count}");
    }
    
    /// <summary>
    /// 报告性能统计
    /// </summary>
    private void ReportPerformanceStats()
    {
        if (frameTimes.Count == 0)
        {
            Debug.Log("V65.0 暂无性能数据");
            return;
        }
        
        // 计算统计数据
        float totalTime = 0f;
        float minFrameTime = float.MaxValue;
        float maxFrameTime = 0f;
        int stutterCount = 0; // 卡顿次数（>33ms）
        
        foreach (float frameTime in frameTimes)
        {
            totalTime += frameTime;
            if (frameTime < minFrameTime) minFrameTime = frameTime;
            if (frameTime > maxFrameTime) maxFrameTime = frameTime;
            if (frameTime > 0.033f) stutterCount++;
        }
        
        float avgFrameTime = totalTime / frameTimes.Count;
        float avgFPS = 1f / avgFrameTime;
        float minFPS = 1f / maxFrameTime;
        float maxFPS = 1f / minFrameTime;
        float stutterRate = (float)stutterCount / frameTimes.Count * 100f;
        
        Debug.Log($"V65.0 性能统计报告 (样本数: {frameTimes.Count}):");
        Debug.Log($"  - 平均FPS: {avgFPS:F1} (帧时间: {avgFrameTime * 1000:F2}ms)");
        Debug.Log($"  - 最低FPS: {minFPS:F1} (帧时间: {maxFrameTime * 1000:F2}ms)");
        Debug.Log($"  - 最高FPS: {maxFPS:F1} (帧时间: {minFrameTime * 1000:F2}ms)");
        Debug.Log($"  - 卡顿率: {stutterRate:F1}% ({stutterCount}/{frameTimes.Count})");
        
        // 清空数据准备下次测试
        frameTimes.Clear();
        Debug.Log("V66.0 性能数据已清空，重新开始监控");
    }

    /// <summary>
    /// V66.0 - 极限测试：同时杀死所有怪物
    /// </summary>
    private void TestExtremeMonsterDeath()
    {
        var gameManager = FindObjectOfType<GameManager>();
        if (gameManager == null)
        {
            Debug.LogError("V66.0 未找到GameManager，无法进行极限测试");
            return;
        }

        int totalMonsters = gameManager.Monsters.Count;
        if (totalMonsters == 0)
        {
            Debug.LogWarning("V66.0 当前没有怪物，无法进行极限测试");
            return;
        }

        Debug.Log($"V66.0 开始极限测试：同时杀死所有 {totalMonsters} 只怪物");

        stopwatch.Restart();
        float startTime = Time.realtimeSinceStartup;
        int startFrameCount = Time.frameCount;

        // 同时杀死所有怪物
        var allMonsters = new List<MonsterThing>(gameManager.Monsters);
        foreach (var monster in allMonsters)
        {
            if (monster != null && monster.Hp.Value > 0)
            {
                monster.Hp.Value = 0; // 同时触发所有怪物死亡
            }
        }

        stopwatch.Stop();

        float endTime = Time.realtimeSinceStartup;
        int endFrameCount = Time.frameCount;
        float totalTime = endTime - startTime;
        int framesPassed = endFrameCount - startFrameCount;
        float avgFPS = framesPassed > 0 ? framesPassed / totalTime : 0;

        Debug.Log($"V66.0 极限测试完成:");
        Debug.Log($"  - 同时杀死怪物数量: {totalMonsters}");
        Debug.Log($"  - 触发时间: {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"  - 总耗时: {totalTime * 1000:F2}ms");
        Debug.Log($"  - 平均FPS: {avgFPS:F1}");
        Debug.Log($"  - 剩余怪物数量: {gameManager.Monsters.Count}");
        Debug.Log($"  - 批量处理效果: {(totalTime < 0.1f ? "优秀" : totalTime < 0.2f ? "良好" : "需要优化")}");
    }
    
    void OnGUI()
    {
        if (!enablePerformanceTest) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 350, 200));
        GUILayout.Label("V66.0 怪物死亡批量处理优化测试", GUI.skin.box);
        GUILayout.Label($"测试怪物数量: {testMonsterCount}");
        GUILayout.Label($"当前FPS: {1f / Time.deltaTime:F1}");
        
        var gameManager = FindObjectOfType<GameManager>();
        if (gameManager != null)
        {
            GUILayout.Label($"当前怪物数量: {gameManager.Monsters.Count}");
        }
        
        if (GUILayout.Button("测试批量死亡 (T)"))
        {
            TestBatchMonsterDeath();
        }

        if (GUILayout.Button("极限测试：杀死所有怪物 (Y)"))
        {
            TestExtremeMonsterDeath();
        }

        if (GUILayout.Button("性能报告 (R)"))
        {
            ReportPerformanceStats();
        }
        
        GUILayout.EndArea();
    }
}
