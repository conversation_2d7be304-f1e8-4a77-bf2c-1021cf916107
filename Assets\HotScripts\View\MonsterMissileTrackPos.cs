// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using HotScripts;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     导弹(定点打击)
    /// </summary>
    public class MonsterMissileTrackPos : MonsterBulletBase
    {
        /// <summary>
        ///     轨迹生成器(包含发射器)
        /// </summary>
        public MissileLocusGenerator MissileLocusGenerator { get; set; }

        /// <summary>
        ///     导弹轨迹(关键点)
        /// </summary>
        public List<Vector3> Locus { get; set; }

        /// <summary>
        ///     是用上面还是下面的轨迹
        /// </summary>
        public bool LocusDown { get; set; }

        /// <summary>
        ///     固定的起始位置（发射时确定，不会改变）
        /// </summary>
        private Vector3 fixedStartPosition;

        /// <summary>
        ///     固定的目标位置（发射时确定，不会改变）
        /// </summary>
        private Vector3 fixedTargetPosition;

        /// <summary>
        ///     固定的飞行总时长（发射时计算，不会改变）
        /// </summary>
        private float fixedTotalDuration;

        /// <inheritdoc />
        public override async UniTask OnAfterInitView()
        {
            await base.OnAfterInitView();

            // 发射时不让怪物移动
            MonsterThing.Monster.MonsterMoveAI.StopMoveAI();

            // V5.11彻底解决方案：在初始化时直接从怪物预制体获取固定位置，确保强关联
            if (MonsterThing?.Monster != null)
            {
                fixedStartPosition = MonsterThing.Monster.transform.position;
                //Debug.Log($"55555555 怪物抛物线子弹从预制体获取起点位置 - 预制体位置:{fixedStartPosition}");
            }
            else
            {
                fixedStartPosition = MissileLocusGenerator.MissileEjector.transform.position;
                //Debug.Log($"55555555 怪物抛物线子弹备用起点位置 - 发射器位置:{fixedStartPosition}");
            }
            
            fixedTargetPosition = MissileLocusGenerator.TargetPosition!.Value;
            
            // 计算固定的飞行总时长
            float distance = Vector3.Distance(fixedStartPosition, fixedTargetPosition);
            float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            fixedTotalDuration = distance / speed;

            // 调试日志：确认轨迹固定化状态
            //Debug.Log($"55555555 怪物抛物线子弹轨迹固定化 - 起点:{fixedStartPosition} 终点:{fixedTargetPosition} 飞行时长:{fixedTotalDuration} MoveDuration:{MoveDuration?.Value}");

            float bulletLocusDuration = (float)GunThing.GetTotalDouble(PropType.BulletLocusDuration).FirstOrDefault();
            if (bulletLocusDuration > 0)
            {
                // 落点预示
                EffectMgr.Instance.ShowEffect(EffectPath.BornCircle,
                    fixedTargetPosition, 2, null, bulletLocusDuration).Forget();

                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration));
            }
        }

        /// <inheritdoc />
        public override async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);
                
                // 重置子弹图片缩放
                if (ImgElement != null)
                {
                    ImgElement.transform.localScale = Vector3.one;
                }
                
                // 重置抛物线子弹特有字段，防止对象池复用时的脏数据
                fixedStartPosition = Vector3.zero;
                fixedTargetPosition = Vector3.zero;
                fixedTotalDuration = 0f;
                
                // 重置移动时间
                if (MoveDuration != null)
                {
                    MoveDuration.Value = 0f;
                }
                
                // 清空导弹发射器引用
                if (MissileLocusGenerator?.MissileEjector != null)
                {
                    MissileLocusGenerator.MissileEjector.gameObject.SetActive(false);
                }
                MissileLocusGenerator = null;
                
                // 清空轨迹数据
                Locus?.Clear();
                
                gameObject.SetActive(false);

                // 延时1分钟后置为null
                await UniTask.Delay(TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        #region 爆炸

        /// <summary>
        ///     在目标位置爆炸后归还到子弹池
        /// </summary>
        /// <param name="token"></param>
        public void DoExplose(CancellationToken token)
        {
            // 爆炸声音
            string exploseSound = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
            AudioPlayer.Instance.PlaySound(exploseSound).Forget();
            // 爆炸特效
            string exploseEffect = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
            // 爆炸半径
            float exploseRadius =
                (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();

            if (!string.IsNullOrWhiteSpace(exploseEffect))
            {
                EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect),
                    fixedTargetPosition,
                    exploseRadius).Forget();
            }

            // 找出被炸到的敌人
            if (SingletonMgr.Instance.BattleMgr.Actor.CircularArea2D.IsIntersect(new CircularArea2D
                {
                    Center = fixedTargetPosition, Radius = exploseRadius
                }))
            {
                (double damage, bool isCritical) =
                    Helper.CalcDamage(BulletThing, 0, 1, SingletonMgr.Instance.BattleMgr.Actor);

                // 受击方先接受枪携带的Buff
                SingletonMgr.Instance.BattleMgr.Actor.ReceiveBuffByBulletHit(BulletThing.CdExecutor.Thing,
                    BuffRecvType.Explose);

                // 敌人接受伤害
                SingletonMgr.Instance.BattleMgr.Actor.TakeHit(BulletThing.CdExecutor.Thing, damage, isCritical);
            }

            // 爆炸后出生新怪
            SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.BornMonster(
                fixedTargetPosition, 3,
                BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ExploseMonsterList).ConvertAll(x => (int)x),
                BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ExploseMonsterCountList).ConvertAll(x => (int)x)
            );

            // 界面还给子弹池
            TurnToPool().Forget();
        }

        #endregion

        #region 移动

        public override void StartMove()
        {
            base.StartMove();

            // 允许怪物移动
            MonsterThing.Monster.MonsterMoveAI.StartMove();
        }

        /// <summary>
        ///     任务实现:按轨迹移动
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                float duration = 0.03f;
                
                // 显示子弹图片
                ImgElement.SetActive(true);

                for (;; await UniTask.Delay(TimeSpan.FromSeconds(duration), cancellationToken: token))
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        // 使用固定的起始和目标位置计算轨迹
                        if (MoveOne_PositionMove(duration, fixedTotalDuration, null))
                        {
                            DoExplose(token);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        /// <inheritdoc />
        protected override bool OnBeforeMoveOne()
        {
            // 子弹销毁了或隐藏了，结束
            if (!this || !isActiveAndEnabled)
            {
                return true;
            }

            // // 达到子弹最大存活时长,结束
            // if (BulletThing.LifeBeginTime.Value +
            //     BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault() <= Time.time)
            // {
            //     return true;
            // }

            return false;
        }

        /// <inheritdoc />
        protected override void OnMoveEnd()
        {
            try
            {
                // 还给子弹池
                TurnToPool().Forget();
            }
            catch
            {
                // ignored
            }
        }

        /// <summary>
        ///     移动一次。传送至抛物线轨迹中按时间进度对应的点
        /// </summary>
        /// <returns>是否已走完整个轨迹</returns>
        public virtual bool MoveOne_PositionMove(float duration, float totalDuration, CubicBezierPath cubicBezier)
        {
            if (totalDuration <= 0)
            {
                return true;
            }

            // 移动计时
            MoveDuration.Value += duration;

            // 时间进度
            float progress = Mathf.Clamp01(MoveDuration.Value / totalDuration);

            #region 导弹前进一次(抛物线轨迹)

            // 起点和终点位置
            Vector3 startPos = fixedStartPosition;
            Vector3 endPos = fixedTargetPosition;
            
            // 计算抛物线的水平和垂直距离
            float horizontalDistance = Vector3.Distance(new Vector3(startPos.x, 0, startPos.z), new Vector3(endPos.x, 0, endPos.z));
            float verticalDistance = endPos.y - startPos.y;
            
            // 抛物线高度系数，显著增加高度让抛物线更明显
            float arcHeight = horizontalDistance * 1.2f; // 大幅增加抛物线最高点高度
            
            // 根据时间进度计算当前位置
            Vector3 currentPos = Vector3.Lerp(startPos, endPos, progress);
            
            // 使用更明显的抛物线公式：增强弧度效果
            // 使用修正的抛物线公式：y = 4h * t * (1-t) * (1 + 0.5*sin(π*t))
            float basicParabola = 4 * arcHeight * progress * (1 - progress); // 基础抛物线，系数从4增加到6
            float enhancedParabola = basicParabola * (1 + 0.3f * Mathf.Sin(Mathf.PI * progress)); // 添加正弦波增强弧度
            currentPos.y += enhancedParabola;
            
            // 计算导弹朝向（切线方向）- 基于增强的抛物线导数
            Vector3 direction = endPos - startPos;
            // 增强抛物线的导数：考虑正弦波影响
            float parabolaDerivative = 6 * arcHeight * (1 - 2 * progress) * (1 + 0.3f * Mathf.Sin(Mathf.PI * progress));
            parabolaDerivative += 6 * arcHeight * progress * (1 - progress) * 0.3f * Mathf.PI * Mathf.Cos(Mathf.PI * progress);
            
            Vector3 tangent = direction.normalized;
            if (horizontalDistance > 0)
            {
                tangent.y = parabolaDerivative / horizontalDistance; // 调整y方向的切线
            }
            
            // 设置导弹位置和朝向
            transform.position = currentPos;
            transform.right = tangent.normalized;
            
            // 增强抛物线视觉效果：更明显的缩放变化
            // 在抛物线最高点时显著放大，起点和终点时缩小
            float scaleProgress = 1 - Mathf.Abs(progress - 0.5f) * 2; // 0到1的缩放进度
            float minScale = 0.6f; // 减小最小缩放，让对比更明显
            float maxScale = 3.0f; // 增加最大缩放，让最高点更突出
            float currentScale = Mathf.Lerp(minScale, maxScale, Mathf.Pow(scaleProgress, 0.5f)); // 使用幂函数让缩放变化更平滑
            
            // 应用缩放到子弹图片
            if (ImgElement != null)
            {
                ImgElement.transform.localScale = Vector3.one * currentScale;
            }

            #endregion

            return progress >= 1;
        }

        #endregion
    }
}