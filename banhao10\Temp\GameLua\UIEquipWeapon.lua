--[[
********************************************************************
    created:	2024/05/17
    author :	李锦剑
    purpose:    体魄界面
*********************************************************************
--]]

local luaID = 'UIEquipWeapon'

--银两ID
local TaelGoodsID = 4
--背景图标1
local bgIconLiat1 = {
    [0] = 'sb_zbbjk_0',
    [1] = 'sb_zbbjk_1',
    [2] = 'sb_zbbjk_2',
    [3] = 'sb_zbbjk_3',
    [4] = 'sb_zbbjk_4',
    [5] = 'sb_zbbjk_5',
    [6] = 'sb_zbbjk_6',
}

---装备穿戴索引
local wearIndex = 3

---体魄界面
---@class UIEquipWeapon:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.StoreUpdateFree] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    --穿戴装备
    ---@type Item_EquipWeapon[]
    m.Item_EquipWeapon_List1 = {}
    --已解锁装备
    ---@type Item_EquipWeapon[]
    m.Item_EquipWeapon_List2 = {}
    --未解锁装备
    ---@type Item_EquipWeapon[]
    m.Item_EquipWeapon_List3 = {}
    --按钮数据
    ---@type {ID:integer, parent:any, creationFun:fun(parent:any, index:integer):Item_EquipWeapon}[]
    local buttonDataList = {
        --全部装备列表
        { ID = 1, parent = m.objList.Grid_Unlocked, creationFun = m.Creation_Item_EquipWeapon1, },
        --未解锁装备列表
        { ID = 2, parent = m.objList.Grid_Locked,   creationFun = m.Creation_Item_EquipWeapon1, },
        --穿戴装备
        { ID = 3, parent = m.objList.Grid_Wear,     creationFun = m.Creation_Item_EquipWeapon2, },
    }

    ---切换按钮
    ---@type Item_Toggle[]
    m.Item_Toggle_List = {}
    for i, v in ipairs(buttonDataList) do
        m.Item_Toggle_List[i] = m.Creation_Item_Toggle(m.objList.Grid_Toggle, i, v)
        if v.ID == 3 then
            wearIndex = i
        end
    end
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.objList.Grid_Wear.gameObject:SetActive(false)
    m.objList.Grid_Locked.gameObject:SetActive(false)
    m.objList.Grid_Unlocked.gameObject:SetActive(false)
    m.CloseEquipReplaceUI()
    m.SelectToggle(m.selectToggleIndex or 1)
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Info, function()
        if not m.selectEquipID then
            return
        end
        UIManager:OpenWnd(WndID.EquipWeaponInfo, m.selectEquipID)
        m.objList.Obj_SelectEquip.gameObject:SetActive(false)
        m.selectEquipID = nil
    end)
    m:AddClick(m.objList.Btn_Use, function()
        local id = m.selectEquipID
        if not id then
            return
        end
        if GamePlayerData.ActorEquip:IsWear(id, 1) then
            --卸下装备
            GamePlayerData.ActorEquip:UnloadEquip(id, 1)
        else
            if GamePlayerData.ActorEquip:GetResidualLatticeAmount(1) ~= 0 then
                --装备穿戴
                GamePlayerData.ActorEquip:WearEquip(id, 1)
            else
                --装备替换
                m.ShowEquipReplaceUI(id)
            end
        end
        m.objList.Obj_SelectEquip.gameObject:SetActive(false)
        m.selectEquipID = nil
    end)
    m:AddClick(m.objList.Btn_SelectEquip, function()
        m.objList.Obj_SelectEquip.gameObject:SetActive(false)
        m.selectEquipID = nil
    end, 0)
    m:AddClick(m.objList.Btn_Replace, m.CloseEquipReplaceUI)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    for i, v in ipairs(m.Item_Toggle_List) do
        v.UpdateView()
    end
end

--------------------------------------------------------------------
-- 装备排序
--------------------------------------------------------------------
function m.EquipSort(a, b)
    local cfg_a = Schemes.Equipment:Get(a)
    local cfg_b = Schemes.Equipment:Get(b)
    --装备品质--排序
    if cfg_a.QualityLevel ~= cfg_b.QualityLevel then
        return cfg_a.QualityLevel > cfg_b.QualityLevel
    end
    --装备部位--排序
    if cfg_a.SubType ~= cfg_b.SubType then
        return cfg_a.SubType > cfg_b.SubType
    end
    --装备等级--排序
    if cfg_a.UseLevel ~= cfg_b.UseLevel then
        return cfg_a.UseLevel > cfg_b.UseLevel
    end
    return a > b
end

--------------------------------------------------------------------
---创建装备框
---@param parent any
---@param index integer
---@return Item_EquipWeapon
--------------------------------------------------------------------
function m.Creation_Item_EquipWeapon1(parent, index)
    ---@class Item_EquipWeapon
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_EquipWeapon)
    item.com.anim = item.com.Img_Bg1:GetComponent("Animation")
    item.com.anim.enabled = false
    m:AddClick(item.com.Btn_Select, function()
        if item.equipID then
            if m.selectEquipReplace then
                GamePlayerData.ActorEquip:ReplaceEquip(item.equipID, m.selectEquipReplace, 1)
                m.CloseEquipReplaceUI()
            else
                if m.IsUnlocked(item.equipID) then
                    m.SelectEquip(item.equipID, item.com.gameObject, m.objList.Obj_Select)
                else
                    UIManager:OpenWnd(WndID.EquipWeaponInfo, item.equipID)
                end
            end
        end
    end)

    item.unlocked = EQUIP_WEAPON_GRID_UNLOCKED_CONDITION[index] or 0

    item.com.Img_Lock2.gameObject:SetActive(false)
    item.com.Txt_IsEquip.gameObject:SetActive(true)
    item.com.Img_Lock.gameObject:SetActive(true)
    item.com.Img_Bg2.gameObject:SetActive(true)

    ---更新装备数据
    ---@param equipID integer 装备ID
    item.UpdateData = function(equipID)
        item.equipID = equipID
        if equipID then
            --设置默认状态
            item.com.Arrows.gameObject:SetActive(false)
            item.com.Img_Expend.gameObject:SetActive(false)
            item.com.Obj_Locked.gameObject:SetActive(false)
            item.com.Sld_Slider.gameObject:SetActive(false)
            item.com.Obj_Unlocked.gameObject:SetActive(false)
            item.com.Txt_Name.gameObject:SetActive(false)
            item.com.Img_RedDot.gameObject:SetActive(false)

            HelperL.SetImageGray(item.com.Img_Icon, true)
            HelperL.SetImageGray(item.com.Img_Type, true)
            HelperL.SetImageGray(item.com.Img_Bg1, true)
            local equipment = Schemes.Equipment:Get(equipID)
            item.com.Txt_Name.text = equipment.GoodsName
            AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_Icon, true)
            AtlasManager:AsyncGetSprite(m.GetColorAndBg(equipment.QualityLevel), item.com.Img_Bg1)
            local gun = Schemes.Gun:Get(equipment.ConsignmentStyle)
            AtlasManager:AsyncGetSprite(gun.CornerIcon, item.com.Img_Type, true)
            item.com.Txt_IsEquip.text = ''

            --章节已解锁
            if m.IsUnlocked(equipID) then
                item.com.Obj_Unlocked.gameObject:SetActive(true)
                item.com.Txt_Name.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.Img_Icon, false)
                HelperL.SetImageGray(item.com.Img_Type, false)
                HelperL.SetImageGray(item.com.Img_Bg1, false)

                local skepBag = SkepModule:GetSkepByID(equipment.PacketID)
                local entity = skepBag:GetEntityByGoodsID(equipment.ID)
                local quality = equipment.Quality
                local starNum = equipment.StarNum
                local level = 1
                --装备已激活
                if entity then
                    quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
                    starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
                    level = quality * 10 + starNum + 1
                end

                if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                    item.com.Txt_IsEquip.text = GetGameText(luaID, 8)
                end

                local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
                if smelt and smelt.LevelMaxExp > 0 then
                    item.com.Txt_Level.text = CommonTextID.GRADE .. level
                    -- AtlasManager:AsyncGetSprite(smelt.GoodsID, item.com.Img_Expend)
                    local num1 = SkepModule:GetGoodsCount(smelt.GoodsID)
                    -- local num2 = SkepModule:GetGoodsCount(TaelGoodsID)
                    item.com.Sld_Slider.value = num1 / smelt.GoodsNum
                    item.com.Txt_Slide.text = string.format('%s/%s', HelperL.GetChangeNum(num1),
                        HelperL.GetChangeNum(smelt.GoodsNum))

                    local bool1 = not HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false, false)
                    local bool2 = not HelperL.IsLackGoods(TaelGoodsID, smelt.CostMoney, false, false)
                    if bool1 then
                        item.com.Img_Fill.color = Color(30 / 255, 235 / 255, 0, 1)
                    else
                        item.com.Img_Fill.color = Color(1, 200 / 255, 0, 1)
                    end

                    if bool1 and bool2 then
                        item.com.Arrows.gameObject:SetActive(true)
                    else
                        item.com.Img_Expend.gameObject:SetActive(true)
                    end
                    item.com.Sld_Slider.gameObject:SetActive(true)
                else
                    item.com.Txt_Level.text = CommonTextID.IS_FULL_LEVEL
                end
                item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIEquipWeapon(equipID))
            else
                --章节未解锁
                local cfg = Schemes.EquipWeapon:Get(equipID)
                item.com.Txt_LockedHint.text = string.format(GetGameText(luaID, 2), cfg.EffectID5)
                item.com.Obj_Locked.gameObject:SetActive(true)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    --播放动画
    item.PlayAnimation = function()
        item.com.anim.enabled = true
    end
    --动画重置
    item.ResetAnimation = function()
        item.com.anim.enabled = false
        item.com.Img_Bg1.transform.localScale = Vector3(1, 1, 1)
    end
    return item
end

--------------------------------------------------------------------
---创建装备框
---@param parent any
---@param index integer
---@return Item_EquipWeapon
--------------------------------------------------------------------
function m.Creation_Item_EquipWeapon2(parent, index)
    ---@type Item_EquipWeapon
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_EquipWeapon)
    item.com.anim = item.com.Img_Bg1:GetComponent("Animation")
    m:AddClick(item.com.Btn_Select, function()
        if item.equipID then
            if m.selectEquipReplace then
                GamePlayerData.ActorEquip:ReplaceEquip(item.equipID, m.selectEquipReplace, 1)
                m.CloseEquipReplaceUI()
            else
                m.SelectEquip(item.equipID, item.com.gameObject, m.objList.Obj_Select)
            end
        end
    end, 0)

    item.unlocked = EQUIP_WEAPON_GRID_UNLOCKED_CONDITION[index] or 0
    item.com.Img_Lock.gameObject:SetActive(false)
    item.com.Txt_IsEquip.gameObject:SetActive(true)
    item.com.Txt_IsEquip.text = ''
    item.com.Img_Lock2.gameObject:SetActive(true)

    ---更新装备数据
    ---@param equipID integer 装备ID
    item.UpdateData = function(equipID)
        item.equipID = equipID

        ---设置默认状态
        item.com.Arrows.gameObject:SetActive(false)
        item.com.Img_Expend.gameObject:SetActive(false)
        item.com.Img_Bg2.gameObject:SetActive(false)
        item.com.Obj_Locked.gameObject:SetActive(false)
        item.com.Obj_Unlocked.gameObject:SetActive(false)
        item.com.Txt_Name.gameObject:SetActive(false)
        item.com.Img_RedDot.gameObject:SetActive(false)
        item.com.Txt_IsEquip.text = ''

        item.com.Img_Bg1.color = Color(0, 0, 0, 1)
        local stageID = GamePlayerData.GameEctype:GetProgress(1)
        if stageID >= item.unlocked then
            if equipID then
                item.com.Img_Bg1.color = Color(1, 1, 1, 1)
                item.com.Img_Bg2.gameObject:SetActive(true)
                item.com.Obj_Unlocked.gameObject:SetActive(true)
                local equipment = Schemes.Equipment:Get(equipID)
                item.com.Txt_Name.text = equipment.GoodsName
                item.com.Txt_Name.gameObject:SetActive(true)
                AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_Icon, true)
                AtlasManager:AsyncGetSprite(m.GetColorAndBg(equipment.QualityLevel), item.com.Img_Bg1)
                local gun = Schemes.Gun:Get(equipment.ConsignmentStyle)
                AtlasManager:AsyncGetSprite(gun.CornerIcon, item.com.Img_Type)

                local skepBag = SkepModule:GetSkepByID(equipment.PacketID)
                local entity = skepBag:GetEntityByGoodsID(equipment.ID)
                local quality = equipment.Quality
                local starNum = equipment.StarNum
                local level = 1
                --装备已激活
                if entity then
                    quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
                    starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
                    level = quality * 10 + starNum + 1
                end

                local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
                if smelt and smelt.LevelMaxExp > 0 then
                    item.com.Txt_Level.text = CommonTextID.GRADE .. level
                    -- AtlasManager:AsyncGetSprite(smelt.GoodsID, item.com.Img_Expend)
                    local num1 = SkepModule:GetGoodsCount(smelt.GoodsID)
                    -- local num2 = SkepModule:GetGoodsCount(TaelGoodsID)
                    item.com.Sld_Slider.value = num1 / smelt.GoodsNum
                    item.com.Txt_Slide.text = string.format('%s/%s', HelperL.GetChangeNum(num1),
                        HelperL.GetChangeNum(smelt.GoodsNum))

                    local bool1 = not HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false, false)
                    local bool2 = not HelperL.IsLackGoods(TaelGoodsID, smelt.CostMoney, false, false)
                    if bool1 then
                        item.com.Img_Fill.color = Color(30 / 255, 235 / 255, 0, 1)
                    else
                        item.com.Img_Fill.color = Color(1, 200 / 255, 0, 1)
                    end

                    if bool1 and bool2 then
                        item.com.Arrows.gameObject:SetActive(true)
                    else
                        item.com.Img_Expend.gameObject:SetActive(true)
                    end
                    item.com.Sld_Slider.gameObject:SetActive(true)
                else
                    item.com.Txt_Level.text = CommonTextID.IS_FULL_LEVEL
                end
                item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIEquipWeapon(equipID))
            else
                if m.unlockedNum > 0 then
                    item.com.Txt_IsEquip.text = GetGameText(luaID, 1)
                    item.com.Img_RedDot.gameObject:SetActive(true)
                end
            end
        else
            item.com.Txt_LockedHint.text = string.format(GetGameText(luaID, 2), item.unlocked)
            item.com.Obj_Locked.gameObject:SetActive(true)
        end
    end
    --播放动画
    item.PlayAnimation = function()
        local stageID = GamePlayerData.GameEctype:GetProgress(1)
        if stageID >= item.unlocked then
            item.com.anim.enabled = true
        end
    end
    --动画重置
    item.ResetAnimation = function()
        item.com.anim.enabled = false
        item.com.Img_Bg1.transform.localScale = Vector3(1, 1, 1)
    end
    return item
end

--------------------------------------------------------------------
---判断已解锁
---@param id integer 装备ID
--------------------------------------------------------------------
function m.IsUnlocked(id)
    local cfg = Schemes.EquipWeapon:Get(id)
    local stageID = GamePlayerData.GameEctype:GetProgress(1)
    if stageID >= cfg.EffectID5 then
        return true
    end
    return false
end

--------------------------------------------------------------------
---获取背景颜色和图片
---@param quality integer
--------------------------------------------------------------------
function m.GetColorAndBg(quality)
    if quality >= 0 and quality <= 6 then
        return bgIconLiat1[quality]
    end
    return bgIconLiat1[0]
end

--------------------------------------------------------------------
---选择装备
---@param id integer 装备ID
---@param obj any 对象，用于获取坐标信息
--------------------------------------------------------------------
function m.SelectEquip(id, obj, parent)
    if m.selectEquipID == id then
        m.objList.Obj_SelectEquip.gameObject:SetActive(false)
        m.selectEquipID = nil
        return
    end
    m.objList.Obj_SelectEquip.transform:SetParent(parent.transform)
    m.objList.Obj_SelectEquip.transform.position = obj.transform.position
    m.selectEquipID = id
    local equipment = Schemes.Equipment:Get(id)
    m.objList.Txt_Name.text = equipment.GoodsName
    AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(id), m.objList.Img_Icon, true)
    AtlasManager:AsyncGetSprite(m.GetColorAndBg(equipment.QualityLevel), m.objList.Img_Bg1)
    local gun = Schemes.Gun:Get(equipment.ConsignmentStyle)
    AtlasManager:AsyncGetSprite(gun.CornerIcon, m.objList.Img_Type, true)

    local quality = equipment.Quality
    local starNum = equipment.StarNum
    local level = 1
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    --装备已激活
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        level = quality * 10 + starNum + 1
    end
    if GamePlayerData.ActorEquip:IsWear(id, 1) then
        m.objList.Txt_Info.text = GetGameText(luaID, 4)
    else
        m.objList.Txt_Info.text = GetGameText(luaID, 3)
    end
    m.objList.Txt_Level.text = CommonTextID.GRADE .. level

    m.objList.Img_RedDotInfo.gameObject:SetActive(RedDotCheckFunc:Check_UIEquipWeapon(id))
    m.objList.Obj_SelectEquip.gameObject:SetActive(true)
end

--------------------------------------------------------------------
-- 显示装备替换界面
---@param id integer 装备ID
--------------------------------------------------------------------
function m.ShowEquipReplaceUI(id)
    m.SelectToggle(wearIndex)
    m.selectEquipReplace = id
    local item = m.Item_Toggle_List[m.selectToggleIndex]
    for i, v in ipairs(item.Item_EquipWeapon_List) do
        v.PlayAnimation()
    end
    m.objList.Btn_Replace.gameObject:SetActive(true)
    m.objList.Obj_Replace.gameObject:SetActive(true)
end

--------------------------------------------------------------------
-- 关闭装备替换界面
--------------------------------------------------------------------
function m.CloseEquipReplaceUI()
    if m.selectEquipReplace == nil or m.selectToggleIndex == nil then
        return
    end
    m.selectEquipReplace = nil
    local item = m.Item_Toggle_List[m.selectToggleIndex]
    for i, v in ipairs(item.Item_EquipWeapon_List) do
        v.ResetAnimation()
    end
    m.objList.Btn_Replace.gameObject:SetActive(false)
    m.objList.Obj_Replace.gameObject:SetActive(false)
end

--------------------------------------------------------------------
---切换按钮
---@param index integer
--------------------------------------------------------------------
function m.SelectToggle(index)
    if m.selectEquipReplace ~= nil then
        return
    end

    m.objList.Btn_Replace.gameObject:SetActive(false)
    m.objList.Obj_Replace.gameObject:SetActive(false)
    m.objList.Obj_SelectEquip.gameObject:SetActive(false)

    local item
    if m.selectToggleIndex then
        item = m.Item_Toggle_List[m.selectToggleIndex]
        item.data.parent.gameObject:SetActive(false)
        item.com.Img_Bg1.gameObject:SetActive(true)
        item.com.Img_Bg2.gameObject:SetActive(false)
    end
    m.selectToggleIndex = index
    item = m.Item_Toggle_List[index]
    item.data.parent.gameObject:SetActive(true)
    item.com.Img_Bg1.gameObject:SetActive(false)
    item.com.Img_Bg2.gameObject:SetActive(true)
    m.UpdateView()
end

--------------------------------------------------------------------
---创建切换按钮
---@param parent any
---@param index integer
---@param data {ID:integer, parent:any, creationFun:fun(parent:any, index:integer):Item_EquipWeapon}
---@return Item_Toggle
--------------------------------------------------------------------
function m.Creation_Item_Toggle(parent, index, data)
    ---@class Item_Toggle
    local item = {}
    item.parent = parent
    item.index = index
    item.data = data
    ---装备框
    ---@type Item_EquipWeapon[]
    item.Item_EquipWeapon_List = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Toggle)
    item.com.Img_Bg1.gameObject:SetActive(true)
    item.com.Img_Bg2.gameObject:SetActive(false)
    m:AddClick(item.com.gameObject, function()
        m.SelectToggle(item.index)
    end, 0)

    ---更新视图
    item.UpdateView = function()
        --穿戴装备列表
        local wearList      = {}
        --已解锁装备列表
        local unlockedList  = {}
        --未解锁装备列表
        local lockedList    = {}
        --全部装备列表
        local allList       = {}
        --已解锁未穿戴装备列表
        local unlockedList2 = {}
        for i, v in ipairs(Schemes.EquipWeapon.items) do
            if v.GroupID < 1000 then
                if m.IsUnlocked(v.ID) then
                    if GamePlayerData.ActorEquip:IsWear(v.ID, 1) then
                        -- table.insert(wearList, v.ID)
                    else
                        table.insert(unlockedList2, v.ID)
                    end
                    table.insert(unlockedList, v.ID)
                else
                    table.insert(lockedList, v.ID)
                end
                table.insert(allList, v.ID)
            end
        end
        --排序
        -- table.sort(wearList, m.EquipSort)
        table.sort(unlockedList, m.EquipSort)
        table.sort(lockedList, m.EquipSort)

        --出战装备列表
        local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        for i = 1, #EQUIP_WEAPON_GRID_UNLOCKED_CONDITION, 1 do
            wearList[i] = weaponsKnapsack[i]
        end

        --未解锁未穿戴装备数量
        m.unlockedNum = #unlockedList2

        local butName = ''
        local equipDataList = {}
        local length = 0
        item.com.Img_RedDot.gameObject:SetActive(false)
        if item.data.ID == 1 then
            --全部装备列表
            equipDataList = allList
            length = #allList
            butName = string.format(GetGameText(luaID, 5), #unlockedList, #allList)
            item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIEquipWeapon(nil, 3))
        elseif item.data.ID == 2 then
            --未解锁装备列表
            equipDataList = lockedList
            length = #lockedList
            butName = GetGameText(luaID, 6)
        elseif item.data.ID == 3 then
            --穿戴装备
            equipDataList = wearList
            length = #EQUIP_WEAPON_GRID_UNLOCKED_CONDITION
            butName = string.format(GetGameText(luaID, 7), #wearList, length)
            item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIEquipWeapon())
        end

        --创建装备框
        local num = math.max(#item.Item_EquipWeapon_List, length)
        for i = 1, num, 1 do
            if not item.Item_EquipWeapon_List[i] then
                item.Item_EquipWeapon_List[i] = item.data.creationFun(item.data.parent, i)
            end
            item.Item_EquipWeapon_List[i].UpdateData(equipDataList[i])
        end
        item.com.Txt_Name1.text = butName
        item.com.Txt_Name2.text = item.com.Txt_Name1.text
        m.objList.Txt_Hint.gameObject:SetActive(#lockedList == 0 and m.objList.Grid_Unlocked.gameObject.activeSelf)
    end

    return item
end

return m
