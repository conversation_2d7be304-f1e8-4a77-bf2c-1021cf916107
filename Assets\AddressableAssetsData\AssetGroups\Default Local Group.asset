%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb281ee3bf0b054c82ac2347e9e782c, type: 3}
  m_Name: Default Local Group
  m_EditorClassIdentifier: 
  m_GroupName: Default Local Group
  m_Data:
    m_SerializedData: []
  m_GUID: bf172ee1b8f31c540ad5208cc9c3372d
  m_SerializeEntries:
  - m_GUID: 526dae9cc284fdc42a43a83637156cb3
    m_Address: Assets/Res/Animations/UI Animations/Model/ScaleAction2.anim
    m_ReadOnly: 0
    m_SerializedLabels: []
  - m_GUID: 9d71245432998204c8a3061dff3ed751
    m_Address: Assets/Temp/ui/Main/ChaoJiChongZhi.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
  - m_GUID: 5af91e83eb514a74b94e12688cf4dde6
    m_Address: Assets/Temp/ui/Main/XinFa.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
  - m_GUID: c019c0e92ac1dbc4791e71c5006ea1a3
    m_Address: Assets/Temp/ui/Main/Holy.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
  - m_GUID: b4685481697fbcd49993723055fa9a64
    m_Address: Assets/Temp/ui/Main/RecommendCommodities.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
  m_ReadOnly: 0
  m_Settings: {fileID: 11400000, guid: 0140d68c6c98cb041889aad8f1afbd96, type: 2}
  m_SchemaSet:
    m_Schemas:
    - {fileID: 11400000, guid: 6bd7bccfa1af7ff458bd7af895fc050e, type: 2}
    - {fileID: 11400000, guid: 329e7e426f0ae2948b5362695c3e2afc, type: 2}
