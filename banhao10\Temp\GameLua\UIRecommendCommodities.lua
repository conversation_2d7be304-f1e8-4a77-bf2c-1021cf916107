--[[
********************************************************************
    created:    2023/08/29
    author :    李锦剑
    purpose:    超值礼包礼包界面
*********************************************************************
--]]

local luaID = ('UIRecommendCommodities')

local m = {}
local cardID = 28
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpen<PERSON>ventList()
	return {
		[EventID.LogicDataChange] = m.UpdateView,
		[EventID.EntitySyncBuff] = m.UpdateView,
		--[EventID.OnHeroPropChange] = m.UpdateView,
	}
end

--资源预加载
function m.GetResourceList()
	return {
	}
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.goodsItemList = {}

	m.rechargeCard = Schemes.RechargeCard:Get(cardID)
	local goodsList = Schemes.PrizeTable:GetGoodsList(m.rechargeCard.PrizeID)
	if goodsList then
		local pos = { Vector2(0, 90), Vector2(-90, -90), Vector2(90, -90), }
		for i = 1, 3, 1 do
			if not m.goodsItemList[i] then
				m.goodsItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
			end
			if goodsList[i] then
                m.goodsItemList[i]:SetItemID(goodsList[i].id)
                m.goodsItemList[i]:SetCount(goodsList[i].num)
                m.goodsItemList[i].gameObject:SetActive(true)
				m.goodsItemList[i].gameObject:GetRectTransform().anchoredPosition = pos[i]
            else
                m.goodsItemList[i].gameObject:SetActive(false)
            end
		end
	end

	m.objList.Txt_Buy.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
	m.objList.Txt_Buy22.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
	m.objList.Txt_Grey2.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
	m.objList.Txt_Describe.text = string.gsub(m.rechargeCard.FirstCharacter1, '<P><P>', '\n')
	m.objList.Btn_Grey2.gameObject:SetActive(false)
	m.objList.Btn_Buy22.gameObject:SetActive(false)
	if SWITCH.RECHARGE then
		m.objList.Btn_Buy22.gameObject:SetActive(true)
	else
		-- m.objList.Btn_Grey2.gameObject:SetActive(true)
	end
	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
	m.AddRedDot()
	m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(function()
		m:CloseSelf()
	end)

	m.objList.Btn_Buy.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)
	m.objList.Btn_Grey.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)
	m.objList.Btn_Grey2.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)
	m.objList.Btn_Buy22.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)

	m.objList.Btn_AD2.onClick:AddListenerEx(function()
		AdvertisementManager.ShowRewardAdByCardID(cardID)
	end)

	m.objList.Btn_AD22.onClick:AddListenerEx(function()
		HelperL.GetAdverHint(m.rechargeCard.Description, true)
	end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	local isBuyCard = HelperL.HadBoughtCardID(cardID)
	m.objList.buy2.gameObject:SetActive(false)
	m.objList.Btn_Buy.gameObject:SetActive(false)
	m.objList.Btn_Grey.gameObject:SetActive(false)
	if isBuyCard then
		m.objList.Btn_Grey.gameObject:SetActive(true)
		m.objList.Txt_Grey.text = CommonTextID.IS_PURCHASE
	else
		m.objList.Btn_Buy.gameObject:SetActive(true)
		-- if m.rechargeCard.Description ~= '0' then
		-- 	m.objList.buy2.gameObject:SetActive(true)
		-- else
		-- 	if SWITCH.RECHARGE then
		-- 		m.objList.Btn_Buy.gameObject:SetActive(true)
		-- 	else
		-- 		-- m.objList.Btn_Grey.gameObject:SetActive(true)
		-- 		-- m.objList.Txt_Grey.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
		-- 	end
		-- end
	end

	local num, toTime = Schemes.CommonText.GetAdToTimeByCardID(cardID)
	m.objList.Txt_AdNum2.text = string.format(GetGameText(luaID, 19), UI_COLOR.Red, num, toTime)
end

--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
	local time, state, commonText

	m.objList.Btn_AD2.gameObject:SetActive(false)
	m.objList.Btn_AD22.gameObject:SetActive(false)
	-- m.objList.Img_AD22.gameObject:SetActive(false)
	m.objList.Txt_AD22.gameObject:SetActive(false)
	state = HelperL.GetAdverState(m.rechargeCard.Description)
	if state ~= 4 then
		if state == 2 then
			commonText = Schemes.CommonText:Get(m.rechargeCard.Description)
			time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
			m.objList.Txt_AD22.text = HelperL.GetTimeString(TimeStringType.FullAuto2, time)
			m.objList.Btn_AD22.gameObject:SetActive(true)
			m.objList.Txt_AD22.gameObject:SetActive(true)
		elseif state == 3 then
			-- m.objList.Img_AD22.gameObject:SetActive(true)
			m.objList.Btn_AD22.gameObject:SetActive(true)
		else
			m.objList.Btn_AD2.gameObject:SetActive(true)
		end
	end
end

--------------------------------------------------------------------
-- 添加红点
--------------------------------------------------------------------
function m.AddRedDot()
	m:SetWndRedDot(m.objList.Btn_AD2):AddCheckParam(WndID.FirstRecharge)
end

return m
