--[[
********************************************************************
    created:    2023/08/13
    author :    李锦剑
    purpose:    好友邀请界面
*********************************************************************
--]]

local luaID = ('UIInvitation')
local m = {}

function m.GetOpenEventList()
    return {
        [EventID.UpdateInviteDataList] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
    }
end

function m.OnCreate()
    m.GiftBagList = {}
    m.CharacterList = {}
    m.catFriendList = {}

    m.objList.InviteToIntroduce.gameObject:SetActive(false)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Close1.onClick:AddListenerEx(function()
        m.objList.InviteToIntroduce.gameObject:SetActive(false)
    end)
    m.objList.Btn_Award.onClick:AddListenerEx(function()
        UIManager:OpenWnd(WndID.ADFree)
    end)

    m.objList.Btn_Copy.onClick:AddListenerEx(m.OnClickCopy)
    m.objList.Btn_Share.onClick:AddListenerEx(m.OnClickShare)
end

--------------------------------------------------------------------
--创建礼包框
--------------------------------------------------------------------
function m.CreateGiftBag(index)
    local item = {}
    item.awardList = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_GiftBag, m.objList.Item_GiftBag)
    item.com.Btn_Buy.onClick:AddListenerEx(function()
        HelperL.Recharge(item.card.ID)
    end)
    item.com.Btn_BuyGrey.onClick:AddListenerEx(function()
        HelperL.Recharge(item.card.ID)
    end)
    item.com.Btn_AD.onClick:AddListenerEx(function()
        m.UpdateInviteInterface(item.data)
    end)
    item.com.Btn_Get.onClick:AddListenerEx(function()
        m.GetAward(item.data)
    end)

    item.UpdateView = function(data)
        if data then
            item.data = data
            item.card = Schemes.RechargeCard:Get(data.RechargeCardID)
            item.com.Img_Discount.gameObject:SetActive(item.card.Pic1 ~= '0')
            item.com.Txt_Discount.text = string.gsub(item.card.Pic1, '<P><P>', '\n')
            item.com.Txt_Title.text = data.Desc
            item.com.Txt_Buy.text = "￥" .. (item.card.FirstRMB / 100)
            item.com.Txt_BuyGrey.text = "￥" .. (item.card.FirstRMB / 100)

            --创建奖励物品
            local prizeList = Schemes.PrizeTable:GetGoodsList(item.card.PrizeID)
            if prizeList then
                local num = math.max(#item.awardList, #prizeList)
                for i = 1, num do
                    if not item.awardList[i] then
                        item.awardList[i] = m.CreateGoods(item.com.Grid_Goods)
                    end
                    item.awardList[i].UpdateView(prizeList[i])
                end
                item.com.Grid_Goods.gameObject:SetActive(true)
            else
                item.com.Grid_Goods.gameObject:SetActive(false)
            end

            local isBuyCard = HelperL.HadBoughtCardID(item.card.ID)
            item.com.Btn_Buy.gameObject:SetActive(false)
            item.com.Btn_BuyGrey.gameObject:SetActive(false)
            if SWITCH.RECHARGE then
                item.com.Btn_Buy.gameObject:SetActive(not isBuyCard)
            else
                -- item.com.Btn_BuyGrey.gameObject:SetActive(true)
            end

            item.com.Btn_AD.gameObject:SetActive(false)
            item.com.Btn_Get.gameObject:SetActive(false)
            item.com.Btn_IsGet.gameObject:SetActive(false)

            local num = HelperL.InviteNum(item.data.InviteLevel)
            local invite = item.data.Invite
            item.com.Txt_AD.text = string.format(GetGameText(luaID, 3),
                num >= invite and '#00FF28' or '#ED2F30',
                num, invite)
            local lv = HeroDataManager:GetLogicBit(data.Save1, data.Save2)
            if lv == 1 then
                item.com.Btn_IsGet.gameObject:SetActive(true)
            else
                if num >= invite then
                    item.com.Btn_Get.gameObject:SetActive(true)
                else
                    item.com.Btn_AD.gameObject:SetActive(true)
                end
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.catFriendList = {}
    local typeList = Schemes.CatFriend:GetByTypeList()
    --筛选未完成的，好友邀请任务
    for k, list in pairs(typeList) do
        for i, v in ipairs(list) do
            if HeroDataManager:GetLogicBit(v.Save1, v.Save2) == 0 then
                table.insert(m.catFriendList, v)
                break
            end
        end
    end

    local num = math.max(#m.catFriendList, #m.GiftBagList)
    for i = 1, num, 1 do
        if not m.GiftBagList[i] then
            m.GiftBagList[i] = m.CreateGiftBag(i)
        end
        m.GiftBagList[i].UpdateView(m.catFriendList[i])
    end
end

--------------------------------------------------------------------
--更新邀请界面
--------------------------------------------------------------------
function m.UpdateInviteInterface(date)
    local num = math.max(#m.CharacterList, #EntityModule.InviteDataList)
    for i = 1, num, 1 do
        if not m.CharacterList[i] then
            m.CharacterList[i] = m.CreateCharacter(i)
        end
        m.CharacterList[i].UpdateData(EntityModule.InviteDataList[i])
    end

    local num2 = HelperL.InviteNum(date.InviteLevel)
    m.objList.Txt_Describe1.text = string.format(GetGameText(luaID, 4),
        date.Text,
        num2 >= date.Invite and '#00FF28' or '#ED2F30',
        num2, date.Invite)

    m.objList.Txt_Describe2.text = string.format(GetGameText(luaID, 1), EntityModule.GetInvitationCode())
    m.objList.InviteToIntroduce.gameObject:SetActive(true)
end

--------------------------------------------------------------------
--创建邀请角色列表
--------------------------------------------------------------------
function m.CreateCharacter(index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Character, m.objList.Item_Character)
    item.UpdateData = function(data)
        if data then
            item.com.Txt_Name.text = data.ActorName
            item.com.Txt_Level.text = data.ActorLevel
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--分享邀请码
--------------------------------------------------------------------
function m.OnClickShare()
    --调用SDK
end

--------------------------------------------------------------------
--复制邀请码
--------------------------------------------------------------------
function m.OnClickCopy()
    --调用SDK
end

--------------------------------------------------------------------
--创建物品框
--------------------------------------------------------------------
function m.CreateGoods(parent)
    local item = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Goods)
    item.UpdateView = function(data)
        if data then
            if not item.goodsItem then
                item.goodsItem = CreateSingleGoods(item.com.gameObject)
            end
            item.goodsItem:SetItemData(data.id, data.num)
            item.goodsItem:SetSize(140, 140)
            item.goodsItem:SetShowName(false)
            item.goodsItem:SetShowNum(true)
            item.goodsItem:SetVisible(true)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--获取奖励
--------------------------------------------------------------------
function m.GetAward(data)
    local lv = HeroDataManager:GetLogicBit(data.Save1, data.Save2)
    if lv ~= 0 then
        HelperL.ShowMessage(TipType.FlowText, CommonTextID.IS_GET)
        return
    end
    LuaModule.RunLuaRequest(string.format('LuaRequestInviteFriendAward?ID=%d', data.ID),
        function(resultCode, content)
            if resultCode ~= RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
            else
                m.UpdateView()
            end
        end
    )
end

return m
