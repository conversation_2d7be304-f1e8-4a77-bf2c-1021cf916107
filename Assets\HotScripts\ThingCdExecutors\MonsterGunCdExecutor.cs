﻿using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

namespace ThingCdExecutors
{
    /// <summary>
    ///     怪物阵营的枪的执行器
    /// </summary>
    public class MonsterGunCdExecutor : GunCdExecutor
    {
        /// <summary>
        ///     枪属于哪个怪物
        /// </summary>
        public MonsterThing MonsterThing => GunThing.Owner as MonsterThing;

        /// <summary>
        ///     获取玩家角色(数据)
        /// </summary>
        protected ActorThing Actor => SingletonMgr.Instance.BattleMgr.Actor;

        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // V30.1-动画系统修复 怪物发射子弹时播放攻击动画attack01
            if (MonsterThing?.Monster != null)
            {
                Debug.Log($"V30.1 MonsterGunCdExecutor 怪物 {MonsterThing.Monster.gameObject.name} 发射子弹，播放攻击动画 attack01");
                MonsterThing.Monster.PlayAnimation("attack01", false, true);
            }

            // 调用基类的DoShoot方法
            base.DoShoot(token).Forget();
        }

        /// <summary>
        ///     射击一次后(按cd时长的一次循环)
        /// </summary>
        protected override void OnAfterShoot()
        {
            // _ = MonsterThing.AiEventList.Where(x => x.CanSuicide).Select(x =>
            // {
            //     x.ShotTimes.Value++;
            //     
            //     // 怪物自杀(延时)
            //     MessageBroker.Default.Publish(new ThingDead { Thing = MonsterThing });
            //     return x;
            // }).ToList();
        }
    }
}