--[[
********************************************************************
    created:    2024/03/11
    author :    李锦剑
    purpose:    赠送体力
*********************************************************************
--]]

local luaID = ('UIGivePhysicalPower')
local adID = 116
---@class UIGivePhysicalPower:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.Get<PERSON>pen<PERSON>List()
    return {
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()

end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Txt_QuickDesc.text = GetGameText(luaID, 5)
    m.goodsItemList = {}
    local commonText = Schemes.CommonText:Get(adID)
    print('------commonText------广告-----', commonText.AddGoods)
    local goodsList = m.GetGoodsList(commonText.AddGoods)

    if goodsList then
        local num = math.max(#m.goodsItemList, #goodsList)
        for i = 1, num, 1 do
            if not m.goodsItemList[i] then
                m.goodsItemList[i] = m.CreateSingleGoods(m.objList.Grid_Goods)
            end
            m.goodsItemList[i].UpdateData(goodsList[i])
        end
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
    m.objList.Btn_Get.onClick:AddListenerEx(function()
        local commonText = Schemes.CommonText:Get(adID)
        --判断今日次数
        local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
        if num >= commonText.DayTime then
            HelperL.ShowMessage(TipType.FlowText, GetGameText(('UIOpenTreasureBox'), 7))
            return
        end
        AdvertisementManager.GetAdAward(adID, m.RequestDirectGiveGoodsy)
        m.OnClickClose()
    end)
end

--------------------------------------------------------------------
--发放奖励
--------------------------------------------------------------------
function m.RequestDirectGiveGoodsy(bool, adID)
    if bool then
        local commonText = Schemes.CommonText:Get(adID)
        HelperL.RequestDirectGiveGoodsy(commonText.AddGoods, commonText.DeductGoods, function(resultCode, content)
            if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                -- HelperL.DisplayReward(PrizeContentType.STRING3, commonText.AddGoods)
                -- m.UpdateView()
            else
                ResultCode:DefaultShowResultCode(resultCode, content)
            end
        end)
    end
end

--------------------------------------------------------------------
-- 创建物品
--------------------------------------------------------------------
function m.CreateSingleGoods(parent)
    local item = {}
    item.goods = CreateSingleGoods(parent)
    item.UpdateData = function(data)
        if data then
            item.goods:SetItemData(data.id, data.num)
            item.goods:SetSize(140, 140)
            item.goods:SetShowName(false)
            item.goods:SetShowNum(true)
            item.goods:SetVisible(true)
        else
            item.goods:SetVisible(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 获取物品列表
--------------------------------------------------------------------
function m.GetGoodsList(content)
    local goodsList = {}
    local strList = HelperL.Split(content, "|")
    local tempList
    for i, v in ipairs(strList) do
        tempList = HelperL.Split(v, ";")
        table.insert(goodsList, {
            id = tonumber(tempList[1]),
            num = tonumber(tempList[2]),
        })
    end
    return goodsList
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.objList.Btn_Get.gameObject:SetActive(false)
    local commonText = Schemes.CommonText:Get(adID)
    --判断今日次数
    local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    if num < commonText.DayTime then
        local condition = HelperL.LunchPhase()
        if num < condition then
            m.objList.Btn_Get.gameObject:SetActive(true)
        end
    end
    m.objList.Txt_Title.text = GetGameText(luaID, num + 1)
end

--------------------------------------------------------------------
--关闭界面
--------------------------------------------------------------------
function m.OnClickClose()
    m:CloseSelf()
end

return m
