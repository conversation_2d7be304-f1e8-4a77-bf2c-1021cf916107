--[[
********************************************************************
    created:	2024/05/17
    author :	李锦剑
    purpose:    体魄界面
*********************************************************************
--]]

local luaID = 'UIEquipWeaponInfo'

---@class UIEquipWeaponInfo:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    m.selectBulletLevel = 1
    ---属性框
    ---@type Item_Attribute[]
    m.Item_Attribute_List = {}
    ---属性框
    ---@type Item_Attribute4[]
    m.Item_Attribute_List2 = {}

    ---按钮
    ---@type Item_Tab[]
    m.Item_Tab_List = {}

    m.RegisterClickEvent()

    m.equipID = EquipWeaponInfo_EQUIP_ID
    if not m.equipID then return end
    local equipWeapon = Schemes.EquipWeapon:Get(m.equipID)
    --升星id
    m.SmeltID_List = {}

    local SmeltID
    local strs = HelperL.Split(equipWeapon.ActiveDesc1, ";")
    for i, v in ipairs(strs) do
        SmeltID = tonumber(v) or 0
        if SmeltID > 0 and SmeltID ~= 20001 then
            print("SmeltID--------------",SmeltID)
            table.insert(m.SmeltID_List, SmeltID)
        end
    end
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(equipID)
    -- m.equipID = equipID
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Upgrade, function()
        local SmeltID = m.SmeltID_List[m.selectIndex or 1]
        print("SmeltID---RegisterClickEvent-----------",SmeltID)
        m.Upgrade(SmeltID)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local equipment = Schemes.Equipment:Get(m.equipID)
    local SmeltID = tonumber(m.SmeltID_List[m.selectIndex or 1]) or 0
    if SmeltID == 0 then
        error('EquipSmeltStar is nil, SmeltID=' .. SmeltID)
        return
    end
    -- local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(SmeltID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(SmeltID)
    print("level---RegisterClickEvent-----------",level)
    -- local exp = GamePlayerData.ActorEquipNew:GetEquipUpgradeEXP(SmeltID)
    local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(SmeltID, level)
    if not equipSme then
        error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
        return
    end

    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(m.equipID), m.objList.Img_Icon, true)

    -------------------------基础属性-----------------------------------
    local attributeList = {}
    local attack = 0
    local hp = 0
    if equipment.QualityLevel < equipSme.AttackList.Length then
        attack = attack + equipSme.AttackList[equipment.QualityLevel]
    end
    if equipment.QualityLevel < equipSme.MaxHpList.Length then
        hp = hp + equipSme.MaxHpList[equipment.QualityLevel]
    end
    --攻击
    table.insert(attributeList,
        { icon = 'AttributeIcon03', name = GetGameText(luaID, 8), describe = attack })
    --生命
    table.insert(attributeList,
        { icon = 'AttributeIcon3', name = GetGameText(luaID, 7), describe = hp })

    local num = math.max(#m.Item_Attribute_List, #attributeList)
    for i = 1, num, 1 do
        if not m.Item_Attribute_List[i] then
            m.Item_Attribute_List[i] = m.Creation_Item_Attribute(i)
        end
        m.Item_Attribute_List[i].UpdateData(attributeList[i])
    end

    -------------------------按钮-----------------------------------
    local num2 = math.max(#m.Item_Tab_List, #m.SmeltID_List)
    for i = 1, 3, 1 do
        if not m.Item_Tab_List[i] then
            m.Item_Tab_List[i] = m.Creation_Item_Tab(i)
        end
        m.Item_Tab_List[i].UpdateData(m.SmeltID_List[i])
    end

    m.ShowBulletInfo(m.selectIndex or 1)
end

--------------------------------------------------------------------
---创建属性框
---@param index integer
---@return Item_Attribute
--------------------------------------------------------------------
function m.Creation_Item_Attribute(index)
    ---@class Item_Attribute
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Attribute, m.objList.Item_Attribute)
    ---属性数据--图片、名字、描述
    ---@param data {icon:string, name:string, describe:string}
    item.UpdateData = function(data)
        item.data = data
        if data then
            item.com.Txt_Value.text = data.name .. data.describe
            AtlasManager:AsyncGetSprite(data.icon, item.com.Img_Icon, true)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---创建属性框
---@param index integer
---@return Item_Attribute4
--------------------------------------------------------------------
function m.Creation_Item_Attribute2(index)
    ---@class Item_Attribute4
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Attribute2, m.objList.Item_Attribute2)
    ---属性数据--图片、名字、描述
    ---@param equipSme EquipSmeltStarCfg
    item.UpdateData = function(equipSme)
        item.equipSme = equipSme
        if equipSme then
            item.com.Txt_Level.text = string.format(GetGameText(luaID, 19), equipSme.StarLvl)
            item.com.Txt_Value.text = equipSme.Remark
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(equipSme.SmeltID)
            item.com.Img_Lock.gameObject:SetActive(level <= equipSme.StarLvl)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---创建按钮
---@param index integer
---@return Item_Tab
--------------------------------------------------------------------
function m.Creation_Item_Tab(index)
    ---@class Item_Tab
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
    m:AddClick(item.com.Btn_Click, function()
        m.ShowBulletInfo(item.index)
    end)

    item.com.Img_Bg.gameObject:SetActive(true)
    item.com.Img_Select.gameObject:SetActive(false)

    --- 更新数据
    ---@param SmeltID integer 升星ID
    item.UpdateData = function(SmeltID)
        item.SmeltID = SmeltID
        local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(SmeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(SmeltID)
        -- local exp = GamePlayerData.ActorEquipNew:GetEquipUpgradeEXP(SmeltID)
        local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(SmeltID, level)
        if not equipSme then
            error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
            return
        end
        item.com.Txt_Name1.text = equipSme.Name
        item.com.Txt_Name2.text = equipSme.Name

        item.com.Img_RedDot.gameObject:SetActive(false)
        if level < maxLevel then
            local bool1 = not HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
            local bool2 = not HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID1Num, false, false)
            if bool1 and bool2 then
                item.com.Img_RedDot.gameObject:SetActive(true)
            end
        end
    end
    return item
end

function m.ShowAttribute()

end

--------------------------------------------------------------------
--显示子弹信息
---@param index integer
--------------------------------------------------------------------
function m.ShowBulletInfo(index)
    local item
    if m.selectIndex then
        item = m.Item_Tab_List[m.selectIndex]
        item.com.Img_Bg.gameObject:SetActive(true)
        item.com.Img_Select.gameObject:SetActive(false)
    end
    item = m.Item_Tab_List[index]
    item.com.Img_Bg.gameObject:SetActive(false)
    item.com.Img_Select.gameObject:SetActive(true)

    m.selectIndex = index
    print("m.selectIndex ====== ",m.selectIndex)
    local SmeltID = m.SmeltID_List[index]
    print("SmeltID ====== ",SmeltID)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(SmeltID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(SmeltID)
    print("maxLevel ====== ",maxLevel)
    print("level ====== ",level)
    -- local exp = GamePlayerData.ActorEquipNew:GetEquipUpgradeEXP(SmeltID)
    local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(SmeltID, level)
    if not equipSme then
        error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
        return
    end
    print('SmeltID=' .. SmeltID .. ' equipSme.Icon=' .. equipSme.Icon)
    --设置默认状态
    m.objList.Txt_Hint.gameObject:SetActive(false)
    m.objList.Btn_Upgrade.gameObject:SetActive(false)
    m.objList.Img_Gray.gameObject:SetActive(false)
    m.objList.Img_UpgradeRedDot.gameObject:SetActive(false)
    AtlasManager:AsyncGetSprite(equipSme.Icon, m.objList.Img_Icon)
    if level < maxLevel then
        m.objList.Btn_Upgrade.gameObject:SetActive(true)
        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Img_UpgradeExpend)
        local bool1 = not HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
        m.objList.Txt_UpgradeExpend.text = string.format("<color=%s>%s</color>/%s",
            bool1 and UI_COLOR.Red or UI_COLOR.Red,
            HelperL.GetChangeNum(SkepModule:GetGoodsCount(equipSme.CostGoodsID1)),
            HelperL.GetChangeNum(equipSme.CostGoodsID1Num))

        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID2, m.objList.Img_UpgradeExpend2)
        local bool2 = not HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID1Num, false, false)
        m.objList.Txt_UpgradeExpend2.text = string.format("%s <color=%s>%s</color>/%s",
            HelperL.GetGoodsName(equipSme.CostGoodsID2),
            bool2 and UI_COLOR.Red or UI_COLOR.Red,
            HelperL.GetChangeNum(SkepModule:GetGoodsCount(equipSme.CostGoodsID2)),
            HelperL.GetChangeNum(equipSme.CostGoodsID2Num))

        if bool1 and bool2 then
            m.objList.Img_UpgradeRedDot.gameObject:SetActive(true)
        else
            m.objList.Img_Gray.gameObject:SetActive(true)
        end
        local list = HelperL.Split( equipSme.Remark,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end
        m.objList.Img_UpgradeExpend2.gameObject:SetActive(true)
        m.objList.Txt_UpgradeExpend2.gameObject:SetActive(true)
        m.objList.Txt_Desc.text = str
    else
        m.objList.Txt_Hint.text = CommonTextID.IS_FULL_LEVEL
        m.objList.Txt_Hint.gameObject:SetActive(true)
        m.objList.Img_UpgradeExpend2.gameObject:SetActive(false)
        m.objList.Txt_UpgradeExpend2.gameObject:SetActive(false)
    end
    m.objList.Txt_Fill.text = level .. "/" .. maxLevel
    m.objList.Img_Fill.fillAmount = level / maxLevel
    m.objList.Txt_Lv.text = string.format(GetGameText(luaID, 19), level)
    

    local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(SmeltID) or {}
    for i = 2, #equipSmeltList, 1 do
        if not m.Item_Attribute_List2[i] then
            m.Item_Attribute_List2[i] = m.Creation_Item_Attribute2(i)
        end
        m.Item_Attribute_List2[i].UpdateData(equipSmeltList[i])
    end
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        -- m.UpdateView()
        --播放升级特效
        HelperL.PlayVFX()
        SoundManager:PlaySound(SoundID.Upgrade)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        HelperL.ShowMessage(TipType.FlowText, "点太快了1秒后尝试！")
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

return m
