﻿using System;
using System.Linq;

using Apq.ChangeBubbling;

using Thing;

using UnityEngine;

namespace View
{
	public class PlayerActor : CreatureBase
	{
		/// <summary>
		/// 玩家角色物件(数据)
		/// </summary>
		public ActorThing ActorThing => Thing as ActorThing;
        /// <summary>
        /// 角色移动组件
        /// </summary>
        public PlayerMove PlayerMove { get; set; }

		public override void Start()
		{
            HpBar.bar.gameObject.SetActive(true);
            ActorThing.Hp.Changed += Hp_Changed;
            ActorThing.Armor.Changed += Armor_Changed;
        }

        public override void DoSyncToThing()
        {
            base.DoSyncToThing();
            SetSpineSortingOrderBySelfPosition();
            HpBar.SetDisplayHealth((float)(ActorThing.Hp.Value / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
            HpBar.SetDisplayArmorBar((float)(ActorThing.Armor.Value / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
        }

        private void OnDisable()
        {
            ActorThing.Hp.Changed -= Hp_Changed;
            ActorThing.Armor.Changed -= Armor_Changed;
        }

        private void Armor_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is double newValue)
            {
                HpBar.SetDisplayArmorBar((float)(newValue / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
            }
        }

        private void Hp_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is double newValue && e.OriginalValue is double oldValue)
            {
                HpBar.SetDisplayHealth((float)(newValue / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
                if (newValue > oldValue)
                {
                    //回飘字
                    HudMgr.Instance.SpwanDamageHud(hudComp =>
                    {
                        hudComp.Init(transform.position + Vector3.up * -10);
                        hudComp.SetHpRecoverNumber(Mathf.CeilToInt((float)newValue - (float)oldValue));
                    }).Forget();
                }
            }
        }

        /// <summary>
        /// 获取或设置是否拒绝移动
        /// </summary>
        public bool DenyMove
        {
            get => PlayerMove.DenyMove;
            set => PlayerMove.DenyMove = value;
        }

        //public void PerformDash()
        //{
        //	if (PlayerPrefs.GetInt("DashTutorial2") == 1)
        //	{
        //		SingletonMgr.Instance.BattleMgr.TimeManager.SetTimescale(1.0f);
        //		//TODO Mobile and Console Controls
        //		//InputController.instance.dashButtonGlow.SetActive(false);
        //		//InputController.instance.gameObject.SetActive(false);
        //		PlayerPrefs.SetInt("DashTutorial2", 0);
        //	}
        //}

        public override void Update()
        {
            base.Update();
            SetSpineSortingOrderBySelfPosition();
        }
    }

}
