using System.Collections.Generic;

using Apq.Extension;
using Apq.Unity3D.Extension;

using Cysharp.Threading.Tasks;

using Thing;

using UnityEngine;

namespace View
{
    /// <summary>
    /// 子弹池
    /// </summary>
    [DisallowMultipleComponent]
    public class BulletPool : MonoBehaviour
    {
        /// <summary>
        /// 字典:子弹类型-根节点
        /// </summary>
        public Dictionary<System.Type, GameObject> DicBullets { get; } = new();

        /// <summary>
        /// 从子弹池中获取一个子弹(或新建)
        /// </summary>
        /// <remarks>只能获取到没有数据的子弹界面</remarks>
        public async UniTask<BulletBase> GetOrCreateFromPool(System.Type bulletCls, BulletThing bulletThing)
        {
            var objName = bulletCls.GetFriendlyName();
            if (!DicBullets.TryGetValue(bulletCls, out var childRoot))
            {
                childRoot = new(objName) { transform = { parent = transform, }, };
                DicBullets.Add(bulletCls, childRoot);
            }

            var bulletObj = childRoot.transform.GetOrAddChildGameObject(objName, gObj =>
            {
                var bullet = gObj.GetComponent(bulletCls) as BulletBase;
                return bullet && bullet.Thing == null;
            });

            var bullet = bulletObj.GetOrAddComponent(bulletCls) as BulletBase;
            if (bullet)
            {
                // 数据与界面双向关联
                bullet.Thing = bulletThing;
                bulletThing.ThingBehaviour = bullet;
                
                // 子弹根级可见
                bullet.gameObject.SetActive(true);
                
                // 先隐藏轨迹和图片
                bullet.LocusRoot.SetActive(false);
                bullet.ImgElement.SetActive(false);

                await bullet.Init();
            }

            return bullet;
        }

        /// <summary>
        /// 从子弹池中获取一个子弹(或新建)
        /// </summary>
        /// <remarks>只能获取到没有数据的子弹界面</remarks>
        public async UniTask<T> GetOrCreateFromPool<T>(BulletThing bulletThing) where T : BulletBase
        {
            var type = typeof(T);
                var objName = type.GetFriendlyName();
            if (!DicBullets.TryGetValue(type, out var childRoot))
            {
                childRoot = new(objName) { transform = { parent = transform, }, };
                DicBullets.Add(type, childRoot);
            }

            var bulletObj = childRoot.transform.GetOrAddChildGameObject(objName, gObj =>
            {
                var bullet = gObj.GetComponent<T>();
                return bullet && bullet.Thing == null;
            });

            var bullet = bulletObj.GetOrAddComponent<T>();
            if (bullet)
            {
                // 数据与界面双向关联
                bullet.Thing = bulletThing;
                bulletThing.ThingBehaviour = bullet;
                
                // 子弹根级可见
                bullet.gameObject.SetActive(true);
                
                // 先隐藏轨迹和图片
                bullet.LocusRoot.SetActive(false);
                bullet.ImgElement.SetActive(false);

                await bullet.Init();
            }

            return bullet;
        }
    }
}