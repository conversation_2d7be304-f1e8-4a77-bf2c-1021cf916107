// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using HotScripts;

using RxEventsM2V;

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     玩家抛物线导弹(定点打击)
    /// </summary>
    public class ActorMissileTrackPos : ActorBulletBase
    {
        /// <summary>
        ///     轨迹生成器(包含发射器)
        /// </summary>
        public MissileLocusGenerator MissileLocusGenerator { get; set; }

        /// <summary>
        ///     导弹轨迹(关键点)
        /// </summary>
        public List<Vector3> Locus { get; set; }

        /// <summary>
        ///     是用上面还是下面的轨迹
        /// </summary>
        public bool LocusDown { get; set; }

        /// <summary>
        ///     固定的起始位置（发射时确定，不会改变）
        /// </summary>
        private Vector3 fixedStartPosition;

        /// <summary>
        ///     固定的目标位置（发射时确定，不会改变）
        /// </summary>
        private Vector3 fixedTargetPosition;

        /// <summary>
        ///     固定的飞行总时长（发射时计算，不会改变）
        /// </summary>
        private float fixedTotalDuration;

        /// <inheritdoc />
        public override async UniTask OnAfterInitView()
        {
            await base.OnAfterInitView();

            // V5.11彻底解决方案：在初始化时直接从玩家预制体获取固定位置，确保强关联
            if (SingletonMgr.Instance.BattleMgr.PlayerActor != null)
            {
                fixedStartPosition = SingletonMgr.Instance.BattleMgr.PlayerActor.transform.position;
                //Debug.Log($"55555555 枪械抛物线子弹从预制体获取起点位置 - 预制体位置:{fixedStartPosition}");
            }
            else
            {
                fixedStartPosition = MissileLocusGenerator.MissileEjector.transform.position;
                //Debug.Log($"55555555 枪械抛物线子弹备用起点位置 - 发射器位置:{fixedStartPosition}");
            }
            
            fixedTargetPosition = MissileLocusGenerator.TargetPosition!.Value;
            
            // 计算固定的飞行总时长
            float distance = Vector3.Distance(fixedStartPosition, fixedTargetPosition);
            float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            fixedTotalDuration = distance / speed;

            // V62.0调试日志：确认轨迹固定化状态
          //   Debug.Log($"V62.0 枪械抛物线子弹初始化完成 - 起点:{fixedStartPosition} 终点:{fixedTargetPosition} 飞行时长:{fixedTotalDuration}秒 子弹速度:{speed}");

            float bulletLocusDuration = (float)GunThing.GetTotalDouble(PropType.BulletLocusDuration).FirstOrDefault();
           //  Debug.Log($"V62.0 落点预示时长: {bulletLocusDuration}秒");
            
            if (bulletLocusDuration > 0)
            {
              //   Debug.Log($"V62.0 显示落点预示特效 - 位置:{fixedTargetPosition} 时长:{bulletLocusDuration}秒");
                
                // 落点预示
                EffectMgr.Instance.ShowEffect(EffectPath.BornCircle,
                    fixedTargetPosition, 2, null, bulletLocusDuration).Forget();

                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration));
              //   Debug.Log($"V62.0 落点预示延时结束，子弹开始移动");
            }
        }

        #region 爆炸

        /// <summary>
        ///     爆炸
        /// </summary>
        public virtual async UniTaskVoid DoExplose(CancellationToken token)
        {
            try
            {
                await UniTask.SwitchToMainThread();

              //   Debug.Log($"V62.0 ===== ActorMissileTrackPos.DoExplose 抛物线到达目标 =====");
              //   Debug.Log($"V62.0 子弹最终位置: {transform.position}");
              //   Debug.Log($"V62.0 目标位置: {fixedTargetPosition}");

                ThingBase thing = BulletThing.CdExecutor.Thing;
                Vector3 explosionPos = fixedTargetPosition;

                // 记录爆炸属性日志
                LogExplosionProperties(thing, new List<HitThingCells>()); // 空敌人列表，因为是到达目标点爆炸

                // 检查爆炸概率
                double explosePriority = thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();
              //   Debug.Log($"V62.0 DoExplose 爆炸概率配置: {explosePriority} ({explosePriority * 100:F1}%)");
                
                if (explosePriority <= 0) 
                {
                  //   Debug.Log($"V62.0 ❌ DoExplose 爆炸概率为0，不触发爆炸");
                    TurnToPool().Forget();
                    return;
                }

                // 随机检查是否触发爆炸
                float randomValue = UnityEngine.Random.Range(0f, 1f);
              //   Debug.Log($"V62.0 DoExplose 随机值: {randomValue:F3}, 需要小于等于: {explosePriority:F3}");
                
                if (randomValue > explosePriority) 
                {
                  //   Debug.Log($"V62.0 ❌ DoExplose 随机检查失败，不触发爆炸 ({randomValue:F3} > {explosePriority:F3})");
                    TurnToPool().Forget();
                    return;
                }

             //    Debug.Log($"V62.0 ✅ DoExplose 随机检查通过，触发到达目标爆炸");

                // 使用完整的V62.0爆炸系统
                ProcessExplosionAsync(thing, explosionPos, 1).Forget();

                // 界面还给子弹池
                TurnToPool().Forget();
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        #endregion

        #region 移动

        /// <summary>
        ///     任务实现:按轨迹移动
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
              //   Debug.Log($"V62.0 ===== ActorMissileTrackPos.DoTask_Move 开始 =====");
              //   Debug.Log($"V62.0 起始位置: {fixedStartPosition}");
               //  Debug.Log($"V62.0 目标位置: {fixedTargetPosition}");
              //   Debug.Log($"V62.0 总飞行时长: {fixedTotalDuration}秒");
                
                float duration = 0.03f;
                
                // 显示子弹图片
                ImgElement.SetActive(true);

                for (;; await UniTask.Delay(TimeSpan.FromSeconds(duration), cancellationToken: token))
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        // 使用固定的起始和目标位置计算轨迹
                        if (MoveOne_PositionMove(duration, fixedTotalDuration, null))
                        {
                   //          Debug.Log($"V62.0 抛物线运动完成，准备触发DoExplose - 当前位置: {transform.position}");
                            DoExplose(token); // 抛物线运动完成时触发爆炸
                            return;
                        }
                    }
                    catch (OperationCanceledException) { throw; }
                    catch (MissingReferenceException)
                    {
                        return;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogException(ex);
                        return;
                    }
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        ///     移动一次。传送至抛物线轨迹中按时间进度对应的点
        /// </summary>
        /// <returns>是否已走完整个轨迹</returns>
        public virtual bool MoveOne_PositionMove(float duration, float totalDuration, CubicBezierPath cubicBezier)
        {
            if (totalDuration <= 0)
            {
                return true;
            }

            // 移动计时
            MoveDuration.Value += duration;

            // 时间进度
            float progress = Mathf.Clamp01(MoveDuration.Value / totalDuration);

            // V62.0 - 关键进度点日志
            if (progress >= 0.5f && progress < 0.6f && MoveDuration.Value > 0)
            {
              //   Debug.Log($"V62.0 抛物线进度50% - 当前位置: {transform.position}");
            }
            else if (progress >= 0.9f && progress < 0.95f)
            {
               //  Debug.Log($"V62.0 抛物线进度90% - 当前位置: {transform.position}, 即将到达目标");
            }

            #region 导弹前进一次(抛物线轨迹)

            // 起点和终点位置
            Vector3 startPos = fixedStartPosition;
            Vector3 endPos = fixedTargetPosition;
            
            // 计算抛物线的水平距离
            float horizontalDistance = Vector3.Distance(new Vector3(startPos.x, 0, startPos.z), new Vector3(endPos.x, 0, endPos.z));
            
            // 抛物线高度系数，显著增加高度让抛物线更明显
            float arcHeight = horizontalDistance * 1.2f; // 大幅增加抛物线最高点高度
            
            // X和Z坐标：根据时间进度线性插值（匀速变化）
            Vector3 currentPos = Vector3.Lerp(startPos, endPos, progress);
            
            // Y坐标：在线性插值基础上加上抛物线高度
            // 增强抛物线公式：y = 4h * t * (1-t) * (1 + 0.3*sin(π*t))
            float basicParabola = 4 * arcHeight * progress * (1 - progress); // 基础抛物线
            float enhancedParabola = basicParabola * (1 + 0.3f * Mathf.Sin(Mathf.PI * progress)); // 添加正弦波增强弧度
            currentPos.y += enhancedParabola; // 在线性插值Y坐标基础上加上抛物线高度
            
            // 增强抛物线视觉效果：更明显的缩放变化
            // 在抛物线最高点时显著放大，起点和终点时缩小
            float scaleProgress = 1 - Mathf.Abs(progress - 0.5f) * 2; // 0到1的缩放进度
            float minScale = 0.6f; // 减小最小缩放，让对比更明显
            float maxScale = 3.0f; // 增加最大缩放，让最高点更突出
            float currentScale = Mathf.Lerp(minScale, maxScale, Mathf.Pow(scaleProgress, 0.5f)); // 使用幂函数让缩放变化更平滑
            
            // 应用缩放到子弹图片
            if (ImgElement != null)
            {
                ImgElement.transform.localScale = Vector3.one * currentScale;
            }
            
            // 计算导弹朝向（切线方向）- 基于增强的抛物线导数
            Vector3 direction = endPos - startPos;
            // 增强抛物线的导数：考虑正弦波影响
            float parabolaDerivative = 6 * arcHeight * (1 - 2 * progress) * (1 + 0.3f * Mathf.Sin(Mathf.PI * progress));
            parabolaDerivative += 6 * arcHeight * progress * (1 - progress) * 0.3f * Mathf.PI * Mathf.Cos(Mathf.PI * progress);
            
            Vector3 tangent = direction.normalized;
            if (horizontalDistance > 0)
            {
                tangent.y = parabolaDerivative / horizontalDistance; // 调整y方向的切线
            }
            
            // 设置导弹位置和朝向
            transform.position = currentPos;
            transform.right = tangent.normalized;

            #endregion

            // 时间进度达到100%则完成轨迹移动
            return progress >= 1.0f;
        }

        /// <summary>
        ///     击中敌人后怎么办
        /// </summary>
        /// <param name="line">移动的线段</param>
        /// <param name="thing">发出子弹的物件</param>
        /// <param name="enemies">击中的敌人(及其格子)</param>
        protected override void OnHitEnemy(LineSegment line, ThingBase thing, IList<HitThingCells> enemies)
        {
            // V62.0 - 修复ShootMethod=11爆炸伤害缺失问题
            // 添加与ActorMissileTrackEnemy相同的爆炸逻辑
            try
            {
             //    Debug.Log($"V62.0 ===== ActorMissileTrackPos.OnHitEnemy 触发 =====");
              //   Debug.Log($"V62.0 子弹位置: {transform.position}");
              //   Debug.Log($"V62.0 移动线段: {line.PosStart} → {line.PosEnd}");
              //   Debug.Log($"V62.0 击中敌人数量: {enemies?.Count ?? 0}");
                
                if (enemies != null)
                {
                    for (int i = 0; i < enemies.Count; i++)
                    {
                        var enemy = enemies[i];
                   //      Debug.Log($"V62.0 敌人 #{i}: {enemy?.Thing?.GetType().Name} 位置:{enemy?.Thing?.Position}");
                    }
                }

                // 先调用基类的击中处理（伤害计算、音效播放等）
                base.OnHitEnemy(line, thing, enemies);

               //  Debug.Log($"V62.0 基类OnHitEnemy处理完成，开始爆炸逻辑");
                
                // 记录爆炸属性日志
                LogExplosionProperties(BulletThing.CdExecutor.Thing, enemies);
                
                // 导弹击中敌人后的爆炸处理
                DamageEnemyWithExplosion(line, BulletThing.CdExecutor.Thing, enemies);
                
                LogPostHitExplosionCheck(BulletThing.CdExecutor.Thing, enemies);
            }
            catch (Exception ex)
            {
              //   Debug.LogError($"V62.0 ActorMissileTrackPos.OnHitEnemy 异常: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// V62.0 - 爆炸伤害处理，复制自ActorMissileTrackEnemy
        /// </summary>
        private void DamageEnemyWithExplosion(LineSegment line, ThingBase thing, IList<HitThingCells> enemies)
        {
            try
            {
                Vector3 pos = enemies.FirstOrDefault()?.Thing?.Position ?? transform.position;
               //  Debug.Log($"V62.0 ===== 开始爆炸伤害检查 =====");
             //    Debug.Log($"V62.0 爆炸位置: {pos}");
              //   Debug.Log($"V62.0 击中敌人数量: {enemies.Count}");

                // 检查爆炸概率
                double explosePriority = thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();
              //   Debug.Log($"V62.0 爆炸概率配置: {explosePriority} ({explosePriority * 100:F1}%)");
                
                if (explosePriority <= 0) 
                {
                 //    Debug.Log($"V62.0 ❌ 爆炸概率为0，不触发爆炸");
                    return;
                }

                // 随机检查是否触发爆炸
                float randomValue = UnityEngine.Random.Range(0f, 1f);
             //    Debug.Log($"V62.0 随机值: {randomValue:F3}, 需要小于等于: {explosePriority:F3}");
                
                if (randomValue > explosePriority) 
                {
                //     Debug.Log($"V62.0 ❌ 随机检查失败，不触发爆炸 ({randomValue:F3} > {explosePriority:F3})");
                    return;
                }

               //  Debug.Log($"V62.0 ✅ 随机检查通过，触发击中爆炸 - 位置:{pos} 概率:{explosePriority}");

                // 异步处理爆炸，避免阻塞主线程
                ProcessExplosionAsync(thing, pos, 1).Forget();

                // 检查击杀爆炸
                bool anyEnemyKilled = enemies.Any(e => e.Thing is MonsterThing monster && monster.Hp.Value <= 0);
              //   Debug.Log($"V62.0 是否有敌人死亡: {anyEnemyKilled}");
                
                if (anyEnemyKilled)
                {
                    double killedExploseRadius = thing.GetTotalDouble(PropType.KilledExploseRadius).FirstOrDefault();
                 //    Debug.Log($"V62.0 击杀爆炸半径配置: {killedExploseRadius}");
                    
                    if (killedExploseRadius > float.Epsilon)
                    {
                   //      Debug.Log($"V62.0 ✅ 触发击杀爆炸 - 半径:{killedExploseRadius}");
                        ProcessExplosionAsync(thing, pos, 2).Forget();
                    }
                    else
                    {
                    //     Debug.Log($"V62.0 ❌ 击杀爆炸半径为0，不触发击杀爆炸");
                    }
                }
            }
            catch (Exception ex)
            {
               //  Debug.LogError($"V62.0 DamageEnemyWithExplosion异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V62.0 - 异步处理爆炸逻辑，复制自ActorMissileTrackEnemy
        /// </summary>
        private async UniTaskVoid ProcessExplosionAsync(ThingBase thing, Vector3 pos, int exploseType)
        {
            try
            {
                string exploseTypeName = exploseType == 1 ? "击中爆炸" : "击杀爆炸";
               //  Debug.Log($"V62.0 ===== 开始处理{exploseTypeName} =====");
              //   Debug.Log($"V62.0 爆炸中心位置: {pos}");

                // 获取爆炸参数
                double exploseRadius = exploseType == 1
                    ? thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault()
                    : thing.GetTotalDouble(PropType.KilledExploseRadius).FirstOrDefault();

               //  Debug.Log($"V62.0 {exploseTypeName}半径: {exploseRadius}");

                if (exploseRadius <= 0) 
                {
                    Debug.Log($"V62.0 ❌ {exploseTypeName}半径为0，取消爆炸");
                    return;
                }

                // 播放爆炸特效（不阻塞）
                string exploseEffect = exploseType == 1
                    ? thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault()
                    : thing.GetTotalString(PropType.KilledExploseEffect).FirstOrDefault();

              //   Debug.Log($"V62.0 {exploseTypeName}特效: '{exploseEffect}'");

                if (!string.IsNullOrWhiteSpace(exploseEffect))
                {
                    EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect),
                        pos, (float)exploseRadius).Forget();
                  //   Debug.Log($"V62.0 ✅ 播放{exploseTypeName}特效");
                }

                // 播放爆炸音效（不阻塞）
                string exploseSound = exploseType == 1
                    ? thing.GetTotalString(PropType.ExploseSound).FirstOrDefault()
                    : thing.GetTotalString(PropType.KilledExploseSound).FirstOrDefault();
                
             //    Debug.Log($"V62.0 {exploseTypeName}音效: '{exploseSound}'");

                if (!string.IsNullOrWhiteSpace(exploseSound))
                {
                    AudioPlayer.Instance.PlaySound(exploseSound).Forget();
                 //    Debug.Log($"V62.0 ✅ 播放{exploseTypeName}音效");
                }

                // 分帧处理爆炸范围内的怪物，避免卡顿
              //   Debug.Log($"V62.0 开始处理{exploseTypeName}范围伤害...");
                await ProcessExplosionDamageInFrames(thing, pos, (float)exploseRadius, exploseType);
            }
            catch (Exception ex)
            {
               //  Debug.LogError($"V62.0 ProcessExplosionAsync 异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V62.0 - 分帧处理爆炸伤害，复制自ActorMissileTrackEnemy
        /// </summary>
        private async UniTask ProcessExplosionDamageInFrames(ThingBase thing, Vector3 pos, float exploseRadius, int exploseType)
        {
            try
            {
                string exploseTypeName = exploseType == 1 ? "击中爆炸" : "击杀爆炸";
               //  Debug.Log($"V62.0 ===== 开始分帧处理{exploseTypeName}伤害 =====");

                // 获取爆炸范围内的怪物
                var monstersInRange = GetNearbyMonsters(pos, exploseRadius);
              //   Debug.Log($"V62.0 爆炸范围内找到怪物数量: {monstersInRange.Count}");
                
                // 获取爆炸系数
                float exploseCoe = (float)(exploseType == 1
                    ? thing.GetTotalDouble(PropType.ExploseCoe).FirstOrDefault()
                    : thing.GetTotalDouble(PropType.KilledExploseCoe).FirstOrDefault());

               //  Debug.Log($"V62.0 {exploseTypeName}伤害系数: {exploseCoe}");

                if (monstersInRange.Count == 0)
                {
               //      Debug.Log($"V62.0 ❌ 爆炸范围内没有怪物，无法造成伤害");
                    return;
                }

                // 分帧处理怪物，每帧最多处理3只怪物
                const int MONSTERS_PER_FRAME = 3;
                int totalDamagedMonsters = 0;
                double totalDamage = 0;
                
                for (int i = 0; i < monstersInRange.Count; i += MONSTERS_PER_FRAME)
                {
                    // 处理本帧的怪物
                    int endIndex = Mathf.Min(i + MONSTERS_PER_FRAME, monstersInRange.Count);
                    
                    for (int j = i; j < endIndex; j++)
                    {
                        var monster = monstersInRange[j];
                        if (monster == null || monster.Hp.Value <= 0) 
                        {
                       //      Debug.Log($"V62.0 跳过怪物 #{j} (null或已死亡)");
                            continue;
                        }

                        // 精确的爆炸范围检测
                        bool inRange = IsMonsterInExplosionRange(monster, pos, exploseRadius);
                      //   Debug.Log($"V62.0 怪物 #{j} ({monster.GetType().Name}) 是否在爆炸范围: {inRange}, 血量: {monster.Hp.Value}");

                        if (inRange)
                        {
                            // 计算爆炸伤害
                            (double damage, bool isCritical) = Helper.CalcDamage(BulletThing, 0, (byte)exploseType, monster);
                         //    Debug.Log($"V62.0 计算伤害: {damage} (暴击: {isCritical})");

                            // 受击方先接受枪携带的Buff
                            monster.ReceiveBuffByBulletHit(thing, BuffRecvType.Explose);

                            // 敌人接受伤害
                            double hpBefore = monster.Hp.Value;
                            monster.TakeHit(thing, damage, isCritical);
                            double hpAfter = monster.Hp.Value;

                       //      Debug.Log($"V62.0 ✅ 怪物受到{exploseTypeName}伤害 {damage}, 血量: {hpBefore} → {hpAfter}");
                            
                            totalDamagedMonsters++;
                            totalDamage += damage;
                        }
                        else
                        {
                            Debug.Log($"V62.0 ❌ 怪物 #{j} 不在精确爆炸范围内");
                        }
                    }

                    // 如果还有更多怪物要处理，等待一帧
                    if (endIndex < monstersInRange.Count)
                    {
                        await UniTask.Yield();
                    }
                }

               //  Debug.Log($"V62.0 ===== {exploseTypeName}伤害处理完成 =====");
              //   Debug.Log($"V62.0 受伤怪物数量: {totalDamagedMonsters}/{monstersInRange.Count}");
              //   Debug.Log($"V62.0 总伤害: {totalDamage:F1}");
            }
            catch (Exception ex)
            {
                // Debug.LogError($"V62.0 ProcessExplosionDamageInFrames 异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V62.0 - 获取附近的怪物，复制自ActorMissileTrackEnemy
        /// </summary>
        private List<MonsterThing> GetNearbyMonsters(Vector3 center, float radius)
        {
            List<MonsterThing> nearbyMonsters = new List<MonsterThing>();
            
            try
            {
               //  Debug.Log($"V62.0 开始搜索附近怪物 - 中心:{center}, 半径:{radius}");
                
                var allMonsters = SingletonMgr.Instance.BattleMgr.Monsters;
                if (allMonsters == null || allMonsters.Count == 0) 
                {
                    // Debug.Log($"V62.0 战场上没有怪物 (allMonsters为null或空)");
                    return nearbyMonsters;
                }
                
                Debug.Log($"V62.0 战场总怪物数量: {allMonsters.Count}");
                
                // 快速距离筛选，避免复杂的圆形碰撞计算
                float radiusSquared = radius * radius;
                int checkedCount = 0;
                int aliveCount = 0;
                
                foreach (var monster in allMonsters)
                {
                    checkedCount++;
                    
                    if (monster == null) 
                    {
                    //     Debug.Log($"V62.0 跳过null怪物 #{checkedCount}");
                        continue;
                    }
                    
                    if (monster.Hp.Value <= 0) 
                    {
                    //     Debug.Log($"V62.0 跳过死亡怪物 #{checkedCount} (血量:{monster.Hp.Value})");
                        continue;
                    }
                    
                    aliveCount++;
                    
                    // 快速距离检查（使用平方距离）
                    Vector3 diff = monster.Position - center;
                    float distanceSquared = diff.x * diff.x + diff.z * diff.z;
                    float actualDistance = Mathf.Sqrt(distanceSquared);
                    
                 //    Debug.Log($"V62.0 怪物 #{checkedCount} 位置:{monster.Position}, 距离:{actualDistance:F2}, 是否在范围内:{distanceSquared <= radiusSquared}");
                    
                    if (distanceSquared <= radiusSquared)
                    {
                        nearbyMonsters.Add(monster);
                        Debug.Log($"V62.0 ✅ 怪物 #{checkedCount} 加入爆炸范围列表");
                        
                        // 限制单次检测的怪物数量，避免卡顿
                        if (nearbyMonsters.Count >= 10)
                        {
                      //       Debug.Log($"V62.0 达到最大检测数量限制(10只)，停止搜索");
                            break;
                        }
                    }
                }
                
               //  Debug.Log($"V62.0 怪物搜索完成 - 检查总数:{checkedCount}, 存活数:{aliveCount}, 范围内数量:{nearbyMonsters.Count}");
            }
            catch (Exception ex)
            {
                // Debug.LogError($"V62.0 GetNearbyMonsters 异常: {ex.Message}");
            }
            
            return nearbyMonsters;
        }

        /// <summary>
        /// V62.0 - 检查怪物是否在爆炸范围内，复制自ActorMissileTrackEnemy
        /// </summary>
        private bool IsMonsterInExplosionRange(MonsterThing monster, Vector3 explosionCenter, float exploseRadius)
        {
            try
            {
                if (monster?.CircularArea2D == null) 
                {
                   //  Debug.Log($"V62.0 怪物CircularArea2D为null，返回false");
                    return false;
                }
                
                // 使用CircularArea2D进行精确检测
                var explosionArea = new CircularArea2D
                {
                    Center = explosionCenter,
                    Radius = exploseRadius
                };
                
                bool isIntersect = monster.CircularArea2D.IsIntersect(explosionArea);
                
              //   Debug.Log($"V62.0 精确范围检测 - 怪物中心:{monster.CircularArea2D.Center}, 怪物半径:{monster.CircularArea2D.Radius}, " +
                 //         $"爆炸中心:{explosionCenter}, 爆炸半径:{exploseRadius}, 结果:{isIntersect}");
                
                return isIntersect;
            }
            catch (Exception ex)
            {
                Debug.LogError($"V62.0 IsMonsterInExplosionRange 异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V62.0 - 记录爆炸属性日志，复制自ActorMissileTrackEnemy
        /// </summary>
        private void LogExplosionProperties(ThingBase thing, IList<HitThingCells> enemies)
        {
            try
            {
                // Debug.Log($"V62.0 ===== ActorMissileTrackPos 爆炸属性检查 =====");
                
                // 获取所有爆炸相关属性
                double explosePriority = thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();
                double exploseRadius = thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();
                double exploseCoe = thing.GetTotalDouble(PropType.ExploseCoe).FirstOrDefault();
                string exploseEffect = thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
                string exploseSound = thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
                
            //     Debug.Log($"V62.0 击中爆炸概率: {explosePriority} ({explosePriority * 100:F1}%)");
             //    Debug.Log($"V62.0 击中爆炸半径: {exploseRadius}");
             //    Debug.Log($"V62.0 击中爆炸伤害系数: {exploseCoe}");
              //   Debug.Log($"V62.0 击中爆炸特效: '{exploseEffect}'");
              //   Debug.Log($"V62.0 击中爆炸音效: '{exploseSound}'");
                
                // 击杀爆炸属性
                double killedExploseRadius = thing.GetTotalDouble(PropType.KilledExploseRadius).FirstOrDefault();
                double killedExploseCoe = thing.GetTotalDouble(PropType.KilledExploseCoe).FirstOrDefault();
                string killedExploseEffect = thing.GetTotalString(PropType.KilledExploseEffect).FirstOrDefault();
                string killedExploseSound = thing.GetTotalString(PropType.KilledExploseSound).FirstOrDefault();
                
            //     Debug.Log($"V62.0 击杀爆炸半径: {killedExploseRadius}");
            //     Debug.Log($"V62.0 击杀爆炸伤害系数: {killedExploseCoe}");
            //     Debug.Log($"V62.0 击杀爆炸特效: '{killedExploseEffect}'");
             //    Debug.Log($"V62.0 击杀爆炸音效: '{killedExploseSound}'");
                
                if (exploseRadius > 0)
                {
               //      Debug.Log($"V62.0 ✅ 击中爆炸条件满足，爆炸半径:{exploseRadius}");
                }
                else
                {
                //     Debug.Log($"V62.0 ❌ 击中爆炸条件不满足，爆炸半径为0");
                }
            }
            catch (Exception ex)
            {
             //    Debug.LogError($"V62.0 LogExplosionProperties异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V62.0 - 记录击中后爆炸检查日志，复制自ActorMissileTrackEnemy  
        /// </summary>
        private void LogPostHitExplosionCheck(ThingBase thing, IList<HitThingCells> enemies)
        {
            try
            {
             //    Debug.Log($"V62.0 ===== 击中后爆炸触发检查 =====");
                
                // 检查是否有敌人死亡
                bool anyEnemyDied = false;
                foreach (var enemy in enemies)
                {
                    if (enemy.Thing is MonsterThing monster)
                    {
                        bool isDead = monster.Hp.Value <= 0;
                   //      Debug.Log($"V62.0 敌人 {monster.GetType().Name} 血量:{monster.Hp.Value} 是否死亡:{isDead}");
                        if (isDead) anyEnemyDied = true;
                    }
                }
                
                if (anyEnemyDied)
                {
                //     Debug.Log($"V62.0 有敌人死亡 - 应该检查击中爆炸 + 击杀爆炸");
                }
                else
                {
                 //    Debug.Log($"V62.0 没有敌人死亡 - 只应该检查击中爆炸");
                }
            }
            catch (Exception ex)
            {
                // Debug.LogError($"V62.0 LogPostHitExplosionCheck异常: {ex.Message}");
            }
        }

        /// <inheritdoc />
        public override async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);
                
                // 重置子弹图片缩放
                if (ImgElement != null)
                {
                    ImgElement.transform.localScale = Vector3.one;
                }
                
                // 重置抛物线子弹特有字段，防止对象池复用时的脏数据
                fixedStartPosition = Vector3.zero;
                fixedTargetPosition = Vector3.zero;
                fixedTotalDuration = 0f;
                
                // 重置移动时间
                if (MoveDuration != null)
                {
                    MoveDuration.Value = 0f;
                }
                
                // 清空导弹发射器引用
                if (MissileLocusGenerator?.MissileEjector != null)
                {
                    MissileLocusGenerator.MissileEjector.gameObject.SetActive(false);
                }
                MissileLocusGenerator = null;
                
                // 清空轨迹数据
                Locus?.Clear();
                
                gameObject.SetActive(false);

                // 延时1分钟后置为null
                await UniTask.Delay(TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }
    }
}