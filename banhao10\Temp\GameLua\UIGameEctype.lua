--[[
********************************************************************
    created:    2023/08/26
    author :    李锦剑
    purpose:    副本界面
*********************************************************************
--]]

local luaID = ('UIGameEctype')
---@class UIGameEctype: UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnHeroPropChange] = m.UpdateInterface,
        [EventID.FreshenWorld] = m.UpdateInterface,
        [EventID.Battle_End] = m.BattleEnd,
        [EventID.OnRankingListUpdate] = m.UpdateRankData,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    -- HelperL.AdaptScale(m.objList.Img_Bg1, 6)
    -- HelperL.AdaptScale(m.objList.Img_Bg2, 6)
    HelperL.AdaptScale_Width(m.objList.Item_Ectype)
end

--------------------------------------------------------------------
-- 创建时
--------------------------------------------------------------------
function m.OnCreate()
    m.buttonList = {}
    ---@type StageItem[]
    m.ectypeItemList = {}
    m.rankingRewardsItem = {}
    m.objList.Obj_Item.gameObject:SetActive(false)
    -- m.objList.Txt_Title4.text = GetGameText(luaID, 12)
    m.objList.Txt_Title5.text = GetGameText(luaID, 13)
    m.objList.Txt_Time.text = GetGameText(luaID, 18)

    --物品背包
    m.maxStageIdList = {} --当前刷到的最大副本id列表
    local cfg
    for frontType = 5, 14, 1 do
        cfg = Schemes.CatMainStage:GetByFrontType(frontType)
        if cfg then
            m.maxStageIdList[frontType] = { firstStageId = cfg[1].ID, curStageId = cfg[1].ID }
            if frontType ~= 7 then
                table.insert(m.ectypeItemList, m.CreateEctypeItem(frontType, cfg))
            end
        end
    end

    local redDotDataList = {
        { windowID = WndID.GameEctype,     name = GetGameText(luaID, 10) },
        { windowID = WndID.SurvivalEctype, name = GetGameText(luaID, 11) },
    }
    for i, v in ipairs(redDotDataList) do
        local item = {}
        item.redDotData = v
        item.index = i
        item.com = m:CreateSubItem(m.objList.Grid_Button, m.objList.Item_Button)
        item.com.Txt_Name.text = v.name
        item.com.Txt_Lock.text = v.name
        item.com.Txt_Select.text = v.name
        item.com.Img_Lock.gameObject:SetActive(false)
        item.com.Obj_Unlock.gameObject:SetActive(true)
        item.com.Img_Select.gameObject:SetActive(false)
        item.com.Img_Bg.gameObject:SetActive(true)
        item.btn = item.com.gameObject:GetComponent('Button')
        item.btn.onClick:AddListenerEx(function()
            m.SelectUI(item.index)
        end)
        --设置红点
        item.SetRedDot = function()
            item.redDot = m:SetWndRedDot(item.com.transform, CachedVector2:Set(-30, -27))
            if item.redDot then
                item.redDot:AddCheckParam(item.redDotData.windowID)
            end
        end
        table.insert(m.buttonList, item)
    end

    m.rankingItemList = {}
    m.rankingDataList = {}
    local dataList = Schemes.CommonText:GetByTextType(4)
    for i, v in ipairs(dataList) do
        if not m.rankingItemList[i] then
            m.rankingItemList[i] = m.CreateRanking(i)
        end
        m.rankingItemList[i].UpdateData(v)
        --根据奖励类型分类
        if not m.rankingDataList[v.PrizeType] then
            m.rankingDataList[v.PrizeType] = {}
        end
        table.insert(m.rankingDataList[v.PrizeType], v)
    end

    if SWITCH.GAME_ECTYPE_RANKING_CLOSE then
        m.objList.Txt_Title7.gameObject:SetActive(true)
        m.objList.Txt_Title5.gameObject:SetActive(false)
        m.objList.Obj_Ranking.gameObject:SetActive(false)
    else
        m.objList.Txt_Title7.gameObject:SetActive(false)
        m.objList.Txt_Title5.gameObject:SetActive(true)
        m.objList.Obj_Ranking.gameObject:SetActive(true)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m.OnOpen()
    m.objList.EctypeLevel.gameObject:SetActive(false)
    m.objList.SaoDangPanel.gameObject:SetActive(false)
    m.objList.RankingRewards_Panel.gameObject:SetActive(false)

    -----------设置红点------------
    for k, v in pairs(m.ectypeItemList) do
        v.SetRedDot()
    end
    for k, v in pairs(m.buttonList) do
        v.SetRedDot()
    end
    m:SetWndRedDot(m.objList.Btn_Entry_Battle):AddCheckParam(WndID.SurvivalEctype)
    m:SetWndRedDot(m.objList.Btn_RankingRewards):AddCheckParam(RedDotCheckType
        .SurvivalRankingList)
    
    m.SelectUI(m.selectTab or 1)
    m.UpdateInterface()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_CloseEctype1.onClick:AddListenerEx(function()
        m.objList.EctypeLevel.gameObject:SetActive(false)
    end)

    m.objList.Btn_Start.onClick:AddListenerEx(m.StartFight)
    m.objList.Btn_Start_Ad.onClick:AddListenerEx(m.StartFight)
    m.objList.Btn_SaoDang.onClick:AddListenerEx(m.SaoDang)
    m.objList.Btn_SaoDangAd.onClick:AddListenerEx(m.StartFight)
    m.objList.Btn_No.onClick:AddListenerEx(function()
        m.objList.SaoDangPanel.gameObject:SetActive(false)
    end)
    m.objList.Btn_Yes.onClick:AddListenerEx(function()
        UIManager:OpenWnd(WndID.MonthCard)
    end)
    m.objList.Btn_Entry_Battle.onClick:AddListenerEx(m.StartFight)
    m.objList.Btn_Entry_Battle_Ad.onClick:AddListenerEx(m.StartFight)

    m.objList.Btn_Arrows1.onClick:AddListenerEx(function()
        m.CutEctype(m.Select_Index - 1)
    end)
    m.objList.Btn_Arrows2.onClick:AddListenerEx(function()
        m.CutEctype(m.Select_Index + 1)
    end)
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_RankingClose.onClick:AddListenerEx(function()
        m.objList.RankingRewards_Panel.gameObject:SetActive(false)
    end)
    m.objList.Btn_RankingRewards.onClick:AddListenerEx(function()
        m.objList.RankingRewards_Panel.gameObject:SetActive(true)
        RankingModule.RequestSelfRank(RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY, m.UpdateRankingRewards)
    end)
    m.objList.Btn_GetAward.onClick:AddListenerEx(m.GetRankingAward)
    m.objList.Btn_Change.onClick:AddListenerEx(function()
        UIManager:OpenWnd(WndID.AirPlaneNew, WndID.GameEctype)
    end)

    m.objList.Btn_Explain1.onClick:AddListenerEx(function()
        HelperL.OpenTipsWindow(11)
    end)
    
    m.objList.Btn_Explain2.onClick:AddListenerEx(function()
        HelperL.OpenTipsWindow(10)
    end)
end

--------------------------------------------------------------------
--选择界面
--------------------------------------------------------------------
function m.SelectUI(index)
    if m.selectTab then
        m.buttonList[m.selectTab].com.Img_Bg.gameObject:SetActive(true)
        m.buttonList[m.selectTab].com.Img_Select.gameObject:SetActive(false)
    end
    m.selectTab = index
    m.buttonList[index].com.Img_Bg.gameObject:SetActive(false)
    m.buttonList[index].com.Img_Select.gameObject:SetActive(true)

    if m.selectTab == 1 then
        RankingModule.RequestRankList(RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY)
        m.objList.TZ_Panel.gameObject:SetActive(false)
        m.objList.SC_Panel.gameObject:SetActive(true)
        m.objList.Txt_Title6.text = ""
        m.Stage_List = Schemes.CatMainStage:GetByFrontType(7)
        local maxStage = m.maxStageIdList[7].curStageId -
            m.maxStageIdList[7]
            .curStageId                -- HeroDataManager:GetLogicData(TEctypeToLogicValue[7])
        if maxStage ~= 0 then
            maxStage = (maxStage + 1) % 100
            if maxStage >= #m.Stage_List then
                maxStage = #m.Stage_List
            end
        else
            maxStage = 1
        end

        m.Select_Index = maxStage
        m.Select_FrontType = 7
        local data = m.Stage_List[m.Select_Index]
        local expendList = HelperL.Split(data.Need, ';')
        local count1 = SkepModule:GetGoodsCount(expendList[1])
        local count2 = SkepModule:GetGoodsCount(expendList[3])
        local num = tonumber(expendList[2])
        if count1 >= num then
            local color = count1 >= num and UI_COLOR.White or UI_COLOR.Red
            m.objList.Txt_ExpendAmount_SC.text = string.format('<color=%s>%s/%s</color>', color, count1, num)
            m.objList.Btn_Entry_Battle.gameObject:SetActive(true)
            m.objList.Btn_Entry_Battle_Ad.gameObject:SetActive(false)
        else
            local color = count2 >= num and UI_COLOR.White or UI_COLOR.Red
            m.objList.Txt_ExpendAmount_SC.text = string.format('<color=%s>%s/%s</color>', color, count2, num)
            m.objList.Btn_Entry_Battle.gameObject:SetActive(false)
            m.objList.Btn_Entry_Battle_Ad.gameObject:SetActive(true)
        end

        local good = Schemes:GetGoodsConfig(tonumber(expendList[1]))
        if count1 < num then
            good = Schemes:GetGoodsConfig(tonumber(expendList[3]))
        end
        AtlasManager:AsyncGetSprite(good.IconID, m.objList.Img_ItemIcon_SC)
    else
        m.objList.TZ_Panel.gameObject:SetActive(true)
        m.objList.SC_Panel.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
--创建副本栏
--------------------------------------------------------------------
function m.CreateEctypeItem(frontType, stageData)
    ---@class StageItem
    local item = {}
    item.frontType = frontType
    item.stageList = stageData
    item.selectIndex = 1
    item.com = m:CreateSubItem(m.objList.Grid_Ectype, m.objList.Item_Ectype)
    item.Img_Start = item.com.Btn_Start_Ad.gameObject:GetComponent("Image")
    item.com.Btn_Start.onClick:AddListenerEx(function()
        m.Stage_List = item.stageList
        m.Select_Index = item.selectIndex
        m.Select_FrontType = item.frontType
        m.CutEctype(m.Select_Index)
    end)
    item.com.Btn_Start_Ad.onClick:AddListenerEx(function()
        m.Stage_List = item.stageList
        m.Select_Index = item.selectIndex
        m.Select_FrontType = item.frontType
        m.CutEctype(m.Select_Index)
    end)
    item.Update = function()
        local data = item.stageList[1]
        local maxStage = m.maxStageIdList[item.frontType].curStageId -
            m.maxStageIdList[item.frontType]
            .firstStageId                             --HeroDataManager:GetLogicData(TEctypeToLogicValue[item.frontType])
        if maxStage ~= 0 then
            maxStage = (maxStage + 1) % 100
            if maxStage >= #item.stageList then
                maxStage = #item.stageList
            end
            item.selectIndex = maxStage
            data = item.stageList[maxStage]
        end

        local level1 = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        local level2 = EctypeUnlockLevel[item.frontType]
        if level1 < level2 then
            item.com.Txt_Hint.text = string.format(GetGameText(luaID, 1), level2, data.Desc)
            item.com.Btn_Lock.gameObject:SetActive(true)
        else
            item.com.Btn_Lock.gameObject:SetActive(false)
        end

        local expendList = HelperL.Split(data.Need, ';')
        local itemID1 = tonumber(expendList[1])
        local itemID2 = tonumber(expendList[3])
        local count1 = SkepModule:GetGoodsCount(itemID1)
        local count2 = SkepModule:GetGoodsCount(itemID2)
        local num = tonumber(expendList[2])

        if count1 >= num then
            local color = count1 >= num and UI_COLOR.White or UI_COLOR.Red
            item.com.Txt_Amount.text = string.format('<color=%s>%s/%s</color>', color, count1, num)
            item.com.Btn_Start.gameObject:SetActive(true);
            item.com.Btn_Start_Ad.gameObject:SetActive(false);
        else
            local color = count2 >= num and UI_COLOR.White or UI_COLOR.Red
            item.com.Txt_Amount.text = string.format('<color=%s>%s/%s</color>', color, count2, num)
            item.com.Btn_Start_Ad_gary.gameObject:SetActive(count2 < num)
            item.com.Btn_Start.gameObject:SetActive(false);
            item.com.Btn_Start_Ad.gameObject:SetActive(true);
        end

        item.Img_Start.raycastTarget = count2 >= num
        item.com.Txt_Title.text = data.Desc .. '-' .. data.Name

        local good = Schemes:GetGoodsConfig(itemID1)
        if count1 < num then
            good = Schemes:GetGoodsConfig(itemID2)
        end
        AtlasManager:AsyncGetSprite(good.IconID, item.com.Img_ItemIcon)

        local mapPosList = HelperL.Split(data.Icon, ';')
        AtlasManager:AsyncGetSprite(mapPosList[2], item.com.Img_Bg1)
        local prizeGoods = Schemes.PrizeTable:GetGoodsList(data.PrizeID[1])
        if prizeGoods then
            -- item.com.Img_Icon.sprite = prizeGoods[1].sprite
            AtlasManager:AsyncGetSprite(prizeGoods[1].iconID, item.com.Img_Goods)
            item.com.Txt_Goods.text = prizeGoods[1].num
        end
    end
    --设置红点
    item.SetRedDot = function()
        item.redDot = m:SetWndRedDot(item.com.Btn_Start)
        if item.redDot then
            item.redDot:AddCheckParam(RedDotCheckType.FuBenRed, item.frontType)
        end
    end
    return item
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateInterface()
    HttpReques.SendRequest(ERequestID.ListPassedStage_StageType, nil, function(o)
        if o ~= nil and o.rows ~= nil and #o.rows ~= 0 then
            for k, v in ipairs(o.rows) do
                if m.maxStageIdList[v.StageType] ~= nil then
                    m.maxStageIdList[v.StageType].curStageId = v.StageLvl + 1
                end
            end
        end

        for i, v in ipairs(m.ectypeItemList) do
            v.Update()
        end
        m.SelectUI(m.selectTab)
        m.OnSetFightHero()
    end)
end

--------------------------------------------------------------------
--切换副本小关卡
--------------------------------------------------------------------
function m.CutEctype(index)
    if index < 1 then return end
    local num = #m.Stage_List
    if index > num then return end
    local cfg = m.Stage_List[index]
    local maxStage = m.maxStageIdList[cfg.FrontType].curStageId -
        m.maxStageIdList[cfg.FrontType]
        .firstStageId                            --HeroDataManager:GetLogicData(TEctypeToLogicValue[cfg.FrontType])
    if maxStage ~= 0 then
        maxStage = (maxStage + 1) % 100
        if maxStage >= #m.Stage_List then
            maxStage = #m.Stage_List
        end
    else
        maxStage = 1
    end
    if index > maxStage then return end

    if index == maxStage then
        m.is_max_stage = true
    else
        m.is_max_stage = false
    end

    m.objList.Txt_Arrow1.gameObject:SetActive(index ~= 1)
    m.objList.Txt_Arrow2.gameObject:SetActive(index < maxStage)
    m.UpdateEctypeLevel1(m.Stage_List[index])
    m.Select_Index = index
end

--------------------------------------------------------------------
--更新副本界面1
--------------------------------------------------------------------
function m.UpdateEctypeLevel1(data)
    if not data then return end
    m.objList.Txt_Title1.text = data.Desc
    m.objList.Txt_EctypeLevel.text = data.Name
    local cfg = m.Stage_List[m.Select_Index]
    local expendList = HelperL.Split(data.Need, ';')
    local maxStage = m.maxStageIdList[cfg.FrontType].curStageId -
        m.maxStageIdList[cfg.FrontType]
        .firstStageId                            --HeroDataManager:GetLogicData(TEctypeToLogicValue[cfg.FrontType])

    local count1 = SkepModule:GetGoodsCount(expendList[1])
    local count2 = SkepModule:GetGoodsCount(expendList[3])
    local num = tonumber(expendList[2])

    m.objList.Btn_Start.gameObject:SetActive(false)
    m.objList.Btn_Grey2.gameObject:SetActive(false)
    m.objList.Btn_Start_Ad.gameObject:SetActive(false)
    if count1 >= num then
        local color = count1 >= num and UI_COLOR.White or UI_COLOR.Red
        m.objList.Txt_ExpendAmount.text = string.format('<color=%s>%s/%s</color>', color, count1, num)
        m.objList.Btn_Start.gameObject:SetActive(true)
    else
        local color = UI_COLOR.White
        if count2 >= num then
            m.objList.Btn_Start_Ad.gameObject:SetActive(true)
        else
            m.objList.Btn_Grey2.gameObject:SetActive(true)
            color = UI_COLOR.Red
        end
        m.objList.Txt_ExpendAmount.text = string.format('<color=%s>%s/%s</color>', color, count2, num)
    end

    local good = Schemes:GetGoodsConfig(tonumber(expendList[1]))
    if count1 < num then
        good = Schemes:GetGoodsConfig(tonumber(expendList[3]))
    end
    AtlasManager:AsyncGetSprite(good.IconID, m.objList.Img_ItemIcon)

    m.OnSetFightHero()
    m.objList.EctypeLevel.gameObject:SetActive(true)
    local prizeGoods = Schemes.PrizeTable:GetGoodsList(data.PrizeID[1])
    if prizeGoods then
        AtlasManager:AsyncGetSprite(prizeGoods[1].iconID, m.objList.Img_Goods)
        m.objList.Txt_Goods.text = prizeGoods[1].num
    end

    --扫荡按钮
    if cfg.FrontType ~= 7 then
        if data.ID <= maxStage then
            if HelperL.IsBuyChaoJiYueKa() then
                local cfg = m.Stage_List[m.Select_Index]
                local expendList = HelperL.Split(cfg.Need, ';')
                local count1 = SkepModule:GetGoodsCount(expendList[1])
                local count2 = SkepModule:GetGoodsCount(expendList[3])
                local num = tonumber(expendList[2])
                local isHaveDaoJu = false
                if count1 >= num then
                    isHaveDaoJu = true
                    -- m.objList.Btn_SaoDang.gameObject:SetActive(true)
                    m.objList.Btn_SaoDangAd.gameObject:SetActive(false)
                else
                    if count2 >= num then
                        isHaveDaoJu = true
                        m.objList.Btn_SaoDang.gameObject:SetActive(false)
                        -- m.objList.Btn_SaoDangAd.gameObject:SetActive(true)
                    end
                end

                if isHaveDaoJu then
                    AtlasManager:AsyncGetSprite('bth_an_02', m.objList.Btn_SaoDang)
                    m.objList.Btn_SaoDang.gameObject:GetComponent('Button').enabled = true
                else
                    -- m.objList.Btn_SaoDang.gameObject:SetActive(true)
                    AtlasManager:AsyncGetSprite('bth_an1_22', m.objList.Btn_SaoDang)
                    m.objList.Btn_SaoDangAd.gameObject:SetActive(false)
                    m.objList.Btn_SaoDang.gameObject:GetComponent('Button').enabled = false
                end
            else
                -- m.objList.Btn_SaoDang.gameObject:SetActive(true)
            end
        else
            m.objList.Btn_SaoDang.gameObject:SetActive(false)
            m.objList.Btn_SaoDangAd.gameObject:SetActive(false)
        end
    end
end

--选择出战角色回调
function m.OnSetFightHero()
    local entity = EntityModule:GetEntity(HelperL.clientTempAirplaneUID)
    if entity then
        local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        AtlasManager:AsyncGetGoodsSprite(goodsID, m.objList.Img_HeroIcon)
    end
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.StartFight()
    local cfg = m.Stage_List[m.Select_Index]
    if not cfg then
        warn('数据为空 cfg==nil')
        return
    end

    local maxStageId = m.maxStageIdList[cfg.FrontType].curStageId
    if maxStageId ~= 1 and cfg.ID > maxStageId then
        --cfg = m.Stage_List[m.Select_Index - 1]
        HelperL.ShowMessage(TipType.FlowText, '请通关' .. m.Stage_List[m.Select_Index - 1].Name)
        return
    end
    --m.objList.EctypeLevel.gameObject:SetActive(false)
    m.Last_Select_Index = m.Select_Index

    local expendList = HelperL.Split(cfg.Need, ';')
    local count1 = SkepModule:GetGoodsCount(expendList[1])
    local num = tonumber(expendList[2])

    if count1 < num then
        local medicamentScheme = Schemes.Medicament:Get(tonumber(expendList[3]))
        AdvertisementManager.ShowRewardAd(medicamentScheme.UseParam6[1], m.WatchAdCallback)
        return
    end
    m.RequestEnterBattle(cfg, maxStageId)
end

--------------------------------------------------------------------
--看广告事件
--------------------------------------------------------------------
function m.WatchAdCallback()
    local cfg = m.Stage_List[m.Select_Index]
    if not cfg then return end
    local expendList = HelperL.Split(cfg.Need, ';')
    local goodID = tonumber(expendList[3]) or 0
    if goodID == 0 then return end
    local entity = EntityModule:GetEntity(SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET):GetGoodsUID(goodID))
    if not entity then return end
    --注册--使用物品事件
    EventManager:Subscribe(EventID.MSG_SKEP_USEGOODS, m.UseGoodsCallback)
    --请求使用物品
    SkepModule:ExecuteUseGood(entity, 1)
end

--------------------------------------------------------------------
--使用物品事件
--------------------------------------------------------------------
function m.UseGoodsCallback()
    --取消--使用物品事件
    EventManager:UnSubscribe(EventID.MSG_SKEP_USEGOODS, m.UseGoodsCallback)
    local cfg = m.Stage_List[m.Select_Index]
    if not cfg then return end
    --m.RequestEnterBattle(cfg)
    m.StartFight()
end

--------------------------------------------------------------------
--请求进入战斗
--------------------------------------------------------------------
function m.RequestEnterBattle(cfg, maxStageId)
    m:CloseSelf()
    -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

--------------------------------------------------------------------
--战斗结束
--------------------------------------------------------------------
function m.BattleEnd()
    m.gameObject:SetActive(true)
    if m.selectTab == 2 then
        local cfg = m.Stage_List[m.Last_Select_Index]
        local maxStage = m.maxStageIdList[cfg.FrontType].curStageId -
            m.maxStageIdList[cfg.FrontType]
            .firstStageId                            --HeroDataManager:GetLogicData(TEctypeToLogicValue[cfg.FrontType])
        if maxStage ~= 0 then
            maxStage = (maxStage + 1) % 100
            if maxStage >= #m.Stage_List then
                maxStage = #m.Stage_List
            end
        else
            maxStage = 1
        end

        if m.is_max_stage then
            m.CutEctype(maxStage)
        else
            m.CutEctype(m.Last_Select_Index)
        end
    end
end

--------------------------------------------------------------------
---更新排行榜数据
---@param data SC_Rank_GetRankList
--------------------------------------------------------------------
function m.UpdateRankData(data)
    RankingModule.RequestSelfRank(RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY, m.UpdateRankingRewards)
end

--------------------------------------------------------------------
--更新排行榜数据
--------------------------------------------------------------------
function m.UpdateRankingRewards(data)
    --满足条件
    local isConditions = false
    local list = m.rankingDataList[1]
    local commonText = list[#list]
    local rankStr
    if data.SelfRankValue >= 10000 and data.SelfRank <= commonText.RankMax then --未上榜--击杀奖励
        for i, v in ipairs(list) do
            if data.SelfRank >= v.RankMin and data.SelfRank <= v.RankMax then
                commonText = v
                isConditions = true
            end
        end
        rankStr = data.SelfRank
    else --上榜--排名奖励
        list = m.rankingDataList[2]
        commonText = list[#list]
        for i, v in ipairs(list) do
            if data.SelfRankValue >= v.RankMin and data.SelfRankValue <= v.RankMax then
                commonText = v
                isConditions = true
            end
        end
        rankStr = data.SelfRank --GetGameText(luaID, 16)
    end
    m.objList.Txt_Title6.text = string.format(GetGameText(luaID, 14), data.SelfRankValue, rankStr)

    ----------------更新按钮------------------
    m.objList.Btn_GetAward.gameObject:SetActive(false)
    m.objList.PrizeContent.gameObject:SetActive(false)
    m.objList.Txt_RankingHint.gameObject:SetActive(false)
    m.objList.Btn_IsGet.gameObject:SetActive(false)
    if isConditions then
        local bool = true
        --判断总次数
        local num = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
        if commonText.ToTime ~= 0 and num >= commonText.ToTime then
            bool = false
        end
        --判断今日次数
        local num2 = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
        if num2 >= commonText.DayTime then
            bool = false
        end
        if bool then
            m.objList.Btn_GetAward.gameObject:SetActive(true)
        else
            m.objList.Btn_IsGet.gameObject:SetActive(true)
        end
        m.objList.PrizeContent.gameObject:SetActive(true)
    else
        local cfg = m.rankingDataList[2][#list]
        m.objList.Txt_RankingHint.text = string.format(GetGameText(luaID, 17), cfg.RankMin)
        m.objList.Txt_RankingHint.gameObject:SetActive(true)
    end

    ----------------创建奖励------------------
    if commonText.ID ~= m.AdverID then
        local rechargeCard = Schemes.RechargeCard:Get(commonText.Text)
        local goodsList = Schemes.PrizeTable:GetGoodsList(rechargeCard.PrizeID) or {}
        local num = math.max(#m.rankingRewardsItem, #goodsList)
        for i = 1, num, 1 do
            if not m.rankingRewardsItem[i] then
                m.rankingRewardsItem[i] = m.CreateSingleGoods(m.objList.PrizeContent)
            end
            m.rankingRewardsItem[i].UpdateData(goodsList[i])
        end
    end

    m.AdverID = commonText.ID
end

--------------------------------------------------------------------
-- 创建排行框
--------------------------------------------------------------------
function m.CreateRanking(index)
    local item = {}
    item.index = index
    item.awardItem = {}
    item.com = m:CreateSubItem(m.objList.Grid_Ranking, m.objList.Item_Ranking)
    item.UpdateData = function(data)
        if data then
            item.com.Txt_Ranking.text = data.Desc
            local rechargeCard = Schemes.RechargeCard:Get(data.Text)
            local goodsList = {}
            if rechargeCard then
                goodsList = Schemes.PrizeTable:GetGoodsList(rechargeCard.PrizeID) or {}
            end
            local num = math.max(#item.awardItem, #goodsList)
            for i = 1, num, 1 do
                if not item.awardItem[i] then
                    item.awardItem[i] = m.CreateSingleGoods(item.com.Grid_Award)
                end
                item.awardItem[i].UpdateData(goodsList[i])
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 创建物品
--------------------------------------------------------------------
function m.CreateSingleGoods(parent)
    local item = {}
    item.goods = CreateSingleGoods(parent)
    item.goods:SetSize(140, 140)
    item.goods:SetShowName(false)
    item.goods:SetShowNum(true)
    ---更新奖励物品数据
    ---@param data PrizeParse
    item.UpdateData = function(data)
        if data then
            item.goods:SetItemData(data.id, data.num)
            item.goods:SetVisible(true)
        else
            item.goods:SetVisible(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 请求回调函数
--------------------------------------------------------------------
function m.RequestCallback(result, content)
    if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
        if m.RequestType == 1 then     --扫荡副本

        elseif m.RequestType == 2 then --领取排行奖励
            RankingModule.RequestSelfRank(RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY, m.UpdateRankingRewards)
        end
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result, content))
    end
end

--------------------------------------------------------------------
--扫荡副本
--------------------------------------------------------------------
function m.SaoDang()
    if HelperL.IsBuyChaoJiYueKa() then
        local cfg = m.Stage_List[m.Select_Index]
        m.RequestType = 1
        local str_req = string.format("LuaRequestSaoDaoCatEctype?RaceType=%s&StageID=%s", cfg.FrontType, cfg.ID)
        LuaModule.RunLuaRequest(str_req, m.RequestCallback)
    else
        m.objList.SaoDangPanel.gameObject:SetActive(true)
    end
end

--------------------------------------------------------------------
--领取排行奖励
--------------------------------------------------------------------
function m.GetRankingAward()
    m.objList.RankingRewards_Panel.gameObject:SetActive(false)
    if not m.AdverID then
        warn('无排行奖励可领取！！！')
        return
    end

    m.RequestType = 2
    local str_req = string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', m.AdverID)
    LuaModule.RunLuaRequest(str_req, m.RequestCallback)
end

return m
