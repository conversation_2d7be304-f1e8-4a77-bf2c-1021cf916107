using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using HotScripts;
using DataStructure;
using Thing;
using UniRx;
using UnityEngine;
using X.PB;
using Apq.Unity3D.UnityHelpers;
using RxEventsM2V;

namespace View
{
    /// <summary>
    /// V60.4 - ��ɫ����׷�������ӵ���ͼ��
    /// ����ActorLocusBullet��ʵ��ʵʱ׷��Ŀ����˵Ĺ���
    /// V61.0 - �Ż��汾�����ٱ�ը��⿨�٣��Ż���������㷨
    /// </summary>
    public class ActorMissileTrackEnemy : ActorLocusBullet
    {
        /// <summary>
        /// ׷��Ŀ�����
        /// </summary>
        public ThingBase TargetEnemy { get; set; }
        
        /// <summary>
        /// ׷���ƶ��ٶ�
        /// </summary>
        private float _trackingSpeed = 100f;
        
        /// <summary>
        /// ���ת����ٶȣ���/�룩
        /// </summary>
        private float _maxTurnRate = 360f;
        
        /// <summary>
        /// �Ƿ�����׷��
        /// </summary>
        private bool _isTracking = true;
        
        /// <summary>
        /// �����֪Ŀ��λ�ã�Ŀ������ʱʹ�ã�
        /// </summary>
        private Vector3? _lastKnownTargetPosition;

        /// <summary>
        /// V61.0 - ����ı�ը��Χ�ڹ����б��������ظ�����
        /// </summary>
        private static List<MonsterThing> _cachedMonstersInExplosionRange = new List<MonsterThing>();
        private static readonly int MAX_EXPLOSION_MONSTERS_PER_FRAME = 5; // ÿ֡�����5ֻ����

        /// <summary>
        /// ��ʼ��׷����������ȡ����CommonProp����
        /// </summary>
        public override void Awake()
        {
            base.Awake();
            
            // ��ȡ������Ҫ��CommonProp����
            if (BulletThing != null)
            {
                // ׷���ƶ��ٶ�
                var bulletSpeed = BulletThing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                if (bulletSpeed > 0)
                {
                    _trackingSpeed = (float)bulletSpeed;
                }

                // ת���ٶ�
                var rotateSpeed = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.RotateSpeed).FirstOrDefault();
                if (rotateSpeed > 0)
                {
                    _maxTurnRate = (float)rotateSpeed;
                }

                // ����������йؼ�����
                //Debug.Log($"777777777 ActorMissileTrackEnemy.Awake() ���Լ���:");
                //Debug.Log($"- BulletSpeed(׷���ٶ�): {_trackingSpeed}");
                //Debug.Log($"- RotateSpeed(ת���ٶ�): {_maxTurnRate}");
                //Debug.Log($"- BulletRadius: {BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletRadius).FirstOrDefault()}");
                //Debug.Log($"- HitCd(������ȴ): {BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.HitCd).FirstOrDefault()}");
                //Debug.Log($"- BulletLife(��������): {BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault()}");
                //Debug.Log($"- PenetrateTimes(��͸����): {BulletThing.PenetrateTimes.Value}");
                
                // ��ը�������
                //Debug.Log($"- ExplosePriority(��ը����): {BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault()}");
                //Debug.Log($"- ExploseRadius(��ը�뾶): {BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault()}");
                //Debug.Log($"- ExploseEffect(��ը��Ч): {BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault()}");
                //Debug.Log($"- ExploseSound(��ը��Ч): {BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseSound).FirstOrDefault()}");
                //Debug.Log($"- HitSound(������Ч): {BulletThing.CdExecutor.Thing.GetTotalString(PropType.HitSound).FirstOrDefault()}");
            }
        }

        /// <summary>
        /// ����׷��Ŀ��
        /// </summary>
        /// <param name="target">Ŀ�����</param>
        public void SetTargetEnemy(ThingBase target)
        {
            TargetEnemy = target;
            _isTracking = target != null;
            
            if (target != null)
            {
                _lastKnownTargetPosition = target.Position;
                //Debug.Log($"777777777 ActorMissileTrackEnemy ����׷��Ŀ�� - Ŀ��:{target.GetType().Name} λ��:{target.Position}");
            }
        }

        /// <summary>
        /// ��д�ƶ�����ʵ��׷���߼���ͬʱ��������CommonProp���Դ���
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                //Debug.Log($"777777777 ActorMissileTrackEnemy.DoTask_Move ��ʼ - ��ʼλ��:{transform.position}");
                
                for (;; await UniTask.Delay(TimeSpan.FromSeconds(MoveInterval), cancellationToken: token))
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    // ʹ�û����OnBeforeMoveOne��飨����BulletLife�����ԣ�
                    if (OnBeforeMoveOne())
                    {
                        //Debug.Log($"777777777 ActorMissileTrackEnemy OnBeforeMoveOne����true - ֹͣ�ƶ�");
                        return;
                    }

                    try
                    {
                        // ִ��׷���ƶ����Զ����߼���
                        bool shouldContinue = DoTrackingMove();
                        
                        if (!shouldContinue)
                        {
                            //Debug.Log($"777777777 ActorMissileTrackEnemy ׷���ƶ����� - ����Ŀ��");
                            break;
                        }

                        // ʹ�û������ײ����߼�������HitSound��HitCd��PenetrateTimes�ȣ�
                        if (ShouldPerformHitCheck())
                        {
                            LineSegment line = new LineSegment
                            {
                                PosStart = transform.position,
                                Dir = Vector3.zero // �������͵��߶μ��
                            };
                            
                            if (OnAfterMoveOne(line))
                            {
                                //Debug.Log($"777777777 ActorMissileTrackEnemy OnAfterMoveOne����true - ֹͣ�ƶ�");
                                return;
                            }
                        }

                        // ����Ƿ�Ӧ�ö���
                        if (BulletThing.ShouldDiscard.Value)
                        {
                            //Debug.Log($"777777777 ActorMissileTrackEnemy ShouldDiscard=true - ֹͣ�ƶ�");
                            return;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                //Debug.Log($"777777777 ActorMissileTrackEnemy.DoTask_Move ��ȡ��");
                throw;
            }
            catch (MissingReferenceException)
            {
                //Debug.Log($"777777777 ActorMissileTrackEnemy.DoTask_Move MissingReferenceException");
            }
            catch (Exception ex)
            {
                //Debug.LogError($"777777777 ActorMissileTrackEnemy.DoTask_Move �쳣: {ex.Message}");
            }
            finally
            {
                // ʹ�û����OnMoveEnd����
                OnMoveEnd();
            }
        }

        /// <summary>
        /// �ж��Ƿ�Ӧ��ִ�л��м��
        /// </summary>
        private bool ShouldPerformHitCheck()
        {
            // ÿ3���ƶ����1�Σ�������ײ���Ƶ��
            return Time.frameCount % 3 == 0;
        }

        /// <summary>
        /// ִ��׷���ƶ��߼�
        /// </summary>
        /// <returns>�Ƿ�Ӧ�ü����ƶ�</returns>
        private bool DoTrackingMove()
        {
            Vector3 currentPos = transform.position;
            Vector3 targetPos;

            // ȷ��Ŀ��λ��
            if (TargetEnemy != null && TargetEnemy.Hp.Value > 0)
            {
                // Ŀ���׷��Ŀ��
                targetPos = TargetEnemy.Position;
                _lastKnownTargetPosition = targetPos;
            }
            else if (_lastKnownTargetPosition.HasValue)
            {
                // Ŀ�����������������֪λ��
                targetPos = _lastKnownTargetPosition.Value;
            }
            else
            {
                // û��Ŀ�ֹ꣬ͣ�ƶ�
                //Debug.Log($"777777777 ActorMissileTrackEnemy DoTrackingMove - û��Ŀ��λ�ã�ֹͣ�ƶ�");
                return false;
            }

            // �������
            float distanceToTarget = Vector3.Distance(currentPos, targetPos);
            
            // ����Ѿ��ܽӽ�Ŀ�ֹ꣬ͣ�ƶ�
            if (distanceToTarget < 8f) // 8��������Ϊ����
            {
                //Debug.Log($"777777777 ActorMissileTrackEnemy DoTrackingMove - ����Ŀ��λ�ã�����:{distanceToTarget}");
                return false;
            }

            // �����ƶ�����
            Vector3 moveDirection = (targetPos - currentPos).normalized;
            
            // ����ת�������Ҫ��
            Vector3 currentForward = transform.forward;
            float angle = Vector3.Angle(currentForward, moveDirection);
            if (angle > 1f) // �Ƕȴ���1�Ȳ�ת��
            {
                Vector3 targetForward = Vector3.Slerp(currentForward, moveDirection, 
                    _maxTurnRate * Time.deltaTime / 180f);
                transform.forward = targetForward;
            }

            // �ƶ�
            float moveDistance = _trackingSpeed * Time.deltaTime;
            Vector3 newPosition = currentPos + moveDirection * moveDistance;
            
            // ����ƶ�·���ϵ���ײ
            TriggerCollision(currentPos, newPosition);
            
            // ����λ��
            transform.position = newPosition;
            
            return true;
        }

        /// <summary>
        /// ����·����ײ���
        /// </summary>
        private void TriggerCollision(Vector3 startPos, Vector3 endPos)
        {
            try
            {
                LineSegment line = new LineSegment
                {
                    PosStart = startPos,
                    Dir = (endPos - startPos).normalized
                };

                // �����ײ
                var hitEnemies = DoCollideEnemies(line);
                if (hitEnemies != null && hitEnemies.Count > 0)
                {
                    Debug.Log($"V62.0 ActorMissileTrackEnemy TriggerCollision - ��⵽��ײ����������:{hitEnemies.Count}");
                    
                    // ʹ�û���Ļ��д����߼�
                    foreach (var hitEnemy in hitEnemies)
                    {
                        if (hitEnemy.Thing != null)
                        {
                            Debug.Log($"V62.0 ׼���������е���: {hitEnemy.Thing.GetType().Name}");
                            OnHitEnemy(line, hitEnemy.Thing, hitEnemies);
                            break; // ֻ������һ�����еĵ���
                        }
                    }
                }
                else
                {
                    Debug.Log($"V62.0 ActorMissileTrackEnemy TriggerCollision - δ��⵽��ײ");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"V62.0 ActorMissileTrackEnemy TriggerCollision �쳣: {ex.Message}");
            }
        }

        /// <summary>
        /// ��д��ײ��⣬�Ż�����׷���ӵ�����ײ�߼�
        /// </summary>
        public override IList<HitThingCells> DoCollideEnemies(LineSegment line)
        {
            try
            {
                List<HitThingCells> hitEnemies = new List<HitThingCells>();
                Vector3 bulletPos = transform.position;
                float bulletRadius = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletRadius).FirstOrDefault();
                if (bulletRadius <= 0) bulletRadius = 5f; // Ĭ�ϰ뾶5����

                // V61.0 - �Ż�������ײ��⣬ֻ��鸽���Ĺ���
                var nearbyMonsters = GetNearbyMonsters(bulletPos, bulletRadius + 50f); // ����50���ؼ�ⷶΧ
                
                foreach (var monster in nearbyMonsters)
                {
                    if (monster?.CircularArea2D == null) continue;
                    
                    float distance = Vector3.Distance(bulletPos, monster.Position);
                    float hitThreshold = bulletRadius + (float)monster.GetTotalDouble(PropType.Radius).FirstOrDefault();
                    
                    if (distance <= hitThreshold)
                    {
                        hitEnemies.Add(new HitThingCells { Thing = monster });
                        //Debug.Log($"777777777 ActorMissileTrackEnemy DoCollideEnemies - ���й���:{monster.GetType().Name} ����:{distance} ��ֵ:{hitThreshold}");
                        break; // ����ֻ���е�һ������
                    }
                }

                return hitEnemies;
            }
            catch (Exception ex)
            {
                //Debug.LogError($"777777777 ActorMissileTrackEnemy DoCollideEnemies �쳣: {ex.Message}");
                return new List<HitThingCells>();
            }
        }

        /// <summary>
        /// V61.0 - ��ȡ�����Ĺ���Ż���Χ����
        /// </summary>
        private List<MonsterThing> GetNearbyMonsters(Vector3 center, float radius)
        {
            List<MonsterThing> nearbyMonsters = new List<MonsterThing>();
            
            try
            {
                Debug.Log($"V62.0 ��ʼ������������ - ����:{center}, �뾶:{radius}");
                
                var allMonsters = SingletonMgr.Instance.BattleMgr.Monsters;
                if (allMonsters == null || allMonsters.Count == 0) 
                {
                    Debug.Log($"V62.0 ս����û�й��� (allMonstersΪnull���)");
                    return nearbyMonsters;
                }
                
                Debug.Log($"V62.0 ս���ܹ�������: {allMonsters.Count}");
                
                // V61.0 - ���پ���ɸѡ�����⸴�ӵ�Բ����ײ����
                float radiusSquared = radius * radius; // ʹ��ƽ��������⿪������
                int checkedCount = 0;
                int aliveCount = 0;
                
                foreach (var monster in allMonsters)
                {
                    checkedCount++;
                    
                    if (monster == null) 
                    {
                        Debug.Log($"V62.0 ����null���� #{checkedCount}");
                        continue;
                    }
                    
                    if (monster.Hp.Value <= 0) 
                    {
                        Debug.Log($"V62.0 ������������ #{checkedCount} (Ѫ��:{monster.Hp.Value})");
                        continue;
                    }
                    
                    aliveCount++;
                    
                    // V62.1-fix ʹ��2D�����飬����Z�����
                    Vector3 diff = monster.Position - center;
                    float distanceSquared = diff.x * diff.x + diff.y * diff.y; // ����Z�����
                    float actualDistance = Mathf.Sqrt(distanceSquared);
                    
                    Debug.Log($"V62.1 ���� #{checkedCount} λ��:{monster.Position}, 2D����:{actualDistance:F2}, �Ƿ��ڷ�Χ��:{distanceSquared <= radiusSquared}");
                    
                    if (distanceSquared <= radiusSquared)
                    {
                        nearbyMonsters.Add(monster);
                        Debug.Log($"V62.0 ? ���� #{checkedCount} ���뱬ը��Χ�б�");
                        
                        // V61.0 - ���Ƶ��μ��Ĺ������������⿨��
                        if (nearbyMonsters.Count >= MAX_EXPLOSION_MONSTERS_PER_FRAME)
                        {
                            Debug.Log($"V62.0 �ﵽ�������������({MAX_EXPLOSION_MONSTERS_PER_FRAME}ֻ)��ֹͣ����");
                            break;
                        }
                    }
                }
                
                Debug.Log($"V62.0 ����������� - �������:{checkedCount}, �����:{aliveCount}, ��Χ������:{nearbyMonsters.Count}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"V62.0 GetNearbyMonsters �쳣: {ex.Message}");
            }
            
            return nearbyMonsters;
        }

        /// <summary>
        /// V63.0 - ɾ����д��OnHitEnemy������ʹ�û���ActorBulletBase�ı�׼��ը����
        /// ȷ������ShootMethodʹ����ȫ��ͬ�ı�ը�߼��������Զ��屬ը����
        /// </summary>
        // ע�ͣ���ɾ����д��OnHitEnemy����������ʹ�û���ı�׼ʵ��

        /// <summary>
        /// V63.0 - ��ɾ���Զ����DamageEnemyWithExplosion����
        /// ����ʹ�û���ActorBulletBase�ı�׼DoExplose�������б�ը����
        /// </summary>
        // ע�ͣ���ɾ��DamageEnemyWithExplosion������ͳһʹ�û��౬ը����

        /// <summary>
        /// V63.0 - ��ɾ���Զ����ProcessExplosionAsync����
        /// ����ʹ�û���ActorBulletBase�ı�׼DoExplose�������б�ը����
        /// </summary>
        // ע�ͣ���ɾ��ProcessExplosionAsync������ͳһʹ�û��౬ը����

        /// <summary>
        /// V63.0 - ��ɾ���Զ����ProcessExplosionDamageInFrames����
        /// ����ʹ�û���ActorBulletBase�ı�׼DoExplose�������б�ը����
        /// </summary>
        // ע�ͣ���ɾ��ProcessExplosionDamageInFrames������ͳһʹ�û��౬ը����

        /// <summary>
        /// V63.0 - ��ɾ���Զ����IsMonsterInExplosionRange����
        /// ����ʹ�û���ActorBulletBase��DoExplose�����ı�׼���������
        /// ����ʹ��CalcDistance2D_SolidCircleToSolidCircle���о�ȷ��2DԲ����ײ���
        /// </summary>
        // ע�ͣ���ɾ��IsMonsterInExplosionRange������ͳһʹ�û��౬ը����

        /// <summary>
        /// V63.0 - ��ɾ���Զ���ı�ը��־����
        /// ����ʹ�û���ActorBulletBase�ı�׼��ը���ƣ������Զ�����־��¼
        /// </summary>
        // ע�ͣ���ɾ��LogExplosionProperties��LogPostHitExplosionCheck������ͳһʹ�û��౬ը����

        /// <summary>
        /// ��дOnMoveEnd�����ӵ��������ʱ������ը
        /// </summary>
        protected override void OnMoveEnd()
        {
            try
            {
                //Debug.Log($"777777777 ActorMissileTrackEnemy.OnMoveEnd - �ӵ��ƶ������������汬ը");
                
                // V61.0 - �첽�������汬ը��飬��������
                CheckGroundExplosionAsync().Forget();
                
                // ���û����߼��������ӵ��أ�
                base.OnMoveEnd();
            }
            catch (Exception ex)
            {
                //Debug.LogError($"777777777 ActorMissileTrackEnemy.OnMoveEnd �쳣: {ex.Message}");
                // �쳣�����ҲҪ���û����߼�
                try
                {
                    base.OnMoveEnd();
                }
                catch
                {
                    // ignored
                }
            }
        }
        
        /// <summary>
        /// V61.0 - �첽�����汬ը�����⿨��
        /// </summary>
        private async UniTaskVoid CheckGroundExplosionAsync()
        {
            try
            {
                if (BulletThing?.CdExecutor?.Thing == null)
                {
                    //Debug.Log($"777777777 CheckGroundExplosionAsync - BulletThing��ThingΪnull���������汬ը���");
                    return;
                }
                
                ThingBase thing = BulletThing.CdExecutor.Thing;
                
                // ��ȡ��ը���ʺͰ뾶
                double explosePriority = thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();
                double exploseRadius = thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();
                
                //Debug.Log($"777777777 CheckGroundExplosionAsync - ��ը����:{explosePriority} ��ը�뾶:{exploseRadius}");
                
                if (exploseRadius > float.Epsilon)
                {
                    // �������Ƿ񴥷���ը
                    float randomValue = UnityEngine.Random.Range(0f, 1f);
                    bool shouldExplode = randomValue <= explosePriority;
                    
                    //Debug.Log($"777777777 CheckGroundExplosionAsync - ���ֵ:{randomValue} �Ƿ�ը:{shouldExplode}");
                    
                    if (shouldExplode)
                    {
                        // V63.0-fix 强制爆炸位置在游戏平面（Z=0），避免Z轴偏移影响伤害计算
                        Vector3 explosionPos = new Vector3(transform.position.x, transform.position.y, 0f);
                        Debug.Log($"V63.0 CheckGroundExplosionAsync - 触发地面爆炸");
                        Debug.Log($"V63.0 原始子弹位置: {transform.position}");
                        Debug.Log($"V63.0 修正爆炸位置: {explosionPos} (强制Z=0)");
                        Debug.Log($"V63.0 爆炸半径: {exploseRadius}");
                        
                        // V63.0 - 使用基类ActorBulletBase的标准爆炸机制
                        transform.position = explosionPos; // 设置爆炸位置
                        DoExplose(CancellationToken.None, thing, explosionPos, 1, 0).Forget(); // 调用基类标准爆炸方法
                    }
                    else
                    {
                        //Debug.Log($"777777777 CheckGroundExplosionAsync - ���汬ը����δ����");
                    }
                }
                else
                {
                    //Debug.Log($"777777777 CheckGroundExplosionAsync - ��ը�뾶Ϊ0�����������汬ը");
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"777777777 CheckGroundExplosionAsync �쳣: {ex.Message}");
            }
        }

        /// <summary>
        /// V61.0 - ����ԭ�����Է�����������
        /// </summary>
        [System.Obsolete("V61.0���Ż�Ϊ�첽�汾CheckGroundExplosionAsync")]
        private void CheckGroundExplosion()
        {
            CheckGroundExplosionAsync().Forget();
        }

        /// <summary>
        /// ���������ӵ�GameObject
        /// </summary>
        private void DestroyBulletImmediately()
        {
            try
            {
                //Debug.Log($"777777777 ActorMissileTrackEnemy.DestroyBulletImmediately - ��ʼ����");
                
                // ֹͣ����Э�̺�����
                if (this != null && gameObject != null)
                {
                    // ��������GameObject
                    Destroy(gameObject);
                    //Debug.Log($"777777777 ActorMissileTrackEnemy.DestroyBulletImmediately - GameObject������");
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"777777777 ActorMissileTrackEnemy.DestroyBulletImmediately �쳣: {ex.Message}");
            }
        }
    }
}
