﻿// ReSharper disable InconsistentNaming

using System.Linq;

using Apq.Unity3D.Extension;

using HotScripts;

using Thing;

using UnityEngine;
using UnityEngine.Events;

using X.PB;

namespace View
{
    /// <summary>
    /// 怪物
    /// </summary>
    public class MonsterBase : CreatureBase
    {
        /// <summary>
        /// 怪物物件(数据)
        /// </summary>
        public MonsterThing MonsterThing => CreatureThing as MonsterThing;

        /// <summary>
        /// 移动组件
        /// </summary>
        public MonsterMoveAI MonsterMoveAI { get; set; }

        /// <summary>
        /// 怪物AI组件
        /// </summary>
        public MonsterAI MonsterAI { get; set; }

        /// <summary>
        /// 寻路组件
        /// </summary>
        public NavAgent NavAgent { get; set; }

        /// <inheritdoc/>
        public override Vector3 CalcDir_Straight()
        {
            // X轴朝向玩家的方向
            var dirX = new Vector3(SingletonMgr.Instance.BattleMgr.Actor.Position.x - transform.position.x, 0)
                .normalized;
            // Y轴朝向玩家的方向
            var dirY = new Vector3(0, SingletonMgr.Instance.BattleMgr.Actor.Position.y - transform.position.y)
                .normalized;

            // 枪
            var gun = MonsterThing.Guns.Value;
            if (gun == null) return Vector3.down;

            // 枪的射程
            var gunRange = gun.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            //if (gunRange <= 0) return Vector3.down;

            // 怪物的半径
            var monsterRadius = CreatureThing.TotalProp_Radius;
            // 角色的半径
            var actorRadius = SingletonMgr.Instance.BattleMgr.Actor.TotalProp_Radius;

            // 与角色的距离
            var distance = SingletonMgr.Instance.BattleMgr.Actor.Position.CalcDistance2D_SolidCircleToSolidCircle(
                actorRadius, transform.position, monsterRadius);

            // 与玩家的X轴距离
            var distanceX = System.Math.Abs(SingletonMgr.Instance.BattleMgr.Actor.Position.x - transform.position.x)
                            - actorRadius - monsterRadius;
            // 与玩家的Y轴距离
            var distanceY = System.Math.Abs(SingletonMgr.Instance.BattleMgr.Actor.Position.y - transform.position.y)
                            - actorRadius - monsterRadius;

            // 能打到,不动了
            if (distance <= gunRange)
            {
                return Vector3.zero;
            }

            // // 1、要靠近了
            // if (distance <= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance ||
            //     // 2、X、Y轴都太远
            //     (distanceX >= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance &&
            //      distanceY >= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance))
            // {
            //     // 向玩家方向移动
            //     var dir = SingletonMgr.Instance.BattleMgr.Actor.Position - transform.position;
            //     var dir_1 = dir.normalized;
            //     return dir_1;
            // }
            //
            // // 横向仍太远，先收拢
            // if (distanceX >= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance)
            // {
            //     return dirX;
            // }

            // 还远着,Y轴朝向玩家的方向
            return dirY;
        }

        public override void Awake()
        {
            base.Awake();
            MonsterAI = gameObject.GetOrAddComponent<MonsterAI>();
            
            // V30.1-动画系统修复 确保初始化MonsterMoveAI和NavAgent组件
            MonsterMoveAI = gameObject.GetOrAddComponent<MonsterMoveAI>();
            NavAgent = gameObject.GetOrAddComponent<NavAgent>();
        }

        public override void Update()
        {
            base.Update();
            if (CreatureThing == null) return;
            
            // 怪物的半径
            var monsterRadius = CreatureThing.TotalProp_Radius;
            // 角色的半径
            var actorRadius = SingletonMgr.Instance.BattleMgr.Actor.TotalProp_Radius;

            // 与角色的距离
            var distance = SingletonMgr.Instance.BattleMgr.Actor.Position.CalcDistance2D_SolidCircleToSolidCircle(
                actorRadius, transform.position, monsterRadius);

            if (MonsterThing.NextImpactEnemyTime.Value <= Time.time)
            {
                if (distance <= float.Epsilon)
                {
                    // // 撞击的声音
                    // AudioPlayer.Instance.PlaySound(MonsterThing.GetTotalString(PropType.HitSound).FirstOrDefault())
                    //     .Forget();

                    MonsterThing.NextImpactEnemyTime.Value =
                        Time.time + (float)MonsterThing.CsvRow_BattleBrushEnemy.DamageAdd;

                    // 计算伤害量
                    var damage = Helper.CalcDamage(MonsterThing.Guns.FirstOrDefault(),
                        SingletonMgr.Instance.BattleMgr.Actor);

                    // 接受伤害
                    SingletonMgr.Instance.BattleMgr.Actor.TakeHit(MonsterThing, damage);
                }
            }
            SetSpineSortingOrderBySelfPosition();
        }


    }
}