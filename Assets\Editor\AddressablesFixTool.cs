using UnityEngine;
using UnityEditor;
using System.IO;

namespace AddressablesFixTool
{
    public class AddressablesFixTool : EditorWindow
    {
        [MenuItem("Tools/修复Addressables重复键问题")]
        public static void ShowWindow()
        {
            GetWindow<AddressablesFixTool>("Addressables修复工具");
        }

        [MenuItem("Tools/清理Addressables缓存")]
        public static void ClearAddressablesCache()
        {
            Debug.Log("=== 开始清理Addressables缓存 ===");
            
            try
            {
                // 1. 清理Addressables构建缓存
                string addressablesPath = Path.Combine(Application.dataPath, "../Library/com.unity.addressables");
                if (Directory.Exists(addressablesPath))
                {
                    Directory.Delete(addressablesPath, true);
                    Debug.Log("✅ 已删除Addressables缓存目录");
                }

                // 2. 清理AA build cache
                string aaBuildPath = Path.Combine(Application.dataPath, "../AddressableAssetsData");
                if (Directory.Exists(aaBuildPath))
                {
                    string[] buildDirs = { "Android", "WebGL", "Windows" };
                    foreach (string buildDir in buildDirs)
                    {
                        string fullPath = Path.Combine(aaBuildPath, buildDir);
                        if (Directory.Exists(fullPath))
                        {
                            Directory.Delete(fullPath, true);
                            Debug.Log($"✅ 已删除{buildDir}构建缓存");
                        }
                    }
                }

                // 3. 清理其他相关缓存
                string[] cachePaths = {
                    Path.Combine(Application.dataPath, "../Library/ArtifactDB"),
                    Path.Combine(Application.dataPath, "../Library/SourceAssetDB"),
                    Path.Combine(Application.dataPath, "../Temp")
                };

                foreach (string cachePath in cachePaths)
                {
                    if (File.Exists(cachePath))
                    {
                        File.Delete(cachePath);
                        Debug.Log($"✅ 已删除缓存文件: {Path.GetFileName(cachePath)}");
                    }
                    else if (Directory.Exists(cachePath))
                    {
                        Directory.Delete(cachePath, true);
                        Debug.Log($"✅ 已删除缓存目录: {Path.GetFileName(cachePath)}");
                    }
                }

                // 4. 刷新资产数据库
                AssetDatabase.Refresh();
                Debug.Log("✅ 已刷新资产数据库");

                Debug.Log("=== Addressables缓存清理完成 ===");
                EditorUtility.DisplayDialog("修复完成", "Addressables缓存已清理完成\n请重新打开Unity编辑器以确保完全生效", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"清理缓存时出错: {e.Message}");
                EditorUtility.DisplayDialog("修复失败", $"清理缓存时出错:\n{e.Message}", "确定");
            }
        }

        void OnGUI()
        {
            GUILayout.Label("Addressables重复键修复工具", EditorStyles.boldLabel);
            GUILayout.Space(10);

            GUILayout.Label("问题症状:");
            GUILayout.Label("• ArgumentException: An item with the same key has already been added");
            GUILayout.Label("• Key: [某个哈希值]");
            GUILayout.Label("• AddressableAssetGroup.ResetEntryMap错误");
            GUILayout.Space(10);

            GUILayout.Label("修复步骤:", EditorStyles.boldLabel);
            GUILayout.Label("1. 点击下面的按钮清理缓存");
            GUILayout.Label("2. 重新启动Unity编辑器");
            GUILayout.Label("3. 重新构建Addressables（如果需要）");
            GUILayout.Space(10);

            if (GUILayout.Button("清理Addressables缓存", GUILayout.Height(30)))
            {
                if (EditorUtility.DisplayDialog("确认清理", "这将删除所有Addressables相关缓存\n确定要继续吗？", "确定", "取消"))
                {
                    ClearAddressablesCache();
                }
            }

            GUILayout.Space(10);
            
            if (GUILayout.Button("强制刷新资产数据库", GUILayout.Height(25)))
            {
                AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
                Debug.Log("✅ 已强制刷新资产数据库");
            }

            GUILayout.Space(10);
            GUILayout.Label("注意: 清理后建议重启Unity编辑器以确保完全生效", EditorStyles.helpBox);
        }
    }
} 