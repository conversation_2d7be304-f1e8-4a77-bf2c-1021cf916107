// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTypo
// ReSharper disable CommentTypo

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.ChangeBubbling;
using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using CsvTables;

using Cysharp.Threading.Tasks;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using ViewModel;

using X.PB;

/// <summary>
///     刷怪器
/// </summary>
public class MonsterSpawner : IDisposable
{
    /// <summary>
    ///     上次刷怪的时间戳（毫秒）
    /// </summary>
    private static long LastBrushTimeMs = 0;
    
    /// <summary>
    ///     不同刷怪ID之间的最小间隔时间（秒）
    /// </summary>
    private const float MinIntervalBetweenBrushes = 0.3f;

    /// <summary>
    ///     最后一波怪物刷出后自动结束波次的计时器（秒）
    /// </summary>
    private float autoCompleteWaveTimer = 0f;
    
    /// <summary>
    ///     结算检测计时器（秒）
    /// </summary>
    private float settlementCheckTimer = 0f;
    
    /// <summary>
    ///     最后一波怪物是否至少刷出过一只
    /// </summary>
    private bool hasSpawnedLastWaveMonster = false;
    
    /// <summary>
    ///     是否已经自动完成了当前波次
    /// </summary>
    private bool hasAutoCompletedWave = false;
    
    /// <summary>
    ///     当前刷怪位置索引
    /// </summary>
    private int currentBrushIndex = 0;

    /// <summary>
    ///     每个刷怪ID实际刷出的怪物数量
    /// </summary>
    private Dictionary<int, int> spawnedMonsterCounts = new();

    /// <summary>
    ///     刷怪检测任务的取消令牌
    /// </summary>
    private CancellationTokenSource CTS_CheckMonsters = new();

    /// <summary>
    ///     当前回合的刷怪列表
    /// </summary>
    private List<BattleBrushEnemy.Item> currentRoundBrushList = null;

    /// <summary>
    ///     任务的取消令牌:刷怪
    /// </summary>
    public CancellationTokenSource CTS_Brush { get; protected set; } = new();

    /// <summary>
    ///     待刷的怪(队列)
    /// </summary>
    public BubblingList<BornMonster> MonsterQueue { get; } = new(nameof(MonsterQueue));

    /// <summary>
    ///     是否排完了
    /// </summary>
    public BubblingList<bool> IsBrushFinish { get; } = new(nameof(IsBrushFinish));

    /// <summary>
    ///     启动刷怪(一回合)
    /// </summary>
    public async UniTaskVoid StartBrush()
    {
        // Debug.Log($"1111111 开始启动刷怪, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        // 先停止之前可能存在的刷怪任务
        StopBrush();

        // 确保所有状态正确初始化
        CTS_Brush = new CancellationTokenSource();
        CTS_CheckMonsters = new CancellationTokenSource();

        // 重置标记和计时器
        hasSpawnedLastWaveMonster = false;
        hasAutoCompletedWave = false;
        autoCompleteWaveTimer = 0f;
        settlementCheckTimer = 0f;
        currentBrushIndex = 0;
        LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        IsBrushFinish.Value = false;

        // 先启动刷怪监控任务
        CancellationToken token = CTS_Brush.Token;
        Task_BrushMonster(token).Forget();

        // 获取当前回合的刷怪列表
        try
        {
            var stageValue = SingletonMgr.Instance.BattleMgr.StageMgr.CsvRow_Stage.Value;
            int roundNo = SingletonMgr.Instance.BattleMgr.Actor.RoundNo.Value;

            // Debug.Log($"1111111 尝试获取刷怪列表: 关卡ID={stageValue?.Id ?? 0}, 回合={roundNo}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");

            var allRoundLists = SingletonMgr.Instance.GlobalMgr.ListBrushMonster_Stage(stageValue);
            if (allRoundLists != null && allRoundLists.ContainsKey(roundNo))
            {
                currentRoundBrushList = allRoundLists[roundNo];
                
                if (currentRoundBrushList == null)
                {
                    // Debug.LogError($"1111111 刷怪列表为空: 回合={roundNo}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    return;
                }
            }
            else
            {
                // Debug.LogError($"1111111 未找到本回合的刷怪列表: 回合={roundNo}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                return;
            }
        }
        catch (Exception ex)
        {
            // Debug.LogError($"1111111 获取刷怪列表异常: {ex.Message}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            return;
        }

        // 输出本回合所有刷怪配置的信息
        // Debug.Log($"1111111 当前回合刷怪配置: 回合={SingletonMgr.Instance.BattleMgr.Actor.RoundNo.Value}, " +
                  // $"刷怪配置数量={currentRoundBrushList.Count}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                  
        // 输出每个刷怪配置的详细信息
        for (int i = 0; i < currentRoundBrushList.Count; i++)
        {
            var item = currentRoundBrushList[i];
            // Debug.Log($"1111111 刷怪配置[{i}]: ID={item.Id}, 名称={item.EnemyName}, " +
                      // $"数量={item.Count}, 屏幕上限={item.CountOnScreen}, " +
                      // $"阈值={item.AntiCriticalStrike}, 同时刷数={item.BrushNum}, " +
                      // $"延迟={item.DelayTime}秒, 回合={item.CriticalStrike}, " +
                      // $"区域数量={(item.BrushAreas != null ? item.BrushAreas.Length/2 : 0)}, 原始区域数据={string.Join(",", item.BrushAreas ?? Array.Empty<float>())}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }

        // 初始化怪物计数字典
        spawnedMonsterCounts.Clear();
        foreach (var item in currentRoundBrushList)
        {
            spawnedMonsterCounts[item.Id] = 0;
        }

        // 启动定期检查怪物数量的任务
        Task_CheckMonstersEveryTwoSeconds(CTS_CheckMonsters.Token).Forget();

        // 先缓存所有待刷怪物的附着属性
        SingletonMgr.Instance.GlobalMgr.GetMonsterPropIds(currentRoundBrushList.ToArray());

        // 当前回合的刷怪配置同时开启子任务
        // Debug.Log($"1111111 开始执行所有刷怪任务, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        try
        {
            // 创建刷怪任务列表
            List<UniTask> tasks = new List<UniTask>();
            foreach (var item in currentRoundBrushList)
            {
                tasks.Add(Task_BrushOneRow(token, item));
            }
            
            // 等待所有刷怪任务完成
            await UniTask.WhenAll(tasks);
            
            if (token.IsCancellationRequested)
            {
                // Debug.Log($"1111111 刷怪任务被取消, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                return;
            }

            // Debug.Log($"1111111 所有刷怪任务完成, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            IsBrushFinish.Value = true;
        }
        catch (Exception ex)
        {
            // Debug.LogError($"1111111 执行刷怪任务异常: {ex.Message}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }
    }

    /// <summary>
    ///     停止刷怪
    /// </summary>
    public void StopBrush()
    {
        // Debug.Log($"1111111 停止刷怪, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        try
        {
            if (CTS_Brush != null && !CTS_Brush.IsCancellationRequested)
            {
                CTS_Brush.Cancel();
                CTS_Brush.Dispose();
            }
            
            if (CTS_CheckMonsters != null && !CTS_CheckMonsters.IsCancellationRequested)
            {
                CTS_CheckMonsters.Cancel();
                CTS_CheckMonsters.Dispose();
            }
        }
        catch (Exception ex)
        {
            // Debug.Log($"1111111 停止刷怪异常: {ex.Message}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            // it is ok to ignore potential issues with cancellation token
        }
        
        MonsterQueue.Clear();
        spawnedMonsterCounts.Clear();
        currentRoundBrushList = null;
        autoCompleteWaveTimer = 0f;
        settlementCheckTimer = 0f;
        hasSpawnedLastWaveMonster = false;
        hasAutoCompletedWave = false;
        
        // Debug.Log($"1111111 刷怪已完全停止, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
    }

    /// <summary>
    ///     出生一个怪物(添加到待刷队列中)
    /// </summary>
    /// <param name="csvRow_Brush">怪物配置</param>
    /// <param name="pos">
    ///     刷怪位置(没传则以配置为准,最多取第一个值)
    ///     (依次替补?)
    /// </param>
    /// <param name="reviveCount">已重生次数</param>
    public void BornMonster(BattleBrushEnemy.Item csvRow_Brush, List<Vector3> pos = null,
        int reviveCount = 0)
    {
        // Debug.Log($"1111111 准备生成怪物: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}, " +
                  // $"位置={string.Join(",", pos?.Select(p => $"({p.x},{p.y},{p.z})") ?? new List<string>())}, " +
                  // $"重生次数={reviveCount}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                  
        // 更新当前ID的已刷怪物数量
        if (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id))
        {
            spawnedMonsterCounts[csvRow_Brush.Id]++;
        }
        else
        {
            spawnedMonsterCounts[csvRow_Brush.Id] = 1;
        }

        try
        {
            BornMonster bornMonster = new()
            {
                CsvRow_Brush = csvRow_Brush,
                MonsterThing = new MonsterThing(nameof(MonsterThing))
                {
                    CsvRow_BattleBrushEnemy = csvRow_Brush,
                    // 怪物固定为1级
                    ThingLvl = { Value = 1 },
                    Hp = { Value = csvRow_Brush.Hp },
                    Armor = { Value = csvRow_Brush.Armor },
                    // PositionInit = pos,
                    ReviveCount = reviveCount
                }
            };

            if (pos is { Count: > 0 })
            {
                bornMonster.MonsterThing.Position = bornMonster.MonsterThing.PositionInit = pos.First();
                // Debug.Log($"1111111 设置怪物位置: ID={csvRow_Brush.Id}, 位置=({pos.First().x}, {pos.First().y}, {pos.First().z})");
            }
            else
            {
                // Debug.LogWarning($"1111111 怪物位置为空: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}");
            }

            List<GunItem> gunList = new() { 
                new() { GunId = csvRow_Brush.SkillID, GunLvl = 1, StarCount = -1 },
                new() { GunId = csvRow_Brush.TurnSpeed, GunLvl = 1, StarCount = -1 }
            };

            // Debug.Log($"1111111 创建怪物枪: ID={csvRow_Brush.Id}, 技能ID={csvRow_Brush.SkillID}, 转速={csvRow_Brush.TurnSpeed}");

            // 加载怪物附着的属性
            bornMonster.MonsterThing.ReloadAttachedProps();

            // 创建枪及附着的属性
            bornMonster.MonsterThing.ReCreateGuns(gunList);

            bornMonster.MonsterThing.ReCalcHoistAndTotal_All();

            // Debug.Log($"1111111 将怪物加入刷新队列: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}, " +
                      // $"队列中现有怪物数={MonsterQueue.Count}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");

            // 如果是最后一波怪物，标记已刷出
            if (currentRoundBrushList != null && 
                currentRoundBrushList.Count > 0 && 
                csvRow_Brush.Id == currentRoundBrushList.Last().Id)
            {
                hasSpawnedLastWaveMonster = true;
                // Debug.Log($"1111111 标记最后一波怪物已刷出: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            }
            
            // 添加到队列
            MonsterQueue.Add(bornMonster);
            // Debug.Log($"1111111 怪物已成功添加到队列: ID={csvRow_Brush.Id}, 队列长度={MonsterQueue.Count}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }
        catch (Exception ex)
        {
            // Debug.LogError($"1111111 创建怪物异常: ID={csvRow_Brush.Id}, 异常={ex.Message}, 堆栈={ex.StackTrace}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }
    }

    /// <summary>
    ///     在指定位置附近一定范围内，出生新的怪物
    /// </summary>
    /// <param name="pos">出生位置</param>
    /// <param name="maxDistance">随机范围的最大距离</param>
    /// <param name="brushIds">刷怪Id</param>
    /// <param name="lstBornNum">出生数量</param>
    /// <param name="reviveCount">新怪物的已重生次数</param>
    public void BornMonster(Vector3 pos, float maxDistance, IList<int> brushIds, IList<int> lstBornNum,
        int reviveCount = 0)
    {
        // Debug.Log($"1111111 开始批量生成怪物: 位置={pos}, 最大距离={maxDistance}, " +
                  // $"刷怪ID数量={brushIds.Count}, 数量列表长度={lstBornNum.Count}, " +
                  // $"重生次数={reviveCount}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        bool isRoundEnd = SingletonMgr.Instance.BattleMgr.Monsters.Count <= 0 && SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner is
        { IsBrushFinish: { Value: true } } && SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.MonsterQueue.Count == 0;
        
        if (isRoundEnd) 
        {
            // Debug.Log($"1111111 回合已结束，不再生成怪物, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            return;
        }
        
        _ = brushIds.Select((id, i) =>
        {
            // Debug.Log($"1111111 尝试生成怪物ID={id}, 索引={i}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            
            if (SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BattleBrushEnemyCsv>().Dic
                .TryGetValue(id, out BattleBrushEnemy.Item csvRow))
            {
                int rebirthCount = lstBornNum.IndexOf_ByCycle(i);
                // Debug.Log($"1111111 找到怪物配置: ID={id}, 名称={csvRow.EnemyName}, 重生数量={rebirthCount}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                
                for (int m = 0; m < rebirthCount; m++)
                {
                    // 重生位置:在 怪物死亡位置，半径在[0,maxDistance)的圆内 随机
                    Vector3 newPos = pos;
                    float distance = RandomNum.RandomFloat(0, maxDistance);
                    float rndAngle = RandomNum.RandomFloat(0, 360);
                    Vector3 dir = Vector3.right.RotateAround(Vector3.forward, rndAngle);
                    newPos += distance * dir;
                    
                    // Debug.Log($"1111111 生成第{m+1}个怪物: ID={id}, 名称={csvRow.EnemyName}, " +
                              // $"原始位置={pos}, 新位置={newPos}, 距离={distance}, 角度={rndAngle}, " +
                              // $"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");

                    BornMonster(csvRow, new List<Vector3> { newPos, pos }, reviveCount);
                }
            }
            else
            {
                // Debug.LogWarning($"1111111 未找到怪物配置: ID={id}, 索引={i}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            }

            return i;
        }).ToList();
    }

    /// <summary>
    ///     修复GetSpawnPositions方法，确保可以正确获取刷怪位置
    /// </summary>
    private List<Vector2> GetSpawnPositions(BattleBrushEnemy.Item brushItem)
    {
        List<Vector2> lstPos = new();
        
        // 如果BrushAreas为空，尝试使用默认位置
        if (brushItem.BrushAreas == null || brushItem.BrushAreas.Length == 0)
        {
            // Debug.LogWarning($"1111111 怪物ID={brushItem.Id}的BrushAreas为空，尝试使用默认位置");
            
            // 使用地图中心点作为默认位置
            Vector3 defaultPos = Vector3.zero;
            if (SingletonMgr.Instance.BattleMgr.StageRound.Map != null)
            {
                Transform mapTransform = SingletonMgr.Instance.BattleMgr.StageRound.Map.transform;
                if (mapTransform != null)
                {
                    defaultPos = mapTransform.position;
                    // Debug.Log($"1111111 使用地图中心点作为默认刷怪位置: ({defaultPos.x}, {defaultPos.y})");
                    lstPos.Add(new Vector2(defaultPos.x, defaultPos.y));
                }
            }
            
            // 如果还是没有位置，使用玩家位置作为默认位置
            if (lstPos.Count == 0 && SingletonMgr.Instance.BattleMgr.PlayerActor != null)
            {
                Vector3 playerPos = SingletonMgr.Instance.BattleMgr.PlayerActor.transform.position;
                // 在玩家周围随机一个位置，避免直接在玩家身上刷怪
                float radius = 5f;  // 5米范围
                float angle = UnityEngine.Random.Range(0, 360) * Mathf.Deg2Rad;
                Vector3 offset = new Vector3(Mathf.Cos(angle) * radius, Mathf.Sin(angle) * radius, 0);
                Vector3 spawnPos = playerPos + offset;
                
                // Debug.Log($"1111111 使用玩家位置附近作为默认刷怪位置: 玩家位置=({playerPos.x}, {playerPos.y}), 刷怪位置=({spawnPos.x}, {spawnPos.y})");
                lstPos.Add(new Vector2(spawnPos.x, spawnPos.y));
            }
            
            return lstPos;
        }
        
        // 记录原始数据
        // Debug.Log($"1111111 原始刷怪区域数据: ID={brushItem.Id}, BrushAreas=[{string.Join(",", brushItem.BrushAreas)}], 长度={brushItem.BrushAreas.Length}");
        
        try
        {
            for (int i = 0; i < brushItem.BrushAreas.Length; i += 2)
            {
                if (i + 1 < brushItem.BrushAreas.Length)
                {
                    lstPos.Add(new Vector2((float)brushItem.BrushAreas[i], (float)brushItem.BrushAreas[i + 1]));
                    // Debug.Log($"1111111 添加刷怪位置: ID={brushItem.Id}, 索引={i/2}, 坐标=({brushItem.BrushAreas[i]},{brushItem.BrushAreas[i+1]})");
                }
            }
        }
        catch (Exception ex)
        {
            // Debug.LogError($"1111111 处理刷怪区域异常: {ex.Message}, ID={brushItem.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }
        
        // Debug.Log($"1111111 获取到的刷怪位置数量: ID={brushItem.Id}, 数量={lstPos.Count}");
        return lstPos;
    }

    /// <summary>
    ///     延时后刷一波怪
    /// </summary>
    protected async UniTask Task_BrushOneRow(CancellationToken token,
        BattleBrushEnemy.Item csvRow_Brush)
    {
        // Debug.Log($"1111111 开始执行刷怪任务: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        // 从回合开始起延时刷怪(毫秒)
        int delay = csvRow_Brush.PhysicsDefense;
        // Debug.Log($"1111111 延时刷怪: ID={csvRow_Brush.Id}, 延时={delay}毫秒, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        await UniTask.Delay(delay, cancellationToken: token);
        if (token.IsCancellationRequested)
        {
            // ////Debug.Log($"1111111 刷怪任务已取消: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            return;
        }

        // 场上怪物数量<=该值时才开始刷怪
        //Debug.Log($"1111111 等待场上怪物数量符合条件: ID={csvRow_Brush.Id}, 当前怪物数={SingletonMgr.Instance.BattleMgr.Monsters.Count}, 阈值={csvRow_Brush.AntiCriticalStrike}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        await UniTask.WaitUntil(() =>
                SingletonMgr.Instance.BattleMgr.Monsters.Count <= csvRow_Brush.AntiCriticalStrike,
            cancellationToken: token);
        if (token.IsCancellationRequested)
        {
            //Debug.Log($"1111111 刷怪任务已取消: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            return;
        }
        //Debug.Log($"1111111 场上怪物数量已符合条件: ID={csvRow_Brush.Id}, 当前怪物数={SingletonMgr.Instance.BattleMgr.Monsters.Count}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");

        // 如果是最后一波怪并且当前波数UI不满足条件，则等待条件满足
        if (IsLastBrushId(csvRow_Brush.Id) && !ShouldSpawnLastWaveMonster())
        {
            //Debug.Log($"1111111 等待最后一波刷怪条件满足: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            await UniTask.WaitUntil(ShouldSpawnLastWaveMonster, cancellationToken: token);
            if (token.IsCancellationRequested)
            {
                //Debug.Log($"1111111 刷怪任务已取消: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                return;
            }
            //Debug.Log($"1111111 最后一波刷怪条件已满足: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }

        // 获取刷怪位置
        List<Vector2> lstPos = GetSpawnPositions(csvRow_Brush);
        
        // 记录刷怪位置的日志
        string posLog = "";
        for (int i = 0; i < System.Math.Min(5, lstPos.Count); i++)
        {
            posLog += $"({lstPos[i].x},{lstPos[i].y})";
            if (i < System.Math.Min(5, lstPos.Count) - 1) posLog += ", ";
        }
        if (lstPos.Count > 5) posLog += "...";
        
        //Debug.Log($"1111111 刷怪位置列表: ID={csvRow_Brush.Id}, 位置数量={lstPos.Count}, 位置=[{posLog}], " +
                  //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                  
        // 添加日志显示BrushAreas原始数据
        string brushAreaText = "";
        if (csvRow_Brush.BrushAreas != null)
        {
            for (int i = 0; i < System.Math.Min(10, csvRow_Brush.BrushAreas.Length); i++)
            {
                brushAreaText += csvRow_Brush.BrushAreas[i];
                if (i < System.Math.Min(10, csvRow_Brush.BrushAreas.Length) - 1) brushAreaText += ",";
            }
            if (csvRow_Brush.BrushAreas.Length > 10) brushAreaText += "...";
        }
        //Debug.Log($"1111111 原始刷怪区域: ID={csvRow_Brush.Id}, BrushAreas=[{brushAreaText}], " +
                  //$"长度={csvRow_Brush.BrushAreas?.Length ?? 0}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        // 这波已刷怪数量
        int monsterCount = 0;
        for (;; await UniTask.NextFrame())
        {
            if (token.IsCancellationRequested)
            {
                break;
            }

            if (Time.deltaTime <= 0)
            {
                continue;
            }

            // 检查是否已达到应该刷的数量
            if (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id) && 
                spawnedMonsterCounts[csvRow_Brush.Id] >= csvRow_Brush.Count)
            {
                //Debug.Log($"1111111 刷怪数量已达上限: ID={csvRow_Brush.Id}, " +
                          //$"已刷数量={spawnedMonsterCounts[csvRow_Brush.Id]}/{csvRow_Brush.Count}, " +
                          //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                break;
            }

            // 如果是最后一波怪物，检查条件
            if (IsLastBrushId(csvRow_Brush.Id) && !ShouldSpawnLastWaveMonster())
            {
                // 如果这波已经至少刷了一只，并且不应该继续刷，则退出
                if (spawnedMonsterCounts[csvRow_Brush.Id] > 0)
                {
                    // Debug.Log($"1111111 最后一波条件不满足，暂停刷怪: ID={csvRow_Brush.Id}, " +
                    //           //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    break;
                }
                
                // 否则等待条件满足
                await UniTask.Delay(500, cancellationToken: token);
                continue;
            }

            // 检查与上次刷怪的时间间隔，确保严格符合最小间隔要求
            long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
            
            // 如果距离上次刷怪时间小于设定的间隔，则等待
            if (elapsedTimeMs < MinIntervalBetweenBrushes * 300)
            {
                int waitTimeMs = (int)(MinIntervalBetweenBrushes * 300 - elapsedTimeMs);
                await UniTask.Delay(waitTimeMs, cancellationToken: token);
                if (token.IsCancellationRequested)
                {
                    break;
                }
                
                // 重新获取当前时间，确保间隔准确
                currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            }

            // 确保当前索引在刷怪位置列表的有效范围内
            if (lstPos.Count > 0)
            {
                currentBrushIndex = currentBrushIndex % lstPos.Count;
            }
            else
            {
                currentBrushIndex = 0;
            }

            // 波内同时刷多个怪
            for (int i = 0; i < csvRow_Brush.BrushNum; i++)
            {
                // 如果这波已经刷够数量，则退出
                if (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id) && 
                    spawnedMonsterCounts[csvRow_Brush.Id] >= csvRow_Brush.Count)
                {
                    // Debug.Log($"1111111 刷怪数量已达上限: ID={csvRow_Brush.Id}, " +
                    //           $"已刷数量={spawnedMonsterCounts[csvRow_Brush.Id]}/{csvRow_Brush.Count}, " +
                    //           $"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    break;
                }

                // 如果是最后一波怪物，再次检查条件
                if (IsLastBrushId(csvRow_Brush.Id) && !ShouldSpawnLastWaveMonster())
                {
                    //Debug.Log($"1111111 最后一波条件不满足，暂停刷怪: ID={csvRow_Brush.Id}, " +
                              //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    break;
                }
                
                // 从所有刷怪位置中顺序取一个位置
                if (lstPos.Count > 0 && currentBrushIndex < lstPos.Count)
                {
                    Vector2 pos2 = lstPos[currentBrushIndex];
                    currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
                    List<Vector3> pos = new() { pos2 };
                    
                    // 更新最后刷怪时间
                    LastBrushTimeMs = currentTimeMs;
                    
                    // Debug.Log($"1111111 创建怪物: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}, " +
                    //           //$"位置索引={currentBrushIndex-1}, 位置=({pos2.x},{pos2.y}), " +
                    //           //$"已刷数量={monsterCount}/{csvRow_Brush.Count}, " +
                    //           //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    
                    BornMonster(csvRow_Brush, pos);
                    monsterCount++;
                    
                    // 每次只刷一个，确保时间间隔
                    break;
                }
                
                // 如果没有配置刷怪位置，则跳出循环
                if (lstPos.Count <= 0)
                {
                    // Debug.Log($"1111111 没有配置刷怪位置: ID={csvRow_Brush.Id}, " +
                    //           //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    break;
                }
            }

            // 这波刷完了指定数量，退出
            if (monsterCount >= csvRow_Brush.Count || 
                (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id) && 
                 spawnedMonsterCounts[csvRow_Brush.Id] >= csvRow_Brush.Count))
            {
                // Debug.Log($"1111111 刷怪完成: ID={csvRow_Brush.Id}, " +
                //           //$"已刷数量={spawnedMonsterCounts[csvRow_Brush.Id]}/{csvRow_Brush.Count}, " +
                //           //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                break;
            }

            // 波内延时时长(秒)，但确保不小于最小间隔
            float delayTime = (float)System.Math.Max(float.Parse(csvRow_Brush.DelayTime), MinIntervalBetweenBrushes);
            // Debug.Log($"1111111 波内延时: ID={csvRow_Brush.Id}, 延时={delayTime}秒, " +
            //           //$"已刷数量={spawnedMonsterCounts[csvRow_Brush.Id]}/{csvRow_Brush.Count}, " +
            //           //$"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            await UniTask.Delay(TimeSpan.FromSeconds(delayTime), cancellationToken: token);
        }
    }

    /// <summary>
    ///     监视待刷的怪，刷出来
    /// </summary>
    protected async UniTaskVoid Task_BrushMonster(CancellationToken token)
    {
        // 用于控制检查频率的计数器
        int frameCounter = 0;
        const int CHECK_INTERVAL = 10; // 每10帧检查一次
        
        // Debug.Log($"1111111 怪物生成监视任务启动, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        for (;; await UniTask.NextFrame())
        {
            if (token.IsCancellationRequested)
            {
                // Debug.Log($"1111111 怪物生成监视任务取消, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                break;
            }

            if (Time.deltaTime <= 0)
            {
                continue;
            }
            
            // 增加帧计数器
            frameCounter++;
            
            // 降低自动完成波次和结算检测的频率，每CHECK_INTERVAL帧执行一次
            if (frameCounter % CHECK_INTERVAL == 0)
            {
                CheckAutoCompleteWave();
                CheckSettlement();
            }

            BornMonster bornMonster = MonsterQueue.Value;
            if (bornMonster == null)
            {
                continue;
            }

            // 怪物总数限制
            if (SingletonMgr.Instance.BattleMgr.Monsters.Count >= bornMonster.CsvRow_Brush.CountOnScreen)
            {
                // Debug.Log($"1111111 刷怪受限: 当前场上怪物数量={SingletonMgr.Instance.BattleMgr.Monsters.Count}, " +
                //           $"限制数量={bornMonster.CsvRow_Brush.CountOnScreen}, ID={bornMonster.CsvRow_Brush.Id}, " +
                //           $"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                continue;
            }
            
            // 刷怪阈值限制：只有当场上怪物数量小于等于AntiCriticalStrike值时才刷新怪物
            if (SingletonMgr.Instance.BattleMgr.Monsters.Count > bornMonster.CsvRow_Brush.AntiCriticalStrike)
            {
                // Debug.Log($"1111111 刷怪受限: 当前场上怪物数量={SingletonMgr.Instance.BattleMgr.Monsters.Count}, " +
                //           $"阈值={bornMonster.CsvRow_Brush.AntiCriticalStrike}, ID={bornMonster.CsvRow_Brush.Id}, " +
                //           $"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                continue;
            }

            // 计算怪物出生点
            bornMonster.MonsterThing.Position = bornMonster.MonsterThing.CalcPositionInit();

            // 记录怪物实际生成日志
            BattleBrushEnemy.Item csvRow_Brush = bornMonster.CsvRow_Brush;
            // Debug.Log($"1111111 怪物生成: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}, " +
            //           $"位置={bornMonster.MonsterThing.Position}, 回合={csvRow_Brush.CriticalStrike}, " +
            //           $"场上怪物数量={SingletonMgr.Instance.BattleMgr.Monsters.Count}, " +
            //           $"时间={DateTime.Now.ToString("HH:mm:ss.fff")}");

            // 通知界面出生此怪物
            try
            {
                // Debug.Log($"1111111 发布怪物生成消息: ID={csvRow_Brush.Id}, 名称={csvRow_Brush.EnemyName}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                MessageBroker.Default.Publish(bornMonster);
                // Debug.Log($"1111111 怪物生成消息已发布: ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            }
            catch (Exception ex)
            {
                // Debug.LogError($"1111111 发布怪物生成消息异常: {ex.Message}, ID={csvRow_Brush.Id}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            }

            // 从待刷列表中移除
            MonsterQueue.Remove(bornMonster);
        }
    }
    
    /// <summary>
    ///     检查自动完成波次
    /// </summary>
    private void CheckAutoCompleteWave()
    {
        if (hasAutoCompletedWave)
            return;
            
        // 如果还没有刷过最后一波的怪物，则不启动自动完成计时
        if (!hasSpawnedLastWaveMonster)
            return;
            
        // 每次进入增加计时器
        autoCompleteWaveTimer += Time.deltaTime;
        
        // 如果最后一波怪物刷出来超过10秒，并且当前没有怪物，则自动完成当前波次
        if (autoCompleteWaveTimer >= 10f && 
            SingletonMgr.Instance.BattleMgr.Monsters.Count <= 0 && 
            MonsterQueue.Count <= 0)
        {
            hasAutoCompletedWave = true;
            IsBrushFinish.Value = true;
        }
    }
    
    /// <summary>
    ///     检查是否应该结算
    /// </summary>
    private void CheckSettlement()
    {
        // 已经刷完并且没有怪物情况下，增加计时器
        if (IsBrushFinish.Value && 
            SingletonMgr.Instance.BattleMgr.Monsters.Count <= 0 && 
            MonsterQueue.Count <= 0)
        {
            settlementCheckTimer += Time.deltaTime;
            
            // 如果满足条件超过2秒，触发结算
            if (settlementCheckTimer >= 2f)
            {
                settlementCheckTimer = 0f;
                
                // 如果是最后一回合且应该自动结算
                int roundNo = SingletonMgr.Instance.BattleMgr.Actor.RoundNo.Value;
                var csvRow_Stage = SingletonMgr.Instance.BattleMgr.StageMgr.CsvRow_Stage.Value;
                var lstBrushes = SingletonMgr.Instance.GlobalMgr.ListBrushMonster_Stage(csvRow_Stage);
                
                int maxRound = lstBrushes.Keys.Max();
                if (roundNo >= maxRound)
                {
                    // 最后一回合结算处理...
                    // 这里不实现结算逻辑，因为不在需求范围内
                }
            }
        }
        else
        {
            // 重置计时器
            settlementCheckTimer = 0f;
        }
    }
    
    /// <summary>
    ///     检查是否是最后一波的刷怪ID
    /// </summary>
    private bool IsLastBrushId(int brushId)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return false;
            
        return brushId == currentRoundBrushList.Last().Id;
    }
    
    /// <summary>
    ///     获取当前允许的场上怪物数量上限
    /// </summary>
    private int GetCurrentMonsterCountLimit()
    {
        // 基本限制20只
        int limit = 20;
        
        // 根据UI波数进度计算
        int x = 0, y = 0;
        GetWaveNumberFromUI(out x, out y);
        
        // 根据波数调整限制
        if (x < y / 2)
        {
            limit = 10;
        }
        else if (x < y * 0.75f)
        {
            limit = 15;
        }
        
        return limit;
    }
    
    /// <summary>
    ///     从UI获取当前波数进度
    /// </summary>
    private void GetWaveNumberFromUI(out int x, out int y)
    {
        x = 1; // 默认值设为1，避免出现0或负数
        y = 1; // 默认值设为1，避免除0错误
        
        try
        {
            // 从CatMainStage获取总波数配置
            if (SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage != null && 
                SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s != null && 
                SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s.Count > 0)
            {
                if (int.TryParse(SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s[0], out int yValue) && yValue > 0)
                {
                    y = yValue;
                    
                    // 从CatMainStage获取K值配置
                    if (SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s.Count > 1)
                    {
                        if (int.TryParse(SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s[1], out int k) && k > 0)
                        {
                            // 获取击杀数
                            int g = SingletonMgr.Instance.BattleMgr.KillCount.Value;
                            
                            // 避免除零错误
                            float kDivY = (k <= 0 || y <= 0) ? 1f : (k / (float)y);
                            if (kDivY <= 0) kDivY = 1f;
                            
                            // 计算当前波数，确保结果在合理范围内
                            x = System.Math.Max(1, System.Math.Min((int)(g / kDivY + 1), y));
                        }
                    }
                }
            }
            
            // Debug.Log($"1111111 当前波数进度: X={x}, Y={y}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        }
        catch (Exception ex)
        {
            // Debug.LogError($"1111111 获取波数进度异常: {ex.Message}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            // 异常情况下设置默认值
            x = 1;
            y = 1;
        }
    }
    
    /// <summary>
    ///     是否应该刷最后一波的怪物
    /// </summary>
    private bool ShouldSpawnLastWaveMonster()
    {
        // 获取当前UI波数进度
        int x = 0, y = 0;
        GetWaveNumberFromUI(out x, out y);
        
        // 如果y<=1，则总是允许刷最后一波怪物
        if (y <= 1)
            return true;
        
        // 如果X是异常值，设为正常值避免卡住
        if (x < 0 || x > 1000) 
        {
            // Debug.LogWarning($"1111111 波数X值异常: {x}，强制设为1，时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            x = 1;
        }
            
        // 当UI显示的当前波次x等于或接近总波次y时，才应该刷最后一波怪物
        // 添加容错，如果x大于y的80%就可以刷最后一波
        bool shouldSpawn = x >= (int)(y * 0.8f);
        // Debug.Log($"1111111 是否应刷最后一波: {shouldSpawn}, X={x}, Y={y}, 阈值={(int)(y * 0.8f)}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        return shouldSpawn;
    }

    /// <summary>
    ///     每2秒检查场上怪物数量，在需要时重新刷怪
    /// </summary>
    protected async UniTaskVoid Task_CheckMonstersEveryTwoSeconds(CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(2), cancellationToken: token);
            if (token.IsCancellationRequested)
            {
                break;
            }
            
            try
            {
                // 如果场上没有怪物并且刷怪已完成但还没有触发结算，则直接跳过刷怪检查
                if (SingletonMgr.Instance.BattleMgr.Monsters.Count <= 0 && 
                    IsBrushFinish.Value && MonsterQueue.Count == 0)
                {
                    continue;
                }
                
                // 获取当前UI波数进度
                int x = 0, y = 0;
                GetWaveNumberFromUI(out x, out y);
                
                // 如果X小于Y且场上怪物数量较少，检查是否需要刷新更多怪物
                if (x < y && SingletonMgr.Instance.BattleMgr.Monsters.Count < GetCurrentMonsterCountLimit())
                {
                    // 检查是否当前有足够的怪物已经在队列中等待刷新
                    if (MonsterQueue.Count >= 5)
                    {
                        continue;
                    }
                    
                    // Debug.Log($"1111111 检查是否需要刷新更多怪物: 当前场上怪物数={SingletonMgr.Instance.BattleMgr.Monsters.Count}, " +
                    //           $"队列中怪物数={MonsterQueue.Count}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    
                    // 先检查是否应该刷最后一波的怪物
                    if (currentRoundBrushList != null && currentRoundBrushList.Count > 0)
                    {
                        int lastId = currentRoundBrushList.Last().Id;
                        
                        // 如果应该刷最后一波怪物
                        if (ShouldSpawnLastWaveMonster())
                        {
                            if (spawnedMonsterCounts.ContainsKey(lastId) && 
                                spawnedMonsterCounts[lastId] < currentRoundBrushList.Last().Count)
                            {
                                // 刷新最后一波的怪物
                                // Debug.Log($"1111111 开始刷新最后一波怪物: ID={lastId}, 已刷数量={spawnedMonsterCounts[lastId]}, " +
                                //           $"目标数量={currentRoundBrushList.Last().Count}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                                await RefreshLastWaveMonster(token);
                            }
                            else
                            {
                                // 最后一波已刷完，但仍需要刷怪，检查其他ID是否还有未刷完的
                                // Debug.Log($"1111111 最后一波已刷完，检查其他未刷完的怪物, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                                await RefreshUnfinishedWaves(token);
                            }
                        }
                        else
                        {
                            // 如果不应该刷最后一波，先尝试刷未刷够数量的怪
                            // Debug.Log($"1111111 不应该刷最后一波，尝试刷新未刷完的怪物, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                            bool hasRefreshed = await RefreshUnfinishedWaves(token);
                            
                            // 如果没有找到未刷够的怪，则从第一波开始重新刷
                            if (!hasRefreshed)
                            {
                                // Debug.Log($"1111111 没有找到未刷完的怪物，从第一波开始重新刷, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                                await RefreshFromFirstWave(token);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Debug.LogError($"1111111 检查刷怪异常: {ex.Message}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            }
        }
    }
    
    /// <summary>
    ///     刷新未刷够数量的怪物配置
    /// </summary>
    /// <returns>是否找到并刷新了怪物</returns>
    private async UniTask<bool> RefreshUnfinishedWaves(CancellationToken token)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return false;
            
        // 查找所有未刷够Count的配置
        foreach (var brushItem in currentRoundBrushList)
        {
            if (!spawnedMonsterCounts.ContainsKey(brushItem.Id) || 
                spawnedMonsterCounts[brushItem.Id] < brushItem.Count)
            {
                // 是最后一波且不应该刷最后一波，则跳过
                if (brushItem.Id == currentRoundBrushList.Last().Id && !ShouldSpawnLastWaveMonster())
                    continue;
                    
                // 检查与上次刷怪时间间隔
                long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
                
                if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
                {
                    int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
                    await UniTask.Delay(waitTimeMs, cancellationToken: token);
                    if (token.IsCancellationRequested)
                        return false;
                }
                
                // 获取刷怪位置
                List<Vector2> lstPos = GetSpawnPositions(brushItem);
                
                if (lstPos.Count > 0)
                {
                    Vector2 pos2 = lstPos[currentBrushIndex % lstPos.Count];
                    currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
                    List<Vector3> pos = new() { pos2 };
                    
                    // 更新最后刷怪时间
                    LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    
                    // 如果是最后一波怪物，标记已刷出
                    if (brushItem.Id == currentRoundBrushList.Last().Id)
                    {
                        hasSpawnedLastWaveMonster = true;
                    }
                    
                    // Debug.Log($"1111111 刷新未刷完的怪物: ID={brushItem.Id}, 已刷数量={spawnedMonsterCounts.GetValueOrDefault(brushItem.Id, 0)}, " +
                    //           $"目标数量={brushItem.Count}, 位置=({pos2.x}, {pos2.y}), 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                    BornMonster(brushItem, pos);
                    return true;
                }
            }
        }
        
        return false;
    }

    /// <summary>
    ///     从第一波开始重新刷怪（当所有怪都已刷够Count时）
    /// </summary>
    private async UniTask RefreshFromFirstWave(CancellationToken token)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return;
            
        // 找到第一个可以刷的ID (不是已经刷完的ID)
        int lastScannedIndex = -1;
        BattleBrushEnemy.Item brushItemToSpawn = null;
        
        // 获取最后一个刷怪的ID (从记录最大的ID值)
        int lastSpawnedId = 0;
        foreach (var kvp in spawnedMonsterCounts)
        {
            if (kvp.Value > 0 && kvp.Key > lastSpawnedId)
                lastSpawnedId = kvp.Key;
        }
        
        // 寻找下一个ID (即列表中ID大于lastSpawnedId的第一个ID)
        bool foundNext = false;
        for (int i = 0; i < currentRoundBrushList.Count; i++)
        {
            var brushItem = currentRoundBrushList[i];
            if (brushItem.Id > lastSpawnedId)
            {
                // 是最后一波且不应该刷最后一波，则跳过
                if (brushItem.Id == currentRoundBrushList.Last().Id && !ShouldSpawnLastWaveMonster())
                    continue;
                    
                brushItemToSpawn = brushItem;
                foundNext = true;
                lastScannedIndex = i;
                // Debug.Log($"1111111 【刷怪轮换】找到下一个ID={brushItem.Id}，上一个ID={lastSpawnedId}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                break;
            }
        }
        
        // 如果没有找到下一个ID，说明已经刷到最后一个ID了，从第一个开始重新刷
        if (!foundNext)
        {
            for (int i = 0; i < currentRoundBrushList.Count; i++)
            {
                // 跳过最后一个ID，如果不应该刷
                if (i == currentRoundBrushList.Count - 1 && !ShouldSpawnLastWaveMonster())
                    continue;
                    
                brushItemToSpawn = currentRoundBrushList[i];
                lastScannedIndex = i;
                // Debug.Log($"1111111 【刷怪轮换】轮回到第一个ID={brushItemToSpawn.Id}，上一个ID={lastSpawnedId}, 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                break;
            }
        }
        
        // 如果找到了需要刷的怪物配置
        if (brushItemToSpawn != null)
        {
            // 检查与上次刷怪时间间隔
            long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
            
            if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
            {
                int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
                await UniTask.Delay(waitTimeMs, cancellationToken: token);
                if (token.IsCancellationRequested)
                    return;
            }
            
            // 获取刷怪位置
            List<Vector2> lstPos = GetSpawnPositions(brushItemToSpawn);
            
            if (lstPos.Count > 0)
            {
                Vector2 pos2 = lstPos[currentBrushIndex % lstPos.Count];
                currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
                List<Vector3> pos = new() { pos2 };
                
                // 更新最后刷怪时间
                LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                
                // 如果是最后一波怪物，重置计时器并标记
                if (brushItemToSpawn.Id == currentRoundBrushList.Last().Id)
                {
                    autoCompleteWaveTimer = 0f;
                    hasSpawnedLastWaveMonster = true;
                }
                
                // 生成怪物
                // Debug.Log($"1111111 【刷怪轮换】开始刷新ID={brushItemToSpawn.Id}，扫描索引={lastScannedIndex}，" + 
                //           $"总波数={currentRoundBrushList.Count}, 位置=({pos2.x}, {pos2.y}), 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
                BornMonster(brushItemToSpawn, pos);
            }
        }
    }

    /// <summary>
    ///     刷新最后一波的怪物
    /// </summary>
    private async UniTask RefreshLastWaveMonster(CancellationToken token)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return;
            
        BattleBrushEnemy.Item lastBrush = currentRoundBrushList.Last();
        
        // 检查是否已刷够
        if (spawnedMonsterCounts.ContainsKey(lastBrush.Id) && 
            spawnedMonsterCounts[lastBrush.Id] >= lastBrush.Count)
            return;
            
        // 检查与上次刷怪时间间隔，确保严格按照最小1秒间隔
        long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
        
        if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
        {
            int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
            await UniTask.Delay(waitTimeMs, cancellationToken: token);
            if (token.IsCancellationRequested)
                return;
        }
        
        // 获取刷怪位置
        List<Vector2> lstPos = GetSpawnPositions(lastBrush);
        
        if (lstPos.Count > 0)
        {
            Vector2 pos2 = lstPos[currentBrushIndex % lstPos.Count];
            currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
            List<Vector3> pos = new() { pos2 };
            
            // 更新最后刷怪时间
            LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            
            // 标记最后一波怪物已刷出
            hasSpawnedLastWaveMonster = true;
            
            // Debug.Log($"1111111 刷新最后一波怪物: ID={lastBrush.Id}, 已刷数量={spawnedMonsterCounts.GetValueOrDefault(lastBrush.Id, 0)}, " +
            //           $"目标数量={lastBrush.Count}, 位置=({pos2.x}, {pos2.y}), 时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
            BornMonster(lastBrush, pos);
        }
    }

    /// <summary>
    ///     从第一波开始重新刷怪
    /// </summary>
    private async UniTask RefreshMonsterFromFirstWave(CancellationToken token)
    {
        // 优先使用RefreshUnfinishedWaves查找并刷新未刷完的怪
        bool hasRefreshed = await RefreshUnfinishedWaves(token);
        
        // 如果所有怪都已刷够，则从第一波开始重新刷
        if (!hasRefreshed)
        {
            await RefreshFromFirstWave(token);
        }
    }

    /// <summary>
    /// 重新从回合第一个怪物ID开始刷怪
    /// </summary>
    public void RestartFromBeginning()
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
        {
            // Debug.LogWarning("1111111 无法重新开始刷怪：当前回合刷怪列表为空");
            return;
        }
        
        // Debug.Log($"1111111 重新从第一个怪物ID开始刷怪，回合={SingletonMgr.Instance.BattleMgr.Actor.RoundNo.Value}，时间={DateTime.Now.ToString("HH:mm:ss.fff")}");
        
        // 重置刷怪状态
        IsBrushFinish.Value = false;
        hasSpawnedLastWaveMonster = false;
        hasAutoCompletedWave = false;
        autoCompleteWaveTimer = 0f;
        settlementCheckTimer = 0f;
        currentBrushIndex = 0;
        
        // 重置怪物计数
        spawnedMonsterCounts.Clear();
        foreach (var item in currentRoundBrushList)
        {
            spawnedMonsterCounts[item.Id] = 0;
        }
        
        // 清空怪物队列
        MonsterQueue.Clear();
        
        // 重新启动刷怪
        StartBrush().Forget();
    }

    #region IDisposable

    protected bool disposedValue;

    /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
    protected virtual void Dispose(bool disposing)
    {
        if (disposedValue)
        {
            return;
        }

        if (disposing)
        {
            StopBrush();
            
            try
            {
                if (CTS_CheckMonsters != null && !CTS_CheckMonsters.IsCancellationRequested)
                {
                    CTS_CheckMonsters.Cancel();
                    CTS_CheckMonsters.Dispose();
                }
            }
            catch (Exception)
            {
                // Ignore cancellation token issues
            }
        }

        // 释放未托管的资源(未托管的对象)并重写终结器
        // 将大型字段设置为 null
        disposedValue = true;
    }

    // // TODO: 仅当"Dispose(bool disposing)"拥有用于释放未托管资源的代码时才替代终结器
    // ~NotifyPropertyChangeBase()
    // {
    //     // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
    //     Dispose(false);
    // }

    public void Dispose()
    {
        // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    #endregion
}