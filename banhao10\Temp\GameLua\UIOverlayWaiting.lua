-- 黑屏等待UI
local luaID = ('UIOverlayWaiting')

local UIOverlayWaiting = {}

-- 初始化
function UIOverlayWaiting:OnCreate()
	self.bgRender = self.objList.Img_BG:GetComponent('CanvasRenderer')
	self.iconTrans = self.objList.Img_Icon.transform
	return true
end

-- 窗口开启
function UIOverlayWaiting:OnOpen(backType, maxTime, timeCallback, desc)
	if backType == 1 then
		self.bgRender.cull = false
	elseif backType == 2 then
		self.bgRender.cull = true
	else
		self.bgRender.cull = false
	end
	
	if desc then
		self.objList.Txt_Desc.text = desc
	else
		self.objList.Txt_Desc.text = ''
	end
	
	self.iconTrans:DOKill()
	self.iconTrans:DOLocalRotate(self.iconTrans.eulerAngles + Vector3(0, 0, 360), 2, DG.Tweening.RotateMode.FastBeyond360):SetEase(TweeningEase.Linear, 1):SetLoops(-1, TweeningLoopType.Restart)
	if self.callBackTimer then
		self.callBackTimer:Stop()
		self.callBackTimer = nil
	end
	self.timeCallback = timeCallback
	if maxTime and maxTime > 0 then
		self.callBackTimer = Timer.New(self.OnTimerCallback, maxTime, 1)
		self.callBackTimer:Start()
	end
end

-- 定时回调
function UIOverlayWaiting.OnTimerCallback()
	local self = UIOverlayWaiting
	self:CloseSelf()
	if self.timeCallback then
		self.timeCallback()
	end
end

-- 窗口关闭
function UIOverlayWaiting:OnClose()
	if self.callBackTimer then
		self.callBackTimer:Stop()
		self.callBackTimer = nil
	end
end

return UIOverlayWaiting