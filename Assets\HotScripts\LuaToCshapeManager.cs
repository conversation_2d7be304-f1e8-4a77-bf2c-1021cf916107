using System.Linq;

using CsvTables;

using DataStructure;

using UnityEngine;

using View;

using X.PB;

public class LuaToCshapeManager : Singleton<LuaToCshapeManager>
{
	/// <summary>
	/// 设置即将开始的关卡并加载进度
	/// </summary>
	public void SetStage(int StageType, int StageLvl)
	{
		SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CatMainStageCsv>()
			.Pb.Items.FirstOrDefault(x => x.FrontType == StageType && x.Id == StageLvl);
	}

	/// <summary>
	/// 清除战斗进度
	/// </summary>
	public void ClearProgress()
	{
		SingletonMgr.Instance.ActorDataMgr.RemoveDataCatalog(
			(int)ActorDataCatalog.BattleProgress,
			(int)ActorDataCatalog.BattlePropIds,
			(int)ActorDataCatalog.BattleHp,
			(int)ActorDataCatalog.BattleBagStatus,
			(int)ActorDataCatalog.BattleBagStatus_GunsInStore,
            (int)ActorDataCatalog.BattleBagGuns,
			(int)ActorDataCatalog.UsedCols_KillImmediately);
	}

	///// <summary>
	///// 从服务器加载战斗进度
	///// </summary>
	///// <param name="cb">(StageType, StageLvl, RoundNo)</param>
	///// <remarks>保存到SingletonMgr.Instance.GlobalMgr.BattleProgress_Server中</remarks>
	//public async UniTaskVoid LoadBattleProgressFromServer(LuaFunction cb = null)
	//{
	//	var ps = new Dictionary<string, string>
	//	{
	//		["ApiVersion"] = "v1",
	//		["ActorId"] = LuaDataSrvClient.Instance.GetActorID().ToString(),
	//	};
	//	var httpRsp = await UnityHttpHelper.GetResponseString(
	//		LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/GetActorBattleProgress_Last",
	//		null, ps);
	//	SingletonMgr.Instance.GlobalMgr.BattleProgress_Server = null;
	//	if (httpRsp.Success)
	//	{
	//		var httpRtn = JsonConvert.DeserializeObject<JsonRtn<ActorBattleProgressDTO>>(httpRsp.Rsp!);
	//		if (httpRtn is { Success: true, Value: { ActorBattleProgress: not null } } &&
	//			httpRtn.Value.ActorBattleProgress.StageType == SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.FrontType &&
	//			httpRtn.Value.ActorBattleProgress.StageLvl == SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id)
	//		{
	//			SingletonMgr.Instance.GlobalMgr.BattleProgress_Server = httpRtn.Value;
	//			cb?.Call(httpRtn.Value.ActorBattleProgress.StageType,
	//				httpRtn.Value.ActorBattleProgress.StageLvl,
	//				httpRtn.Value.ActorBattleProgress.RoundNo);
	//			return;
	//		}
	//	}

	//	cb?.Call();
	//}

	///// <summary>
	///// 清除服务器中保存的战斗进度
	///// </summary>
	//public async UniTaskVoid ClearBattleProgressInServer()
	//{
	//	var progress = new ActorBattleProgressDTO
	//	{
	//		ActorBattleProgress = new ActorBattleProgress
	//		{
	//			ActorId = LuaDataSrvClient.Instance.GetActorID(),
	//			StageType = SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.FrontType,
	//			StageLvl = SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id,
	//			RoundNo = 0,
	//		}
	//	};

	//	// 同时清除本地进度
	//	SingletonMgr.Instance.GlobalMgr.BattleProgress_Server = null;

	//	var url = LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/SaveActorBattleProgress";
	//	var json = JsonConvert.SerializeObject(progress);
	//	await UnityHttpHelper.PostJson(url, json);
	//}

	/// <summary>
	/// 是否战斗状态
	/// </summary>
	public bool IsFighting { get; set; }

	//[HideInInspector] public GameOverMobile gameOver;

	/// <summary>
	/// 广告得到双倍奖励的回调
	/// </summary>
	/// <param name="value"></param>
	public void GetDoubleRewardByAD(int value)
	{
		//gameOver.ADGetDoubleCallback(value == 1);
	}

	public bool IsOnlyBuff
	{
		get
		{
			var battleManager = LuaManager.Instance.LuaState_.GetTable("BattleManager");
			return (bool)battleManager["isOnlyBuff"];
		}
	}

	//加速
	public float AddSpeed(int index)
	{
		if (SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage == null) return Globals.game_speed;
		var diffLevels = SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.DiffLevels;
		if (diffLevels.Length <= 0) return Globals.game_speed;
		if (index >= diffLevels.Length) index = 0;
		Globals.game_speed = diffLevels[index];
		PauseOrResumeBattle(Globals.game_speed);
		return Globals.game_speed;
	}

	/// <summary>
	/// 暂停或继续
	/// </summary>
	public void PauseOrResumeBattle(float value)
	{
		if (Mathf.Approximately(value, 1))
		{
			value = Globals.game_speed;
		}
		Time.timeScale = value;
	}

	/// <summary>
	/// 退出战斗
	/// </summary>
	public void FightQuit()
	{
		PauseOrResumeBattle(1);
		UIMgr.Instance.CloseAllUI();
		SingletonMgr.Instance.BattleMgr.Actor.RoundNo.Value = 0;
		SingletonMgr.Instance.BattleMgr.Actor.SaveProgress();
		LuaManager.Instance.RunLuaFunction("BattleManager.BattleEnd", false);
	}

	/// <summary>
	/// 角色复活(点击复活后)
	/// </summary>
	public void ActorRevive()
	{
		SingletonMgr.Instance.BattleMgr.Actor.ReviveCount++;
		SingletonMgr.Instance.BattleMgr.Actor.Hp.Value =
			SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault();

		// // 清理战场
		// SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.CTS_Brush.Cancel();
		// SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.MonsterQueue.Clear();
		// SingletonMgr.Instance.BattleMgr.Monsters.ToList().ForEach(m =>
		// {
		// 	Destroy(m.ThingBehaviour.gameObject);
		// });
		// SingletonMgr.Instance.BattleMgr.Monsters.Clear();
		// SingletonMgr.Instance.BattleMgr.Actor.Guns.Where(g=>g.IsHidden).ToList().ForEach(g =>
		// {
		// 	Destroy(g.ThingBehaviour.gameObject);
  //
  //           // // 枪也复活
  //           // g.SuspendFightTimes.Value = 0;
  //       });
		// 复活给点金币
		// SingletonMgr.Instance.BattleMgr.Actor.Coins.Value += 30;
		// SingletonMgr.Instance.BattleMgr.Actor.SaveProgress();

		// // 保存当前血量和最大血量
		// var maxHp = SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault();
		// var lstHp = new List<double>
		// {
		// 	maxHp,
		// 	maxHp,
		// };
		// SingletonMgr.Instance.ActorDataMgr.SetDouble((int)ActorDataCatalog.BattleHp, lstHp.ToArray());
		//
		// // 打开背包界面
		// UIMgr.Instance.OpenUI(UIType.FightBag, default, uiObj =>
		// {
		// 	var uiFightBag = uiObj.GetComponent<UIFightBag>();
		// 	if (uiFightBag != null)
		// 	{
		// 		uiFightBag.Init();
		// 		uiFightBag.ResetProgress(true).Forget();
		// 	}
		// });
	}

	/// <summary>
	/// 副本结算
	/// </summary>
	/// <param name="isPassed">是否通关</param>
	public void SettleAccounts(bool isPassed)
	{
		SingletonMgr.Instance.BattleMgr.SettleAccounts(isPassed);
	}
}