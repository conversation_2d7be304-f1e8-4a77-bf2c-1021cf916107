--[[
********************************************************************
    created:    2024/03/29
    author :    李锦剑
    purpose:    购买确认窗口
*********************************************************************
--]]

local luaID = ('UIPurchaseWindows')
--命格商店ID
local FateShop = 2
--消耗类型转换物品ID
local CostType = {
    [1] = 3,   --钻石
    [2] = 4,   --金币
    [3] = 7,   --声望
    [4] = 6,   --帮贡(历练)
    [5] = 9,   --功勋
    [6] = 10,  --绑定元宝
    [7] = 11,  --点券
    [8] = 5,   --熔炼值
    [9] = 16,  --夺宝券
    [10] = 18, --虚拟金币
}

---@class UIPurchaseWindows:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
-- function m:GetOpenEventList()
--     return {
--         -- [EventID.LogicDataChange] = m.UpdateView,
--         -- [EventID.OnHeroPropChange] = m.UpdateView,
--         -- [EventID.OnGoodsPropChange] = m.UpdateView,
--         -- [EventID.OnSkepGoodsChange] = m.UpdateView,
--     }
-- end

--------------------------------------------------------------------
-- 预制体自适应
--------------------------------------------------------------------
-- function m:AdaptScale()

-- end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
    m.objList.Txt_Cancel.text = CommonTextID.CANCEL
    m:RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
---@param storeID integer 商店ID
---@param itemNum integer 购买数量
---@param data ItemData 购买物品数据
--------------------------------------------------------------------
function m:OnOpen(storeID, itemNum, data)
    m.data = data
    m.itemNum = itemNum
    m.storeID = storeID
    m.costType = CostType[data.CostType]
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m:RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Cancel.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_OK.onClick:AddListenerEx(function()
        if m.storeID == FateShop then
            local skep = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
            if skep:IsFull() then
                HelperL.ShowMessage(TipType.FlowText, GetGameText('UIFate', 8))
                return
            end
        end

        if HelperL.IsLackGoods(m.costType, m.data.CostNum, false, true) then
            return
        end
        if m.storeID == 9 then
            local goodInfo = m.data.GoodsID .. ';' .. m.data.GoodsNum
            local costInfo = 3 .. ';' .. m.data.CostNum
            HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, m.RequestCallback)
        else
            StoreModule:StoreBuyItem(m.storeID, m.data.ItemID, m.itemNum)
        end     
        HelperL.ShowMessage(TipType.FlowText, '购买成功！')
        m:CloseSelf()
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    if not m.goodsItem then
        m.goodsItem = _GAddSlotItem(m.objList.Obj_Root)
    end
    m.goodsItem:SetItemID(m.data.GoodsID)
    m.goodsItem:SetCount(m.itemNum)
    if m.data.GoodsID > DEFINE.MAX_MEDICAMENT_ID then
        local cfg = Schemes.Equipment:Get(m.data.GoodsID)
        -- local color = HelperL.GetColorByQuality(cfg.QualityLevel)
        m.objList.Txt_Name.text = cfg.GoodsName
        m.objList.Txt_Desc.text = string.gsub(cfg.TipsDes, '<br>', '\n')
    else
        local cfg = Schemes.Medicament:Get(m.data.GoodsID)
        -- local color = HelperL.GetColorByQuality(cfg.Quality)
        m.objList.Txt_Name.text = cfg.GoodsName
        m.objList.Txt_Desc.text = string.gsub(cfg.TipsDes, '<br>', '\n')
    end
    m.objList.Txt_Univalence.text = m.data.CostNum
    AtlasManager:AsyncGetGoodsSprite(m.costType, m.objList.Img_Property)

    local num = SkepModule:GetGoodsCount(m.costType)
    local color = '#ffffff'
    if num < m.data.CostNum then
        color = '#ff000'
    end
    m.objList.Txt_Property.text = string.format('<color=#%s>%s</color>', color, num)
    AtlasManager:AsyncGetGoodsSprite(m.costType, m.objList.Img_Univalence)
end

return m
