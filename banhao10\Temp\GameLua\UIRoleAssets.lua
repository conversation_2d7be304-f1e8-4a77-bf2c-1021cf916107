--[[
********************************************************************
    created:	2024/06/01
    author :	李锦剑
    purpose:    角色资产
*********************************************************************
--]]

local luaID = 'UIRoleAssets'
local property = {
    PHYSICAL_POWER = 2, --体力
    DIAMOND = 3,        --钻石
    GOLD = 4,           --金币
    --XW = 9,      --修为
}

---@class UIRoleAssets:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---资产按钮集合
    ---@type Property[]
    m.propertyButtonList = {}
    for _, v in pairs(property) do
        m.propertyButtonList[v] = m.CreationPropertyButton(v)
    end
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    if m.propertyButtonList and #m.propertyButtonList > 0 then
        for _, v in pairs(m.propertyButtonList) do
            v.SecondUpdate()
        end
    end
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    --创建资产按钮
    for _, v in pairs(m.propertyButtonList) do
        v.UpdateView()
    end
end

--------------------------------------------------------------------
---创建资产按钮
---@param id integer 资产ID
---@return Property
--------------------------------------------------------------------
function m.CreationPropertyButton(id)
    ---@class Property
    local item = {}
    item.id = id
    item.com = m:CreateSubItem(m.objList.GridProperty, m.objList.Item_Property)
    item.com.Txt_Time.text = ''
    item.com.Txt_Amount.text = ''
    m:AddClick(item.com.Btn_Click, function()
        m.OnClickProperty(item.id)
    end)
    item.com.gameObject.name = 'Property_' .. id
    AtlasManager:AsyncGetGoodsSprite(id, item.com.Img_Icon)
    ---更新界面
    item.UpdateView = function()
        if item.id == nil then return end
        item.com.Txt_Amount.text = HelperL.GetChangeNum(SkepModule:GetGoodsCount(id))
    end
    ---每秒更新
    item.SecondUpdate = function()
        if item.id == nil then return end
        if item.id == 2 then --体力特殊处理
            local time = HelperL.GetServerTime()
            local lastTime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ENERGY_CHECKTIME)
            local tiLiTime = Schemes.ConstValue:Get(CONST_VALUE.CONST_ENERGY_ADDINTERVAL)
            item.com.Txt_Time.text = HelperL.GetTimeString(TimeStringType.FullAuto2, tiLiTime - (time - lastTime))
        end
    end
    return item
end

--------------------------------------------------------------------
--资产点击事件
---@param id integer 资产ID
--------------------------------------------------------------------
function m.OnClickProperty(id)
    SoundManager:PlaySound(1005)
    local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    if id == property.PHYSICAL_POWER then
        UIManager:OpenWnd(WndID.PurchasePhysicalPower)
    elseif id == property.DIAMOND then
        local openLevel = MainButtonOpenLevel[1] or 0
        if level >= openLevel then
            UIManager:OpenWnd(WndID.MainTitle, 1, 1)
        else
            HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText("UIMainTitle", 80), openLevel))
        end
    elseif id == property.GOLD then
        local openLevel = WanOpenLevel[WndID.CatPatrol] or 0
        if level >= openLevel then
            UIManager:OpenWnd(WndID.CatPatrol)
        else
            HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText("UIMainTitle", 80), openLevel))
        end
    end
end

return m
