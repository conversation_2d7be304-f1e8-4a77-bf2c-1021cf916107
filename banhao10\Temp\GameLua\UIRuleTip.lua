-- 登录UI
local luaID = ('UIRuleTip')

local UIRuleTip = {}

-- 初始化
function UIRuleTip:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.btnList = { self.objList.Btn_Close}
	return true
end

-- 窗口开启
function UIRuleTip:OnOpen(desc, title)
	self.objList.Txt_Desc.text = desc
	if not title then
		self.objList.Txt_Title.text = GetGameText(luaID, 1)
	else
		self.objList.Txt_Title.text = title
	end
end

function UIRuleTip.OnClickClose()
	local self = UIRuleTip
	self:CloseSelf()
end

-- 窗口关闭
function UIRuleTip:OnClose()
end

-- 窗口销毁
function UIRuleTip:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIRuleTip