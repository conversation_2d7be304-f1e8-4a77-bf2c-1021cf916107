// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;

using Cysharp.Threading.Tasks;

using CsvTables;

using DataStructure;

using HotScripts;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    /// V60.0 - 虚拟目标类，用于表示已死亡敌人的坐标点
    /// </summary>
    public class DummyTarget : ThingBase
    {
        public DummyTarget(Vector3 position)
        {
            Position = position;
        }

        public override bool CanAttach(CommonProp prop)
        {
            return false;
        }

        public override string ToString()
        {
            return $"DummyTarget({Position})";
        }
    }

    /// <summary>
    ///     导弹追击敌人(枪角色) - ShootMethod=10
    /// </summary>
    public class ThingCdExecutor_10 : ActorGunCdExecutor
    {
        // V60.0 - 连射目标追踪
        private static Dictionary<int, ThingBase> _currentTargets = new Dictionary<int, ThingBase>();
        private static Dictionary<int, Vector3> _targetDeathPositions = new Dictionary<int, Vector3>();

        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 获取枪械ID用于调试
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            Debug.Log($"=== ThingCdExecutor_10 DoShoot开始 - 枪ID:{gunId} ===");

            // 物件的射程
            double gunRange = Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            if (gunRange <= 0)
            {
                Debug.Log($"ThingCdExecutor_10 射程为0，取消发射 - 枪ID:{gunId} 射程:{gunRange}");
                return;
            }

            Debug.Log($"ThingCdExecutor_10 查找敌人 - 枪ID:{gunId} 角色位置:{Actor.Position} 射程:{gunRange}");

            // 查找攻击范围内的敌人
            var enemiesInRange = SingletonMgr.Instance.BattleMgr.FindMonster(
                Actor.Position, Actor.TotalProp_Radius, FindActionTarget.NearestEnemy, (float)gunRange, 99);

            Debug.Log($"ThingCdExecutor_10 敌人查找完成 - 枪ID:{gunId} 找到敌人数量:{enemiesInRange?.Count ?? 0}");

            // 没有攻击目标，不发子弹
            if (enemiesInRange == null || enemiesInRange.Count == 0)
            {
                Debug.Log($"ThingCdExecutor_10 攻击范围内没有敌人，取消发射 - 枪ID:{gunId}");
                return;
            }

            Debug.Log($"ThingCdExecutor_10 角色发射导弹，播放攻击动画 - 枪ID:{gunId} 范围内敌人数量:{enemiesInRange.Count}");
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayAnimation("attack01", false, true);

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                Debug.Log($"ThingCdExecutor_10 Actor为null，取消发射 - 枪ID:{gunId}");
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token,
                Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 读取连射配置参数
                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);

                Debug.Log($"ThingCdExecutor_10 连射配置读取 - 枪ID:{gunId} 原始连射次数:{shootTimes} 延时列表长度:{burstDelayList?.Count ?? 0} 子弹数量列表长度:{burstBulletCountList?.Count ?? 0}");

                // 如果没有配置连射次数，则默认发射1次
                if (shootTimes <= 0)
                {
                    shootTimes = 1;
                    Debug.Log($"ThingCdExecutor_10 连射次数为0，设为默认值1 - 枪ID:{gunId}");
                }

                // 如果没有配置延时，则默认立即发射
                if (burstDelayList.Count == 0)
                {
                    burstDelayList.Add(0);
                    Debug.Log($"ThingCdExecutor_10 延时列表为空，添加默认值0 - 枪ID:{gunId}");
                }

                // 如果没有配置子弹数量，则默认每次1发
                if (burstBulletCountList.Count == 0)
                {
                    burstBulletCountList.Add(1);
                    Debug.Log($"ThingCdExecutor_10 子弹数量列表为空，添加默认值1 - 枪ID:{gunId}");
                }

                Debug.Log($"ThingCdExecutor_10 最终连射参数 - 枪ID:{gunId} 连射次数:{shootTimes} 子弹数量列表:[{string.Join(",", burstBulletCountList)}] 敌人数量:{enemiesInRange.Count}");

                // 清理之前的目标分配
                _currentTargets.Clear();
                _targetDeathPositions.Clear();

                // 根据配置延时后射击 - 简化版本，直接使用原有逻辑
                for (int i = 0; i < shootTimes; i++)
                {
                    double delay = burstDelayList.Count > i ? burstDelayList[i] : burstDelayList.LastOrDefault();
                    long bulletCount = burstBulletCountList.Count > i ? burstBulletCountList[i] : burstBulletCountList.LastOrDefault();

                    Debug.Log($"ThingCdExecutor_10 第{i+1}轮射击准备 - 枪ID:{gunId} 延时:{delay}秒 子弹数量:{bulletCount}");

                    // 简化版本：回到原有的BurstOne逻辑，但保持新的目标分配
                    BurstOneSimple(cts_Skill.Token, (float)delay, enemiesInRange, bulletCount, i + 1).Forget();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"ThingCdExecutor_10 连射逻辑异常 - 枪ID:{gunId} 异常:{ex.Message}");
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// V61.1 - 简化版本：减少复杂优化，确保稳定性
        /// </summary>
        private async UniTaskVoid BurstOneSimple(CancellationToken token, float delay, List<DistanceThing> enemiesInRange,
            long bulletCount, int burstIndex)
        {
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            try
            {
                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮开始 - 枪ID:{gunId} 延时:{delay}秒 子弹数量:{bulletCount}");

                // 等待延时
                if (delay > 0)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);
                    Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮延时完成 - 枪ID:{gunId}");
                }

                if (token.IsCancellationRequested)
                {
                    Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮被取消 - 枪ID:{gunId}");
                    return;
                }

                // 分配目标给子弹
                List<ThingBase> assignedTargets = AssignTargetsForBullets(enemiesInRange, (int)bulletCount, burstIndex);

                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮目标分配完成 - 枪ID:{gunId} 分配目标数量:{assignedTargets.Count}");

                if (assignedTargets.Count == 0)
                {
                    Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮没有可分配的目标 - 枪ID:{gunId}");
                    return;
                }

                // 创建子弹 - 简化版本，一次性创建（稍后再优化分帧）
                for (int i = 0; i < assignedTargets.Count; i++)
                {
                    if (token.IsCancellationRequested) return;
                    
                    ThingBase target = assignedTargets[i];
                    
                    Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮第{i+1}颗子弹创建开始 - 枪ID:{gunId} 目标类型:{target?.GetType().Name} 目标位置:{target?.Position}");
                    
                    // 创建单颗子弹
                    CreateSingleBullet(target, i, burstIndex, gunId);
                }

                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮射击完成 - 枪ID:{gunId} 已发射{assignedTargets.Count}颗导弹追击子弹");
            }
            catch (OperationCanceledException) 
            { 
                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮射击被取消 - 枪ID:{gunId}");
                throw; 
            }
            catch (Exception ex)
            {
                Debug.LogError($"ThingCdExecutor_10 第{burstIndex}轮射击异常 - 枪ID:{gunId} 异常:{ex.Message}");
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 创建单颗子弹的方法
        /// </summary>
        private void CreateSingleBullet(ThingBase target, int bulletIndex, int burstIndex, int gunId)
        {
            try
            {
                int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                // 计算子弹初始位置：每轮从0开始重新计算高度
                float baseHeight = 40f;
                float heightIncrement = 5f; // 每颗子弹递增5像素高度
                float additionalHeight = bulletIndex * heightIncrement; // 每轮内的子弹序号
                float finalHeight = baseHeight + additionalHeight;
                Vector3 bulletStartPos = new Vector3(target.Position.x, target.Position.y + finalHeight, target.Position.z);
                
                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮第{bulletIndex+1}颗子弹高度计算 - 枪ID:{gunId} 轮内序号:{bulletIndex} 基础高度:{baseHeight} 附加高度:{additionalHeight} 最终高度:{finalHeight} 起始位置:{bulletStartPos}");
                
                // 创建导弹追击子弹
                BulletThing bullet = Thing.CreateBullet(this, target, target.Position, 0,
                    maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                if (bullet == null)
                {
                    Debug.LogError($"ThingCdExecutor_10 第{burstIndex}轮第{bulletIndex+1}颗子弹创建失败 - 枪ID:{gunId} bullet为null");
                    return;
                }

                // 设置子弹从怪物头顶发射，追击目标敌人
                bullet.Position = bulletStartPos;
                bullet.TrackPosition = target.Position;

                // 保存目标分配信息用于连射跟踪
                _currentTargets[bulletIndex] = target;

                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮第{bulletIndex+1}颗导弹创建成功 - 枪ID:{gunId} 从位置:{bulletStartPos} 追击目标:{target.GetType().Name} 位置:{target.Position}");

                // 在子弹上存储目标信息，供GameManager初始化时使用
                bullet.AttackBaseDirFollowThing = target;

                MessageBroker.Default.Publish(new BornBullet
                {
                    Bullet = bullet, 
                    PositionPre = bullet.Position
                });
                
                Debug.Log($"ThingCdExecutor_10 第{burstIndex}轮第{bulletIndex+1}颗子弹BornBullet消息已发布 - 枪ID:{gunId}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ThingCdExecutor_10 创建子弹异常 - 枪ID:{gunId} 轮次:{burstIndex} 序号:{bulletIndex} 异常:{ex.Message}");
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 为子弹分配不重复的目标敌人
        /// </summary>
        private List<ThingBase> AssignTargetsForBullets(List<DistanceThing> enemiesInRange, int bulletCount, int burstIndex)
        {
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            List<ThingBase> assignedTargets = new List<ThingBase>();
            
            // 筛选存活敌人
            List<ThingBase> aliveEnemies = enemiesInRange
                .Where(e => e.Thing2 != null && e.Thing2.Hp.Value > float.Epsilon)
                .Select(e => e.Thing2)
                .ToList();

            Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 开始分配目标 - 枪ID:{gunId} 第{burstIndex}轮 需要子弹数:{bulletCount} 范围内敌人:{enemiesInRange?.Count ?? 0} 存活敌人:{aliveEnemies.Count}");

            if (aliveEnemies.Count == 0)
            {
                Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 没有存活敌人 - 枪ID:{gunId} 第{burstIndex}轮 返回空列表");
                return assignedTargets;
            }

            if (bulletCount >= aliveEnemies.Count)
            {
                // 如果子弹数量大于等于敌人数量，每个敌人分配一颗子弹
                assignedTargets.AddRange(aliveEnemies);
                Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 子弹>=敌人 - 枪ID:{gunId} 第{burstIndex}轮 每个敌人分配1颗子弹 分配数量:{assignedTargets.Count}");
            }
            else
            {
                // 如果子弹数量小于敌人数量，随机选择对应数量的敌人
                var random = new System.Random();
                var selectedEnemies = aliveEnemies.OrderBy(x => random.Next()).Take(bulletCount).ToList();
                assignedTargets.AddRange(selectedEnemies);
                Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 子弹<敌人 - 枪ID:{gunId} 第{burstIndex}轮 随机选择{bulletCount}个敌人 分配数量:{assignedTargets.Count}");
            }

            // 连射时处理目标死亡的情况
            if (burstIndex > 1)
            {
                Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 连射处理 - 枪ID:{gunId} 第{burstIndex}轮 检查之前目标状态");
                
                for (int i = 0; i < assignedTargets.Count; i++)
                {
                    // 检查之前分配的目标是否死亡
                    if (_currentTargets.ContainsKey(i))
                    {
                        ThingBase previousTarget = _currentTargets[i];
                        if (previousTarget != null && previousTarget.Hp.Value <= float.Epsilon)
                        {
                            // 目标死亡，记录死亡坐标点
                            _targetDeathPositions[i] = previousTarget.Position;
                            Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 目标死亡 - 枪ID:{gunId} 第{burstIndex}轮 第{i+1}颗子弹 记录死亡坐标:{previousTarget.Position}");
                            
                            // 尝试重新分配新目标
                            ThingBase newTarget = FindNewTargetForBullet(aliveEnemies, assignedTargets);
                            if (newTarget != null)
                            {
                                assignedTargets.Add(newTarget);
                                _currentTargets[i] = newTarget;
                                Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 重新分配新目标 - 枪ID:{gunId} 第{burstIndex}轮 第{i+1}颗子弹 新目标:{newTarget.GetType().Name}");
                            }
                            else
                            {
                                // 没有新目标，创建虚拟目标使用死亡坐标点
                                DummyTarget dummyTarget = new DummyTarget(_targetDeathPositions[i]);
                                assignedTargets.Add(dummyTarget);
                                Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 使用死亡坐标点 - 枪ID:{gunId} 第{burstIndex}轮 第{i+1}颗子弹 死亡坐标:{_targetDeathPositions[i]}");
                            }
                        }
                    }
                }
            }

            Debug.Log($"ThingCdExecutor_10 AssignTargetsForBullets 分配完成 - 枪ID:{gunId} 第{burstIndex}轮 最终分配目标数量:{assignedTargets.Count}");
            
            return assignedTargets;
        }

        /// <summary>
        /// 为子弹查找新的目标敌人（不重复）
        /// </summary>
        private ThingBase FindNewTargetForBullet(List<ThingBase> aliveEnemies, List<ThingBase> alreadyAssigned)
        {
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            // 从存活敌人中查找未被分配的敌人
            var availableEnemies = aliveEnemies.Where(enemy => !alreadyAssigned.Contains(enemy)).ToList();
            
            Debug.Log($"ThingCdExecutor_10 FindNewTargetForBullet - 枪ID:{gunId} 存活敌人:{aliveEnemies.Count} 已分配:{alreadyAssigned.Count} 可用敌人:{availableEnemies.Count}");
            
            if (availableEnemies.Count == 0)
            {
                Debug.Log($"ThingCdExecutor_10 FindNewTargetForBullet 没有可用敌人 - 枪ID:{gunId}");
                return null;
            }
            
            // 随机选择一个敌人
            var random = new System.Random();
            ThingBase selectedTarget = availableEnemies[random.Next(availableEnemies.Count)];
            
            Debug.Log($"ThingCdExecutor_10 FindNewTargetForBullet 选择目标 - 枪ID:{gunId} 目标类型:{selectedTarget?.GetType().Name}");
            
            return selectedTarget;
        }
    }
}