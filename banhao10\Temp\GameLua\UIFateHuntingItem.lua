---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by Administrator.
--- DateTime: 2023/7/22 16:44
--- 【菌落界面】狩猎Item
---

local UIItemBase = require('Base_UI_Item')
local ECacheField = require("UIFateEnumDefine").CacheField

---@class UIFateHuntingItem:UIItemBase
---@field private _base UIItemBase
---@field private cfg
---@field private handler UIFateHandler
---@field private cache UIFateCache
---@field private isActive boolean @是否为已激活（可狩猎）
local this = class(UIItemBase)

function this:_init(go, cfg, handler, cache)
    self:Init(go, cfg, handler, cache)
end

--- 初始化
---@param handler UIFateHandler
---@param cache UIFateCache
function this:Init(go, cfg, handler, cache)
    self:baseInit(go)
    self.cfg = cfg
    self.handler = handler
    self.cache = cache
    self:SetLuaFileName("UIFate")
    self:SetHeadIcon()
    self:SetCost()
    AtlasManager:AsyncGetGoodsSprite(7, self.objList.Img_PrestigeIcon)
end

--- 重置Item
---@param isActive boolean @是否为已激活，true表示激活
function this: Reset(isActive)
    self.isActive = isActive
    -- 遮罩
    self.objList.Mask:SetActive(not isActive)
end

---@private
function this:SetHeadIcon()
    
end

---@private
function this:SetBG()

end

---@private
function this:SetCost()
    local cost = self.cfg.CostSilver
    self.objList.TMP_Prestige.text = (cost ~= 0) and cost or self:GetGameText(5)
end

--- 添加监听
---@private
function this:AddListener()
    self:AddClickEvent(self.objList.Btn_Hunt, function()
        self:OnClickItem()
    end)
end

---@private
function this:OnClickItem()
    if self.isActive == false then
        return
    end
    self.handler:Hunt(self.cfg.ID, self.cache:GetField(ECacheField.CUR_COST_TYPE))
end

--- 销毁
function this:OnDestroy()

end

return this