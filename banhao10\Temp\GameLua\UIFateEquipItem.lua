---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by Admin.
--- DateTime: 2023/8/4 20:30
---

local UIItemBase = require("Base_UI_Item")
--背景框
local kuang_bg = {
    'kuang_1',
    'kuang_2',
    'kuang_3',
    'kuang_4',
    'kuang_5',
}

---@class UIFateEquipItem:UIItemBase
---@field protected _base UIItemBase
---@field protected _handler UIFateHandler
---@field protected _cache UIFateCache
local this = class(UIItemBase)

---@private
function this:_init(...)
    self:Init(...)
end

---@param _go GameObject
---@param _handler UIFateHandler
---@param _cache UIFateCache
function this:Init(_go, _handler, _cache)
    self:baseInit(_go)
    self._handler = _handler
    self._cache = _cache
end

---@private
function this:AddListener()
    local btn = self.objList.FateItem:GetComponent("Button")
    self:AddClickEvent(btn, function()
        if self.entity then
            EventManager:Fire(EventID.ShowFateHint, self.entity)
        end
    end)
end

--- 刷新
---@param entity table @实体数据
function this:Reset(entity)
    -- 实体ID一致，无需刷新
    if self.entity and entity and self.entity.uid == entity.uid then
        self.entity = nil
        return
    end
    self.entity = entity
    local quality
    local goodsCfg
    if entity then
        local goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        goodsCfg = Schemes:GetGoodsConfig(goodsId)
        quality = goodsCfg.QualityLevel
    end
    -- 图片
    self:SetIconSprite(goodsCfg)
    -- 名字
    self:SetName(goodsCfg)
    -- 背景图片
    self:SetBackgroundImage(quality)
end

---@private
function this:SetIconSprite(goodsCfg)
    self.objList.Img_Icon.gameObject:SetActive(false)
    if goodsCfg then
        AtlasManager:AsyncGetSprite(goodsCfg.IconID, self.objList.Img_Icon, false, function(sprite, image, param)
            image.gameObject:SetActive(sprite ~= nil)
        end)
    end
end

---@private
function this:SetName(goodsCfg)
    self.objList.TMP_FateName.gameObject:SetActive(false)
    if goodsCfg then
        local str = self:GetActorPropDesc(self.entity)
        self.objList.Txt_FateBuff.text = StringL.SetColoredText(str, HelperL.GetColorByQuality(goodsCfg.QualityLevel))
    end
    self.objList.Img_FateBuff.gameObject:SetActive(goodsCfg ~= nil)
end

---获取属性描述
function this:GetActorPropDesc(entity)
    local str = ''
    local list = ActorProp.GetEquipSmeltProperty(entity)
    for _, v in ipairs(list) do
        if str == '' then
            str = v.value .. '%\n' .. v.desc
        else
            str = str .. '\n' .. v.value .. '%\n' .. v.desc
        end
    end
    return str
end

--- 设置背景图片
function this:SetBackgroundImage(quality)
    AtlasManager:AsyncGetSprite(kuang_bg[quality or 1] or kuang_bg[1], self.objList.Img_Bg)
end

---@private
function this:IsEmpty()
    local goodsId, goodsCfg
    local entity = self.entity
    if entity ~= nil then
        goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        goodsCfg = Schemes:GetGoodsConfig(goodsId)
    end
    return self.entity == nil or goodsCfg == nil
end

function this:OnDestroy()

end

return this
