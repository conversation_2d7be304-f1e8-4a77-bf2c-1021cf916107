// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;

using Cysharp.Threading.Tasks;

using CsvTables;

using DataStructure;

using HotScripts;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    /// V61.0 - 虚拟目标类，用于处理随机坐标点目标
    /// </summary>
    public class RandomPositionTarget : ThingBase
    {
        public RandomPositionTarget(Vector3 position)
        {
            Position = position;
            Hp.Value = 1.0; // 设置为1避免被认为是死亡状态
        }
        
        /// <summary>
        /// 虚拟目标不支持属性附着
        /// </summary>
        public override bool CanAttach(CommonProp prop)
        {
            return false; // 虚拟目标不支持任何属性附着
        }
        
        public override string ToString()
        {
            return $"RandomPositionTarget at {Position}";
        }
    }

    /// <summary>
    ///     导弹追击随机坐标点(枪角色) - ShootMethod=11
    /// </summary>
    public class ThingCdExecutor_11 : ActorGunCdExecutor
    {
        /// <summary>
        /// 静态目标分配表：用于连射时跟踪目标分配情况
        /// </summary>
        private static Dictionary<int, ThingBase> _currentTargets = new Dictionary<int, ThingBase>();
        private static Dictionary<int, Vector3> _targetPositions = new Dictionary<int, Vector3>();

        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 888888888 获取枪械ID和子弹类型用于调试
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            // 888888888 读取Bullet表的BulletType字段
            string actualBulletType = "未知";
            string expectedBulletType = "MissileTrackEnemy"; // 对应BulletType枚举值4
            int actualBulletTypeInt = 0;
            int expectedBulletTypeInt = 4; // BulletType.MissileTrackEnemy的枚举值
            
            try
            {
                if (Thing is GunThing gun && gun.CsvRow_Gun.Value != null)
                {
                    var bulletId = gun.CsvRow_Gun.Value.BulletId;
                    
                    // 获取子弹配置 - 使用正确的CsvLoaderMgr访问方式
                    var bulletCsv = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CsvTables.BulletCsv>();
                    var bulletLevelConfigs = bulletCsv.GetByBulletId(bulletId);
                    
                    if (bulletLevelConfigs != null && bulletLevelConfigs.Count > 0)
                    {
                        // 使用等级1的配置来检查BulletType
                        var bulletCfg = bulletLevelConfigs.Values.FirstOrDefault();
                        if (bulletCfg != null)
                        {
                            // V61.0 修复：BulletType是枚举类型，直接转换为字符串
                            actualBulletType = bulletCfg.BulletType.ToString();
                            actualBulletTypeInt = (int)bulletCfg.BulletType;
                        }
                    }
                    
                    //Debug.Log($"888888888 ThingCdExecutor_11 Bullet配置检查 - 枪ID:{gunId} BulletId:{bulletId} 实际BulletType:[{actualBulletType}({actualBulletTypeInt})] 应该配置:[{expectedBulletType}({expectedBulletTypeInt})]");
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"888888888 ThingCdExecutor_11 读取BulletType失败 - 枪ID:{gunId} 错误:{ex.Message}");
            }
            
            //Debug.Log($"888888888 ThingCdExecutor_11 开始执行 - 枪ID:{gunId} BulletType:[{actualBulletType}]");

            // 物件的射程
            double gunRange = Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            if (gunRange <= 0)
            {
                //Debug.Log($"888888888 ThingCdExecutor_11 射程为0，取消发射 - 枪ID:{gunId} BulletType:[{actualBulletType}] 射程:{gunRange}");
                return;
            }

            //Debug.Log($"888888888 ThingCdExecutor_11 区域随机坐标模式 - 枪ID:{gunId} BulletType:[{actualBulletType}] 角色位置:{Actor.Position} 射程:{gunRange}");

            // V61.0 - 动画系统修复
            //Debug.Log($"888888888 ThingCdExecutor_11 角色发射导弹，播放攻击动画 - 枪ID:{gunId} BulletType:[{actualBulletType}]");
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayAnimation("attack01", false, true);

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                //Debug.Log($"888888888 ThingCdExecutor_11 Actor为null，取消发射 - 枪ID:{gunId} BulletType:[{actualBulletType}]");
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token,
                Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 读取连射配置参数
                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);

                //Debug.Log($"888888888 ThingCdExecutor_11 连射配置读取 - 枪ID:{gunId} BulletType:[{actualBulletType}] 原始连射次数:{shootTimes} 延时列表长度:{burstDelayList?.Count ?? 0} 子弹数量列表长度:{burstBulletCountList?.Count ?? 0}");

                // 如果没有配置连射次数，则默认发射1次
                if (shootTimes <= 0)
                {
                    shootTimes = 1;
                    //Debug.Log($"888888888 ThingCdExecutor_11 连射次数为0，设为默认值1 - 枪ID:{gunId} BulletType:[{actualBulletType}]");
                }

                // 如果没有配置延时，则默认立即发射
                if (burstDelayList.Count == 0)
                {
                    burstDelayList.Add(0);
                    //Debug.Log($"888888888 ThingCdExecutor_11 延时列表为空，添加默认值0 - 枪ID:{gunId} BulletType:[{actualBulletType}]");
                }

                // 如果没有配置子弹数量，则默认每次1发
                if (burstBulletCountList.Count == 0)
                {
                    burstBulletCountList.Add(1);
                    //Debug.Log($"888888888 ThingCdExecutor_11 子弹数量列表为空，添加默认值1 - 枪ID:{gunId} BulletType:[{actualBulletType}]");
                }

                ////Debug.Log($"888888888 ThingCdExecutor_11 最终连射参数 - 枪ID:{gunId} BulletType:[{actualBulletType}] 连射次数:{shootTimes} 子弹数量列表:[{string.Join(",", burstBulletCountList)}]");

                // 清理之前的目标分配
                _currentTargets.Clear();
                _targetPositions.Clear();

                // 根据配置延时后射击
                for (int i = 0; i < shootTimes; i++)
                {
                    double delay = burstDelayList.Count > i ? burstDelayList[i] : burstDelayList.LastOrDefault();
                    long bulletCount = burstBulletCountList.Count > i ? burstBulletCountList[i] : burstBulletCountList.LastOrDefault();

                    ////Debug.Log($"888888888 ThingCdExecutor_11 第{i+1}轮射击准备 - 枪ID:{gunId} 延时:{delay}秒 子弹数量:{bulletCount}");

                    BurstOne(cts_Skill.Token, (float)delay, gunRange, bulletCount, i + 1).Forget();
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"888888888 ThingCdExecutor_11 连射逻辑异常 - 枪ID:{gunId} 异常:{ex.Message}");
                Debug.LogException(ex);
            }
        }

        /// <summary>
        ///     发射一轮导弹追击随机坐标点子弹
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="gunRange">枪械射程，用于确定随机区域半径</param>
        /// <param name="bulletCount">发射的子弹数量</param>
        /// <param name="burstIndex">连射轮次</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, double gunRange,
            long bulletCount, int burstIndex)
        {
            // 888888888 获取枪械ID用于调试
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            try
            {
                ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮射击开始等待 - 枪ID:{gunId} 延时:{delay}秒");
                
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    ////////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮射击被取消 - 枪ID:{gunId}");
                    return;
                }

                ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮射击开始 - 枪ID:{gunId} 延时{delay}秒后 准备发射{bulletCount}颗导弹");

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                // V61.0 - 为每个子弹生成随机的坐标点目标
                List<ThingBase> assignedTargets = GenerateRandomPositionTargets((int)bulletCount, gunRange, burstIndex);

                ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮随机坐标点生成完成 - 枪ID:{gunId} 生成坐标点数量:{assignedTargets?.Count ?? 0}");

                ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮开始创建子弹 - 枪ID:{gunId} 子弹数量:{assignedTargets.Count}");

                // 发射子弹，每个子弹对应一个随机坐标点
                for (int i = 0; i < assignedTargets.Count; i++)
                {
                    ThingBase target = assignedTargets[i];
                    
                    ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮第{i+1}颗子弹创建开始 - 枪ID:{gunId} 目标类型:{target?.GetType().Name} 目标位置:{target?.Position}");
                    
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // V61.0 - 计算子弹初始位置：每轮从0开始重新计算高度
                    // 基础高度40，第1颗=40，第2颗=80，第3颗=120，以此类推
                    float baseHeight = 40f;
                    float heightIncrement = 40f; // 每颗子弹递增40像素高度
                    float additionalHeight = i * heightIncrement; // 每轮内的子弹序号
                    float finalHeight = baseHeight + additionalHeight;
                    Vector3 bulletStartPos = new Vector3(target.Position.x, target.Position.y + finalHeight, target.Position.z);
                    
                    ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮第{i+1}颗子弹高度计算 - 枪ID:{gunId} 轮内序号:{i} 基础高度:{baseHeight} 附加高度:{additionalHeight} 最终高度:{finalHeight} 起始位置:{bulletStartPos}");
                    ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮第{i+1}颗子弹参数 - 枪ID:{gunId} 穿透次数:{maxPenetrateTimes} 反弹次数:{maxBounceTimes} 分裂次数:{maxSeparateTimes}");
                    
                    // 创建导弹追击子弹
                    BulletThing bullet = Thing.CreateBullet(this, target, target.Position, 0,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    if (bullet == null)
                    {
                        ////Debug.LogError($"888888888 ThingCdExecutor_11 第{burstIndex}轮第{i+1}颗子弹创建失败 - 枪ID:{gunId} bullet为null");
                        continue;
                    }

                    // V61.0 - 设置子弹从随机坐标点上方发射，追击目标坐标点
                    bullet.Position = bulletStartPos;
                    bullet.TrackPosition = target.Position;

                    // 保存目标分配信息用于连射跟踪
                    _currentTargets[i] = target;
                    _targetPositions[i] = target.Position;

                    ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮第{i+1}颗导弹创建成功 - 枪ID:{gunId} 从位置:{bulletStartPos} 追击坐标点:{target.Position}");

                    // V61.0 - 在子弹上存储目标信息，供GameManager初始化时使用
                    bullet.AttackBaseDirFollowThing = target;

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, 
                        PositionPre = bullet.Position
                    });
                }

                ////Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮射击完成 - 枪ID:{gunId} 已发射{assignedTargets.Count}颗导弹追击随机坐标点");
            }
            catch (OperationCanceledException) 
            { 
                //Debug.Log($"888888888 ThingCdExecutor_11 第{burstIndex}轮射击被取消 - 枪ID:{gunId}");
                throw; 
            }
            catch (Exception ex)
            {
                ////Debug.LogError($"888888888 ThingCdExecutor_11 第{burstIndex}轮射击异常 - 枪ID:{gunId} 异常:{ex.Message}");
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// V61.0 - 为子弹生成随机的坐标点目标，确保分散分布
        /// </summary>
        /// <param name="bulletCount">需要发射的子弹数量</param>
        /// <param name="gunRange">枪械射程，用于确定随机区域半径</param>
        /// <param name="burstIndex">连射轮次</param>
        /// <returns>分配给子弹的随机坐标点目标列表</returns>
        private List<ThingBase> GenerateRandomPositionTargets(int bulletCount, double gunRange, int burstIndex)
        {
            // 888888888 获取枪械ID用于调试
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;
            
            List<ThingBase> assignedTargets = new List<ThingBase>();
            Vector3 actorPosition = Actor.Position;
            float range = (float)gunRange;

            ////Debug.Log($"888888888 GenerateRandomPositionTargets 开始生成随机坐标点 - 枪ID:{gunId} 第{burstIndex}轮 需要子弹数:{bulletCount} 角色位置:{actorPosition} 射程半径:{range}");

            var random = new System.Random();
            List<Vector3> generatedPositions = new List<Vector3>(); // 用于检查分散度

            for (int i = 0; i < bulletCount; i++)
            {
                Vector3 randomPosition = GenerateRandomPositionInCircle(actorPosition, range, generatedPositions, random);
                generatedPositions.Add(randomPosition);
                
                // 创建随机坐标点目标
                RandomPositionTarget target = new RandomPositionTarget(randomPosition);
                assignedTargets.Add(target);

                ////Debug.Log($"888888888 GenerateRandomPositionTargets 第{i+1}个坐标点 - 枪ID:{gunId} 第{burstIndex}轮 坐标:{randomPosition} 与角色距离:{Vector3.Distance(actorPosition, randomPosition):F1}");
            }

            ////Debug.Log($"888888888 GenerateRandomPositionTargets 生成完成 - 枪ID:{gunId} 第{burstIndex}轮 总共生成:{assignedTargets.Count}个随机坐标点");
            
            return assignedTargets;
        }

        /// <summary>
        /// V61.0 - 在圆形区域内生成随机坐标点，确保与已有坐标点保持分散
        /// </summary>
        /// <param name="center">圆心（角色位置）</param>
        /// <param name="radius">圆半径（射程）</param>
        /// <param name="existingPositions">已生成的坐标点列表，用于避免过于集中</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>分散的随机坐标点</returns>
        private Vector3 GenerateRandomPositionInCircle(Vector3 center, float radius, List<Vector3> existingPositions, System.Random random)
        {
            int maxAttempts = 10; // 最大尝试次数，避免无限循环
            float minDistance = radius * 0.3f; // 最小分散距离，防止过于集中

            for (int attempt = 0; attempt < maxAttempts; attempt++)
            {
                // 在圆形区域内生成随机坐标点
                // 使用极坐标系统确保均匀分布
                float randomAngle = (float)(random.NextDouble() * 2 * Math.PI); // 0 到 2π
                float randomRadius = (float)(Math.Sqrt(random.NextDouble()) * radius); // 使用sqrt确保均匀分布

                float x = center.x + randomRadius * Mathf.Cos(randomAngle);
                float z = center.z + randomRadius * Mathf.Sin(randomAngle);
                Vector3 randomPosition = new Vector3(x, center.y, z);

                // 检查与已有坐标点的距离，确保分散
                bool isTooClose = false;
                foreach (Vector3 existingPos in existingPositions)
                {
                    float distance = Vector3.Distance(randomPosition, existingPos);
                    if (distance < minDistance)
                    {
                        isTooClose = true;
                        break;
                    }
                }

                if (!isTooClose || attempt == maxAttempts - 1)
                {
                    // 找到合适的分散坐标点，或者已达到最大尝试次数
                    ////Debug.Log($"888888888 GenerateRandomPositionInCircle - 尝试{attempt + 1}次 坐标:{randomPosition} 角度:{randomAngle * 57.3f:F1}° 半径:{randomRadius:F1} 分散检查:{(isTooClose ? "太近但已最大尝试" : "合适")}");
                    return randomPosition;
                }

                ////Debug.Log($"888888888 GenerateRandomPositionInCircle - 尝试{attempt + 1}次 坐标:{randomPosition} 太接近已有坐标，重新生成");
            }

            // 兜底：如果所有尝试都失败，返回一个基本的随机坐标点
            float fallbackAngle = (float)(random.NextDouble() * 2 * Math.PI);
            float fallbackRadius = (float)(random.NextDouble() * radius);
            Vector3 fallbackPosition = new Vector3(
                center.x + fallbackRadius * Mathf.Cos(fallbackAngle),
                center.y,
                center.z + fallbackRadius * Mathf.Sin(fallbackAngle)
            );

            ////Debug.Log($"888888888 GenerateRandomPositionInCircle - 兜底坐标:{fallbackPosition}");
            return fallbackPosition;
        }
    }
}