﻿// ReSharper disable StaticMemberInGenericType
// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;

using Google.Protobuf;

using UnityEngine;

/// <summary>
/// CSV表格加载器(proto3)
/// </summary>
/// <typeparam name="T">Message类型(表)</typeparam>
/// <typeparam name="TRow">单行类型</typeparam>
/// <typeparam name="TKey">主键列类型</typeparam>
public class CsvDicPb3Loader<T, TRow, TKey> : ICsvLoader<T, TRow, TKey>
	where T : IMessage<T>, new()
{
	/// <summary>
	/// 从Pb中获取行集时的属性名
	/// </summary>
	public static string PropertyName_Table { get; set; } = "CSVTable";
	/// <summary>
	/// 主键列名
	/// </summary>
	public static string PropertyName_Key { get; set; } = "Id";

	protected T Pb_m;

	public T Pb
	{
		get
		{
			if (Pb_m != null) return Pb_m;
			LoadPb();
			return Pb_m;
		}
	}

	/// <summary>
	/// 加载Pb到内存
	/// </summary>
	/// <param name="forceReload">是否强制重新加载</param>
	public void LoadPb(bool forceReload = false)
	{
		if (Pb_m == null || forceReload)
		{
			Pb_m = default;
			try
			{
				Pb_m = HotResManager.ReadPb3<T>(typeof(T).Name);
			}
			catch (System.Exception ex)
			{
				Debug.LogError($"加载CSV失败:{typeof(T).Name}!");
				Debug.LogException(ex);
			}
		}
	}

	private Dictionary<TKey, TRow> Dic_m;
	/// <summary>
	/// 获取从Pb转换的字典
	/// </summary>
	public Dictionary<TKey, TRow> Dic
	{
		get
		{
			if (Dic_m != null) return Dic_m;

			Dic_m = new();
			var pTable = Pb.GetType().GetProperty(PropertyName_Table);
			var pKey = typeof(TRow).GetProperty(PropertyName_Key);

			if (pTable != null && pKey != null && pTable.GetValue(Pb) is IEnumerable<TRow> vTable)
			{
				foreach (var item in vTable)
				{
					if (pKey.GetValue(item) is TKey vKey)
					{
						try
						{
							Dic_m.Add(vKey, item);
						}
						catch (ArgumentException)
						{
							// 提供详细的重复键错误信息
							Debug.LogError($"CSV表配置错误 - 表名: {typeof(T).Name}, 重复的Key: {vKey}");
							throw new ArgumentException($"CSV表 {typeof(T).Name} 中存在重复的键值: {vKey}，请检查表格配置");
						}
					}
				}
			}

			return Dic_m;
		}
	}
}