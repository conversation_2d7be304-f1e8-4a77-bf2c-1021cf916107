local luaID = ('UIGuideViewOld')

local UIGuideViewOld = {}

-- 订阅事件列表
function UIGuideViewOld:GetOpenEventList()
	return
	{
		[EventID.OnRequestUseActiveSucc] = self.OnRequestUseActiveSucc,
		[EventID.TowerBattleDeckGroupUpdate] = self.TowerBattleDeckGroupUpdate,
		[EventID.TowerBattleGridMonsterUpdate] = self.TowerBattleGridMonsterUpdate,
		[EventID.WindowClose] = self.OnWindowCloseHandle,
	}
end

-- 初始化
function UIGuideViewOld:OnCreate()
	self.objList.Btn_Skip.onClick:AddListenerEx(self.OnClickSkip)
	local eventTrigger = self.objList.Img_ClickMask.gameObject:AddComponent(typeof(EventTrigger))
	eventTrigger:AddListener(EventTriggerType.PointerClick, function(pointerEventData) 
		self:OnClickNoClickMask() 
	end)
	self.btnList = { self.objList.Btn_Skip}
	self.uiCopyContainer = self.objList.CopyTargetContainer:GetRectTransform()
	self.objList.Btn_Skip.gameObject:SetActive(false)
	self.rightHandRectTrans = self.objList.Img_Right:GetRectTransform()
	self.leftHandRectTrans = self.objList.Img_Left:GetRectTransform()
	self.rightTxtRectTrans = self.objList.Txt_Right:GetRectTransform()
	self.leftTxtRectTrans = self.objList.Txt_Left:GetRectTransform()
	self.rightBubbleRectTrans = self.objList.Img_RightBubble:GetRectTransform()
	self.leftBubbleRectTrans = self.objList.Img_LeftBubble:GetRectTransform()
	self.circleHaloRectTrans = self.objList.CircleHalo:GetRectTransform()
	self.itemContainer = self.objList.CircleHalo.gameObject.transform
	self.bigVec = Vector3(1.2,1.2,1.2)
	self.smaVec = Vector3(1,1,1)
	self.moveAnchoredPos = Vector2(0,0)	
	return true
end

function UIGuideViewOld:OnClickNoClickMask()
	local self = UIGuideViewOld
	if self.paramList and self.paramList.LeftHand ~= 10 then 
		return
	end
	coroutine.resume(self.co)
end

-- 窗口开启
function UIGuideViewOld:OnOpen(guideID,eventID) 
	self.eventID = eventID
	local guideItem = nil
	local paramList = nil
	local times = 1
	self:SwitchFrame(false)
	self.objList.Img_ClickMask.gameObject:SetActive(false)
	self.co = coroutine.create(function () --引导所有小步骤，出现一步后  协程挂起  等玩家点击后继续 
		for i=1,math.huge do       
			guideItem = Schemes.Guide:GetGuideByParams(guideID,i) 
			if not guideItem then
				paramList=nil
			else
				paramList =
				{
					FocusOn = guideItem.FocusOn,
					ClickFocus = guideItem.ClickFocus,
					GuideID = guideID,
					Step    = i,
					IsLock  = guideItem.IsLock,
					IsTips  = guideItem.IsTips,
					TipsContent = guideItem.TipsContent,
					LeftHand    = guideItem.LeftHand,
					HaveScript = guideItem.HaveScript,
					NextEvent = guideItem.NextEvent,
					SoundID = guideItem.SoundID,
					BeiZhu = guideItem.BeiZhu,
					Offset = guideItem.Offset,
					CloseUI = guideItem.CloseUI,
					DelayTime = guideItem.DelayTime,
					EndGuide = guideItem.EndGuide,
					Animation = guideItem.Animation,
					PosType = guideItem.PosType,
					GridIndex = guideItem.GridIndex,
					Stop = guideItem.Stop,
					MoveFocusOn = guideItem.MoveFocusOn,
					WindowID = guideItem.WindowID,
					TriggerType = guideItem.TriggerType,
					CloseWindowEnd = guideItem.CloseWindowEnd,
				}
			end  
			if paramList then
				times = paramList.DelayTime/10
			else
				times = 0
			end
			self:StartGuideTime(paramList,times)
			coroutine.yield()
		end     
	end)
	coroutine.resume(self.co)
end

function UIGuideViewOld:StartGuideTime(paramList,times)
	if times == 0 then	
		self.OpenGuideMask(paramList)
	else
		if self.delayGuideTime then
			self.delayGuideTime:Stop()
		end
		if not self.delayGuideTime then
			self.delayGuideTime = TimerEx.New(self.OpenGuideMask, times, 1, paramList)
		else
			self.delayGuideTime:Reset(self.OpenGuideMask, times, 1, paramList)
		end
		self.delayGuideTime:Start()	
	end
end

--引导切换(引导时 指向图标切换控制)
function UIGuideViewOld:SwitchFrame(isTrue)
	self.objList.Btn_Skip.gameObject:SetActive(false)
	self.objList.CircleHalo.gameObject:SetActive(isTrue)
	self.objList.Img_LeftBubble.gameObject:SetActive(isTrue)
	self.objList.Img_RightBubble.gameObject:SetActive(isTrue)
	self.objList.CopyTargetContainer.gameObject:SetActive(isTrue)
	self.objList.Img_ClickMask.gameObject:SetActive(true)--进入引导步骤中时（引导未结束），不允许玩家点击其他地方    
end

function UIGuideViewOld:isShowMask(isTrue)
	self.objList.Img_ClickMask.gameObject:SetActive(isTrue)
end

function UIGuideViewOld.OnWindowCloseHandle(windowID)
	local self = UIGuideViewOld
	if not self.paramList then return end
	if self.paramList.WindowID ~= windowID then return end
	if self.paramList.CloseWindowEnd == 0 then return end
	self:SwitchFrame(false)
	self.objList.Txt_Left.text = ''
	self.objList.Txt_Right.text = ''
	if self.GuideClock then
		self.GuideClock:Stop()
	end
	if self.paramList.trans then
		self.paramList.trans:DOKill(true)
		self.paramList.trans.anchoredPosition = self.paramList.anchoredPosition
	end
	coroutine.resume(self.co)
end

--放兵结束特殊处理
function UIGuideViewOld.OnRequestUseActiveSucc()
	local self = UIGuideViewOld
	if not self.paramList or self.paramList.Animation ~= 1 then return end
	self:SwitchFrame(false)
	self.objList.Txt_Left.text = ''
	self.objList.Txt_Right.text = ''
	if self.GuideClock then
		self.GuideClock:Stop()
	end
	if self.paramList.trans then
		self.paramList.trans:DOKill(true)
		self.paramList.trans.anchoredPosition = self.paramList.anchoredPosition
	end
	if self.paramList.Stop == 1 then
		self.isPause = false
		TowerBattleManager:RequestPauseBattle(0, TowerBattlePauseType.Player)
	end
	coroutine.resume(self.co)
end

function UIGuideViewOld.TowerBattleGridMonsterUpdate()
	local self = UIGuideViewOld
	if self.paramList and (self.paramList.Animation == 3 or self.paramList.TriggerType == 1) then
		local curTime = UnityEngine.Time.time
		if self.lastUpdateTime and curTime - self.lastUpdateTime < 1 then 
			return
		end
		self.lastUpdateTime = curTime
		self:SwitchFrame(false)
		self.objList.Txt_Left.text = ''
		self.objList.Txt_Right.text = ''
		if self.GuideClock then
			self.GuideClock:Stop()
		end
		if self.paramList.trans then
			self.paramList.trans:DOKill(true)
			self.paramList.trans.anchoredPosition = self.paramList.anchoredPosition
		end
		if self.paramList.Stop == 1 then
			self.isPause = false
			TowerBattleManager:RequestPauseBattle(0, TowerBattlePauseType.Player)
		end
		coroutine.resume(self.co)
	end
end

function UIGuideViewOld.TowerBattleDeckGroupUpdate()
	local self = UIGuideViewOld
	if not self.paramList or self.paramList.Animation ~= 2 then return end
	self:SwitchFrame(false)
	self.objList.Txt_Left.text = ''
	self.objList.Txt_Right.text = ''
	if self.GuideClock then
		self.GuideClock:Stop()
	end
	self.paramList.trans:DOKill(true)
	self.paramList.trans.anchoredPosition = self.paramList.anchoredPosition
	coroutine.resume(self.co)
end

--打开引导
function UIGuideViewOld.OpenGuideMask(paramList)
	local self = UIGuideViewOld
	if not paramList then
		if self.GuideClock then
			self.GuideClock:Stop()
		end
		if self.isPause then
			TowerBattleManager:RequestPauseBattle(0, TowerBattlePauseType.Player)
		end
		local eventId_req = string.format("LuaRequestSaveGuideEvent?eventID=%d", self.eventID)
		LuaModule.RunLuaRequest(eventId_req) --向服务器发送 记录ID
		self.OnClickSkip()
		return
	end
	if self.paramList and self.paramList.trans then
		self.paramList.trans:DOKill(true)
	end
	self.paramList = paramList
	if paramList.Step == 1 and paramList.CloseUI == 1 then 
		--UIManager.CloseAllUI()
	end
	local uiConfig = Schemes.UIConfig:Get(paramList.WindowID)
	local rootObj = nil
	local focusObj = nil
	if uiConfig then
		rootObj = UIManager:GetUIContainer(uiConfig.SortOrder) --获取rootUI
		if rootObj then
			focusObj = rootObj:Find(paramList.FocusOn) --获取需要引导的图标
		end
	end

	--延迟调用（相当与延迟一帧调用）
	local delayFun = coroutine.create(function ()--延迟调用
		coroutine.wait(0.05)
		--目标路径为"None"时的处理
		if paramList.FocusOn=="None" then
			self:OpenGuideOtherConditions(paramList)--目标路径为空时的情况
			return
		end
		if not focusObj then
			warn("引导对象路径找不到!"..paramList.FocusOn)
			self.OnClickSkip()
			return
		end
		if tolua.isnull(focusObj) then return end
		if not focusObj.gameObject.activeInHierarchy then
			warn("引导对象在隐藏状态!")
			self.OnClickSkip()
			return 
		end
		self:SwitchFrame(true)
		local childCount = self.uiCopyContainer.childCount
		if childCount>0 then
			for i=childCount-1,0,-1 do
				GameObject.Destroy(self.uiCopyContainer:GetChild(i).gameObject)--销毁上一个引导图标
			end
		end
		local targetObj = GameObject.Instantiate(focusObj, self.uiCopyContainer)
		local uiTrans = targetObj.transform
		Helper.ResetTransform(uiTrans)

		targetObj.transform.position=focusObj.gameObject.transform.position --设置位置
		self.objList.CircleHalo.transform.position=focusObj.gameObject.transform.position
		targetObj:GetRectTransform().sizeDelta = focusObj.gameObject:GetRectTransform().sizeDelta
		
		--处理特殊对象，手指指向的位置有偏差（如：指向佩戴兵器时，手指未指向勾选框）
		local pos= self.circleHaloRectTrans.anchoredPosition
		local x_,y_ =0,0
		if paramList.Offset then      
			if paramList.Offset[1] then
				x_=paramList.Offset[1]
			end
			if paramList.Offset[2] then
				y_=paramList.Offset[2]
			end
		end
		self.circleHaloRectTrans.anchoredPosition=Vector2(pos.x+x_,pos.y+y_)

		local circleHaloPos=self.circleHaloRectTrans.anchoredPosition
		local trans = nil
		local anchoredPosition = nil
		local txt_obj = nil
		local moveDis = 10
		if paramList.LeftHand == 1 then
			-- 第一象限
			self.objList.Img_Right.gameObject:SetActive(true)
			self.objList.Txt_Right.gameObject:SetActive(true)
			self.objList.Img_RightBubble.gameObject:SetActive(true)
			self.objList.Img_Left.gameObject:SetActive(false)  
			self.objList.Txt_Left.gameObject:SetActive(false)
			self.objList.Img_LeftBubble.gameObject:SetActive(false)
			if paramList.IsTips == 0 then
				self.objList.Img_RightBubble.gameObject:SetActive(false)
			else
				self.objList.Img_Right.transform.localEulerAngles=Vector3(0,0,0)
				self.objList.Txt_RightBubble.text = string.gsub(paramList.TipsContent, '\\n', '\n')
				self.rightHandRectTrans.anchoredPosition = Vector2(50, -30)
				self.rightTxtRectTrans.anchoredPosition = Vector2(50, -100)
				self.rightBubbleRectTrans.anchoredPosition = Vector2(circleHaloPos.x+100, circleHaloPos.y-180)
			end		
			trans = self.rightHandRectTrans
			anchoredPosition = self.rightHandRectTrans.anchoredPosition
			self.moveAnchoredPos.x = anchoredPosition.x + moveDis 
			self.moveAnchoredPos.y = anchoredPosition.y - moveDis 
			txt_obj = self.objList.Txt_Right.gameObject
		elseif paramList.LeftHand == 2 then
			-- 第二象限
			self.objList.Img_Right.gameObject:SetActive(false)
			self.objList.Txt_Right.gameObject:SetActive(false)
			self.objList.Img_RightBubble.gameObject:SetActive(false)
			self.objList.Img_Left.gameObject:SetActive(true)  
			self.objList.Txt_Left.gameObject:SetActive(true)
			self.objList.Img_LeftBubble.gameObject:SetActive(true)
			if paramList.IsTips == 0 then
				self.objList.Img_LeftBubble.gameObject:SetActive(false)
			else
				self.objList.Img_Left.transform.localEulerAngles=Vector3(0,-180,0)
				self.objList.Txt_LeftBubble.text = string.gsub(paramList.TipsContent, '\\n', '\n')
				self.leftHandRectTrans.anchoredPosition = Vector2(-50, -30)
				self.leftTxtRectTrans.anchoredPosition = Vector2(-50, -100)
				self.leftBubbleRectTrans.anchoredPosition = Vector2(circleHaloPos.x-140, circleHaloPos.y-180)
			end
			trans = self.leftHandRectTrans
			anchoredPosition = self.leftHandRectTrans.anchoredPosition
			self.moveAnchoredPos.x = anchoredPosition.x - moveDis 
			self.moveAnchoredPos.y = anchoredPosition.y - moveDis 
			txt_obj = self.objList.Txt_Left.gameObject
		elseif paramList.LeftHand == 3 then
			-- 第三象限
			self.objList.Img_Right.gameObject:SetActive(true)
			self.objList.Txt_Right.gameObject:SetActive(true)
			self.objList.Img_RightBubble.gameObject:SetActive(true)
			self.objList.Img_Left.gameObject:SetActive(false)  
			self.objList.Txt_Left.gameObject:SetActive(false)
			self.objList.Img_LeftBubble.gameObject:SetActive(false)
			
			if paramList.IsTips == 0 then
				self.objList.Img_RightBubble.gameObject:SetActive(false)
			else
				self.objList.Txt_RightBubble.text = string.gsub(paramList.TipsContent, '\\n', '\n')
				self.rightHandRectTrans.anchoredPosition = Vector2(50, 30)
				self.rightTxtRectTrans.anchoredPosition = Vector2(50, 100)
				self.rightBubbleRectTrans.anchoredPosition = Vector2(circleHaloPos.x+140, circleHaloPos.y+180)
				self.objList.Img_Right.transform.localEulerAngles=Vector3(0,0,90)
			end
			trans = self.rightHandRectTrans
			anchoredPosition = self.rightHandRectTrans.anchoredPosition
			self.moveAnchoredPos.x = anchoredPosition.x + moveDis 
			self.moveAnchoredPos.y = anchoredPosition.y + moveDis 
			txt_obj = self.objList.Txt_Right.gameObject
		elseif paramList.LeftHand == 4 then
				-- 第四象限
			self.objList.Img_Right.gameObject:SetActive(false)
			self.objList.Txt_Right.gameObject:SetActive(false)
			self.objList.Img_RightBubble.gameObject:SetActive(false)
			self.objList.Img_Left.gameObject:SetActive(true)  
			self.objList.Txt_Left.gameObject:SetActive(true)
			self.objList.Img_LeftBubble.gameObject:SetActive(true)   
			
			if paramList.IsTips == 0 then
				self.objList.Img_LeftBubble.gameObject:SetActive(false)
			else
				self.objList.Txt_LeftBubble.text = string.gsub(paramList.TipsContent, '\\n', '\n')
				self.leftHandRectTrans.anchoredPosition = Vector2(-50, 30)
				self.leftTxtRectTrans.anchoredPosition = Vector2(-50, 100)
				self.leftBubbleRectTrans.anchoredPosition = Vector2(circleHaloPos.x-140, circleHaloPos.y+180)
				self.objList.Img_Left.transform.localEulerAngles=Vector3(0,0,-180)
			end
			trans = self.leftHandRectTrans
			anchoredPosition = self.leftHandRectTrans.anchoredPosition
			self.moveAnchoredPos.x = anchoredPosition.x - moveDis 
			self.moveAnchoredPos.y = anchoredPosition.y + moveDis 
			txt_obj = self.objList.Txt_Left.gameObject
		else
			warn("错误的LeftHand值："..paramList.LeftHand)
			self.OnClickSkip()
			return
		end
		txt_obj:SetActive(false)
		trans.localScale = Vector3(1, 1, 1)
		local remainTime = 1000
		if paramList.Stop == 1 then
			self.isPause = true
			TowerBattleManager:RequestPauseBattle(1, TowerBattlePauseType.Player)
		end
		--1 , 2 ,3 
		if paramList.Animation == 1 then--1, 动画特殊处理
			if paramList.PosType == PosType.WorldPos then
				-- 先转成屏幕坐标
				paramList.trans = trans
				paramList.anchoredPosition = anchoredPosition
				local gridConfig = Schemes.TowerBattleGrid:Get(paramList.GridIndex)
				local screenPos = RectTransformUtility.WorldToScreenPoint(SceneManager.mainCamera, Vector3(gridConfig.PosX, gridConfig.PosY, gridConfig.PosZ))
				local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.itemContainer, screenPos, UIManager:GetUICamera(), nil)
				paramList.targettPos = nil
				if result then
					paramList.targettPos = Vector2(localPoint.x+x_, localPoint.y+y_)
				end
				paramList.onAniEndFunc = function()
					UIManager:SendWndMsg(WndID.TowerBattleMain, "ShowGridFx", {index=paramList.GridIndex, showSelect=true})
					paramList.onAniStartFunc()
				end
				paramList.onAniStartFunc = function()
					if paramList.targettPos then
						paramList.trans:DOKill(true)
						paramList.trans.anchoredPosition = paramList.anchoredPosition
						local tween = paramList.trans:DOAnchorPos(Vector2(paramList.targettPos.x+x_, paramList.targettPos.y+y_), 1.5)
						tween:OnComplete(paramList.onAniEndFunc)
					end
				end
				paramList.onAniStartFunc()
			end
		elseif paramList.Animation == 3 and paramList.PosType ~= 0 then--3, 动画特殊处理
			if paramList.PosType == PosType.WorldPos then
				-- 先转成屏幕坐标				
				local gridConfig = Schemes.TowerBattleGrid:Get(tonumber(paramList.MoveFocusOn))
				local screenPos = RectTransformUtility.WorldToScreenPoint(SceneManager.mainCamera, Vector3(gridConfig.PosX, gridConfig.PosY, gridConfig.PosZ))
				local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.itemContainer, screenPos, UIManager:GetUICamera(), nil)
				
				paramList.trans = trans
				paramList.anchoredPosition = Vector2(localPoint.x+x_, localPoint.y+y_)
				gridConfig = Schemes.TowerBattleGrid:Get(paramList.GridIndex)
				screenPos = RectTransformUtility.WorldToScreenPoint(SceneManager.mainCamera, Vector3(gridConfig.PosX, gridConfig.PosY, gridConfig.PosZ))
				result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.itemContainer, screenPos, UIManager:GetUICamera(), nil)
				paramList.targettPos = nil
				if result then
					paramList.targettPos = localPoint
				end
				paramList.onAniEndFunc = function()
					paramList.onAniStartFunc()
				end
				paramList.onAniStartFunc = function()
					if paramList.targettPos then
						paramList.trans:DOKill(true)
						paramList.trans.anchoredPosition = paramList.anchoredPosition
						local tween = paramList.trans:DOAnchorPos(Vector2(paramList.targettPos.x+x_, paramList.targettPos.y+y_), 1.5)
						tween:OnComplete(paramList.onAniEndFunc)
					end
				end
				paramList.onAniStartFunc()
			end	
		elseif paramList.Animation == 2 then
				if paramList.PosType == PosType.WorldPos then
					paramList.trans = trans
					paramList.anchoredPosition = anchoredPosition
					local targetFocusObj = rootObj:Find(paramList.MoveFocusOn)
					if not targetFocusObj then
						self.OnClickSkip()
						return
					end
					if tolua.isnull(targetFocusObj) then return end
					local moveTargetObj = GameObject.Instantiate(targetFocusObj, self.uiCopyContainer)
					moveTargetObj.transform.position=targetFocusObj.gameObject.transform.position
					moveTargetObj:GetRectTransform().sizeDelta = targetFocusObj.gameObject:GetRectTransform().sizeDelta	
					moveTargetObj.gameObject.name = targetFocusObj.gameObject.name
					self.objList.Img_ClickMask.color = Color(1, 1, 1, 10/255)
					local imageBg = Helper.GetChildByName(moveTargetObj.gameObject, 'Img_BG')
					imageBg:GetComponent('Image').color = Color(1, 1, 1, 10/255)
					local screenPos = RectTransformUtility.WorldToScreenPoint(UIManager:GetUICamera(), targetFocusObj.gameObject.transform.position)
					local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.itemContainer, screenPos, UIManager:GetUICamera(), nil)
					if result then
						paramList.targettPos = Vector2(localPoint.x+x_, localPoint.y+y_)
					end
					paramList.onAniEndFunc = function()
						paramList.onAniStartFunc()
					end
					paramList.onAniStartFunc = function()
						if paramList.targettPos then
							paramList.trans:DOKill(true)
							paramList.trans.anchoredPosition = paramList.anchoredPosition
							local tween = paramList.trans:DOAnchorPos(Vector2(paramList.targettPos.x+x_, paramList.targettPos.y+y_), 1.5)
							tween:OnComplete(paramList.onAniEndFunc)
						end
					end
					paramList.onAniStartFunc()
				end
		else
			if trans then
				self.quence = DOTween.Sequence();
				self.quence:Append(trans:DOAnchorPos(self.moveAnchoredPos, 0.5):SetEase(TweeningEase.Linear, 0.5))
				self.quence:Insert(0, trans:DOScale(self.bigVec,0.5):SetEase(TweeningEase.Linear, 0.5))
				self.quence:Append(trans:DOAnchorPos(anchoredPosition, 0.5):SetEase(TweeningEase.Linear, 0.5))
				self.quence:Insert(0.5, trans:DOScale(self.smaVec,0.5):SetEase(TweeningEase.Linear, 0.5))
				self.quence:AppendInterval(0.2)
				self.quence:SetLoops(-1, TweeningLoopType.Restart)
			end
		end
		if paramList.IsLock == 0 then
			targetObj.gameObject:SetActive(false)
			self:isShowMask(false)  --为0时，隐藏遮罩，玩家可以自由点击
			GuideManager.isLockScreen = false
		else
			GuideManager.isLockScreen = true
		end
		if paramList.NextEvent == 0 then
			self.objList.Btn_Skip.gameObject:SetActive(false) --为0时，隐藏跳过按钮
		else
			self.objList.Btn_Skip.gameObject:SetActive(true)
		end  	
		
		---处理事件
		local ClickTarget = function (pointerEventData)
			if self.quence then
				self.quence:Kill()
				self.quence = nil
			end
			if self.GuideClock then
				self.GuideClock:Stop()
			end
			self.objList.Txt_Left.text = ''
			self.objList.Txt_Right.text = ''
			self:SwitchFrame(false)
			if paramList.TriggerType == 0 then
				coroutine.resume(self.co)
			end
			--点击了引导的目标
			if not tolua.isnull(focusObj) then
				local targetFocusObj = focusObj
				if paramList.ClickFocus ~= "None" then
					local focuUiTrans = focusObj.transform
					targetFocusObj = focuUiTrans:Find(paramList.ClickFocus)
				end
				local eventTrigger = targetFocusObj.gameObject:GetComponent(typeof(EventTrigger))
				if eventTrigger and eventTrigger.OnPointerClick then
					eventTrigger:OnPointerClick(pointerEventData)
				else
					targetFocusObj.gameObject:GetComponent("Button").onClick:Invoke()
				end	
			end
		end

		local function SetRemainingTime()
			self.objList.Txt_Left.text = remainTime
			self.objList.Txt_Right.text = remainTime
			if remainTime <= 10 then txt_obj:SetActive(true) end
			if remainTime <= 0 then
				ClickTarget(nil)
			end
			remainTime = remainTime - 1
		end
		
		local clickTaget = targetObj
		if paramList.ClickFocus ~= "None" then
			clickTaget = targetObj:Find(paramList.ClickFocus)
			if not clickTaget then
				clickTaget = targetObj
			end
		end
		local eventTrigger = clickTaget.gameObject:GetComponent(typeof(EventTrigger))
		if eventTrigger then 
			if paramList.Animation == 2 then
			 	targetObj.gameObject:GetComponent(typeof(EventTrigger)).triggers = focusObj.gameObject:GetComponent(typeof(EventTrigger)).triggers
			end
			eventTrigger:ClearListener(EventTriggerType.PointerClick)
		else
			eventTrigger = clickTaget.gameObject:AddComponent(typeof(EventTrigger))
		end	
		eventTrigger:AddListener(EventTriggerType.PointerClick, function(pointerEventData)
			if paramList.Animation == 2 then return end
			ClickTarget(pointerEventData)
		end)
		if not self.GuideClock then
			self.GuideClock = Timer.New(SetRemainingTime, 1, -1)
		else
			self.GuideClock:Reset(SetRemainingTime, 1, -1)
		end
		self.GuideClock:Start()
	end)
	coroutine.resume(delayFun) 
end

--打开引导：其它的情况
function UIGuideViewOld:OpenGuideOtherConditions(paramList)
	self:SwitchFrame(true)    
    local childCount = self.uiCopyContainer.childCount
	if childCount>0 then
		for i=childCount-1,0,-1 do
			GameObject.Destroy(self.uiCopyContainer:GetChild(i).gameObject)--销毁上一个引导图标
		end
	end

	self.objList.Img_Right.gameObject:SetActive(false)
	self.objList.Txt_Right.gameObject:SetActive(false)
	self.objList.Img_RightBubble.gameObject:SetActive(false)
	self.objList.Img_Left.gameObject:SetActive(true)  
	self.objList.Txt_Left.gameObject:SetActive(true)
	self.objList.Img_LeftBubble.gameObject:SetActive(true)   
 
    if paramList.LeftHand == 10 then --无目标的手指指向的处理
		GuideManager.isLockScreen = true
        self.objList.CircleHalo:SetActive(false)
        self.objList.Img_RightBubble.gameObject:SetActive(false)  
        self.objList.Img_LeftBubble.gameObject:SetActive(true)  
		self.leftBubbleRectTrans.anchoredPosition = Vector2(0, -100)
        if paramList.IsTips == 0 then
			self.objList.Img_LeftBubble.gameObject:SetActive(false)
        else
            self.objList.Txt_LeftBubble.text = string.gsub(paramList.TipsContent, '\\n', '\n')
        end
    else
        warn("错误的LeftHand值："..paramList.LeftHand)
        self.OnClickSkip()--销毁引导
    end
end

-- 窗口关闭
function UIGuideViewOld:OnClose()
    if self.delayGuideTime then
		self.delayGuideTime:Stop()
		self.delayGuideTime=nil
	end
	if self.GuideClock then
		self.GuideClock:Stop()
		self.GuideClock = nil
	end
end

-- 窗口销毁
function UIGuideViewOld:OnDestroy()
	
end

function UIGuideViewOld.OnClickSkip()
	GuideManager.isRunning = false
	GuideManager.isLockScreen = false
	local self = UIGuideViewOld
	self.paramList = nil
	self:CloseSelf()
end
return UIGuideViewOld