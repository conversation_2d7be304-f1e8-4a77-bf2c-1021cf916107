﻿// ReSharper disable InconsistentNaming

using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     围击炸弹(怪物的枪)
    /// </summary>
    public class ThingCdExecutor_2004 : MonsterGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 物件的射程
            double gunRange = Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            if (gunRange <= 0)
            {
                return;
            }

            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                return;
            }

            // 攻击距离还够不到目标，就不发子弹了。
            if (distanceEnemy.Distance > gunRange)
            {
                return;
            }

            // V36.0-修复MoveType=0怪物动画播放规则：只有确认要发射子弹时才播放攻击动画
            if (MonsterThing?.Monster != null)
            {
                Debug.Log($"V36.0 ThingCdExecutor_2004 怪物 {MonsterThing.Monster.gameObject.name} 确认发射子弹，播放攻击动画 attack01");
                MonsterThing.Monster.PlayAnimation("attack01", false, true);
            }

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 怪物已销毁，不发子弹
            if (MonsterThing == null)
            {
                return;
            }

            // 目标位置
            Vector3 targetPos = distanceEnemy.Thing2.Position;

            Vector3 bulletEndPos = Actor.Position;
            long bulletCount = Thing.GetTotalLong(PropType.BurstBulletCountList).FirstOrDefault();
            float bulletBornDistanceToActor =
                (float)Thing.GetTotalDouble(PropType.BulletBornDistanceToActor).FirstOrDefault();

            for (int i = 0; i < bulletCount; i++)
            {
                Vector3 offset =
                    (Vector3.right * bulletBornDistanceToActor).RotateAround(Vector3.forward,
                        RandomNum.RandomFloat(0, 360));
                Vector3 bulletPos = bulletEndPos + offset;

                // 创建炸弹
                BulletThing bullet = MonsterThing.CreateBullet(this, null, targetPos, 0, 0, 0, 0);

                BornSiegeBomb bornSiegeBomb = new BornSiegeBomb
                {
                    Bullet = bullet, BornPosition = bulletPos, TargetPosition = targetPos
                };

                MessageBroker.Default.Publish(bornSiegeBomb);
            }
        }
    }
}