// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;

using Cysharp.Threading.Tasks;

using DataStructure;

using HotScripts;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     抛物线子弹(枪角色)
    /// </summary>
    public class ThingCdExecutor_6 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 物件的射程
            double gunRange = Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            if (gunRange <= 0)
            {
                return;
            }

            // 找一个敌人作为攻击目标
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                return;
            }

            // 攻击距离还够不到目标，就不发子弹了
            if (distanceEnemy.Distance > gunRange)
            {
                return;
            }

            // V30.2-动画系统修复 有攻击目标时播放攻击动画attack01，智能链接后续动画
            Debug.Log($"V30.2 ThingCdExecutor_6 角色 {SingletonMgr.Instance.BattleMgr.PlayerActor.gameObject.name} 发射子弹，播放攻击动画 attack01");
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayAnimation("attack01", false, true);

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                return;
            }

            // 获取枪械ID用于调试
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token,
                Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 目标位置
                Vector3 trackPos = distanceEnemy.Thing2.Position;

                // 读取连射配置参数
                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                List<int> burstAnglesIds = Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                // 调试日志：为枪ID=2051打印详细连射参数
                if (gunId == 2051)
                {
                    //Debug.Log($"3333333333 枪ID=2051 连射参数 - 连射次数:{shootTimes}, 延时列表:[{string.Join(",", burstDelayList)}], 子弹数量列表:[{string.Join(",", burstBulletCountList)}], 角度ID列表:[{string.Join(",", burstAnglesIds)}]");
                }

                // 如果没有配置连射次数，则默认发射1次
                if (shootTimes <= 0)
                {
                    shootTimes = 1;
                    if (gunId == 2051)
                    {
                        //Debug.Log($"3333333333 枪ID=2051 连射次数为0，设置为默认值1");
                    }
                }

                // 如果没有配置延时，则默认立即发射
                if (burstDelayList.Count == 0)
                {
                    burstDelayList.Add(0);
                    if (gunId == 2051)
                    {
                        //Debug.Log($"3333333333 枪ID=2051 延时列表为空，添加默认值0");
                    }
                }

                // 如果没有配置子弹数量，则默认每次1发
                if (burstBulletCountList.Count == 0)
                {
                    burstBulletCountList.Add(1);
                    if (gunId == 2051)
                    {
                        //Debug.Log($"3333333333 枪ID=2051 子弹数量列表为空，添加默认值1");
                    }
                }

                // 根据配置延时后射击
                for (int i = 0; i < shootTimes; i++)
                {
                    double delay = burstDelayList.Count > i ? burstDelayList[i] : burstDelayList.LastOrDefault();
                    long bulletCount = burstBulletCountList.Count > i ? burstBulletCountList[i] : burstBulletCountList.LastOrDefault();
                    int anglesId = burstAnglesIds.Count > i ? burstAnglesIds[i] : (burstAnglesIds.Count > 0 ? burstAnglesIds.LastOrDefault() : 0);

                    if (gunId == 2051)
                    {
                        //Debug.Log($"3333333333 枪ID=2051 第{i+1}轮射击 - 延时:{delay}秒, 子弹数量:{bulletCount}, 角度ID:{anglesId}");
                    }

                    BurstOne(cts_Skill.Token, (float)delay, distanceEnemy.Thing2, trackPos, bulletCount, anglesId, i + 1).Forget();
                }
            }
            catch (Exception ex)
            {
                if (gunId == 2051)
                {
                    //Debug.LogError($"3333333333 枪ID=2051 连射逻辑异常: {ex.Message}");
                }
                Debug.LogException(ex);
            }
        }

        /// <summary>
        ///     发射一轮抛物线子弹
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准目标</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="bulletCount">发射的子弹数量</param>
        /// <param name="anglesId">角度配置ID</param>
        /// <param name="burstIndex">连射轮次</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3 trackPos, long bulletCount, int anglesId, int burstIndex)
        {
            // 获取枪械ID用于调试
            int gunId = Thing is GunThing gunThing ? gunThing.CsvRow_Gun.Value.Id : 0;

            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                if (gunId == 2051)
                {
                    //Debug.Log($"3333333333 枪ID=2051 第{burstIndex}轮射击开始 - 延时{delay}秒后, 准备发射{bulletCount}颗子弹, 角度ID:{anglesId}");
                }

                // 获取角度配置
                List<float> angles = new List<float> { 0f }; // 默认角度
                if (anglesId > 0 && SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesId, out CommonProp anglesProp))
                {
                    angles = anglesProp.DoubleValues.Select(x => (float)x).ToList();
                    if (gunId == 2051)
                    {
                        //Debug.Log($"3333333333 枪ID=2051 第{burstIndex}轮 找到角度配置ID:{anglesId}, 角度列表:[{string.Join(",", angles)}]");
                    }
                }
                else if (anglesId > 0)
                {
                    if (gunId == 2051)
                    {
                        //Debug.LogWarning($"3333333333 枪ID=2051 第{burstIndex}轮 未找到角度配置ID:{anglesId}, 使用默认角度0");
                    }
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                // 计算发射点到原始目标点的距离和方向
                Vector3 shooterPos = Actor.Position;
                Vector3 originalTarget = trackPos;
                float targetDistance = Vector3.Distance(shooterPos, originalTarget);
                Vector3 baseDirection = (originalTarget - shooterPos).normalized;

                // 发射多颗子弹
                for (int i = 0; i < bulletCount; i++)
                {
                    float angle = angles.Count > 0 ? angles[i % angles.Count] : 0f;
                    
                    // 计算当前角度对应的目标点
                    Vector3 currentTargetPos;
                    if (Mathf.Abs(angle) < 0.01f) // 0度角，射向原始目标
                    {
                        currentTargetPos = originalTarget;
                    }
                    else
                    {
                        // 其他角度：在以发射点为圆心、目标距离为半径的圆上计算新目标点
                        // 将角度转换为弧度
                        float angleRad = angle * Mathf.Deg2Rad;
                        
                        // 计算基准方向的角度（相对于X轴正方向）
                        float baseAngleRad = Mathf.Atan2(baseDirection.y, baseDirection.x);
                        
                        // 加上偏移角度
                        float finalAngleRad = baseAngleRad + angleRad;
                        
                        // 计算新的目标点
                        currentTargetPos = shooterPos + new Vector3(
                            Mathf.Cos(finalAngleRad) * targetDistance,
                            Mathf.Sin(finalAngleRad) * targetDistance,
                            0f
                        );
                    }
                    
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 创建抛物线子弹（注意：这里angle参数传0，因为目标位置已经按角度计算好了）
                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, currentTargetPos, 0,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    // 设置子弹发射位置和目标位置
                    bullet.Position = Actor.Position;
                    bullet.TrackPosition = currentTargetPos;

                    // 调试日志：记录发射者和子弹的位置信息
                    Vector3 playerActorPosition = SingletonMgr.Instance.BattleMgr.PlayerActor != null 
                        ? SingletonMgr.Instance.BattleMgr.PlayerActor.transform.position 
                        : Vector3.zero;

                    if (gunId == 2051)
                    {
                        //Debug.Log($"3333333333 枪ID=2051 第{burstIndex}轮第{i+1}颗子弹 - 角度:{angle}, Actor.Position:{Actor.Position}, bullet.Position:{bullet.Position}, PlayerActor坐标:{playerActorPosition}, 原始目标:{originalTarget}, 计算目标:{currentTargetPos}");
                    }
                    else
                    {
                        //Debug.Log($"44444444 ThingCdExecutor_6创建子弹 - 第{burstIndex}轮第{i+1}颗, 角度:{angle}, Actor.Position:{Actor.Position}, bullet.Position:{bullet.Position}, PlayerActor坐标:{playerActorPosition}, 原始目标:{originalTarget}, 计算目标:{currentTargetPos}");
                    }

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, 
                        PositionPre = bullet.Position
                    });
                }

                if (gunId == 2051)
                {
                    //Debug.Log($"3333333333 枪ID=2051 第{burstIndex}轮射击完成 - 已发射{bulletCount}颗抛物线子弹");
                }
            }
            catch (OperationCanceledException) 
            { 
                if (gunId == 2051)
                {
                    //Debug.Log($"3333333333 枪ID=2051 第{burstIndex}轮射击被取消");
                }
                throw; 
            }
            catch (MissingReferenceException) 
            { 
                if (gunId == 2051)
                {
                    //Debug.LogWarning($"3333333333 枪ID=2051 第{burstIndex}轮射击引用丢失");
                }
            }
            catch (Exception ex)
            {
                if (gunId == 2051)
                {
                    //Debug.LogError($"3333333333 枪ID=2051 第{burstIndex}轮射击异常: {ex.Message}");
                }
                Debug.LogException(ex);
            }
        }
    }
}