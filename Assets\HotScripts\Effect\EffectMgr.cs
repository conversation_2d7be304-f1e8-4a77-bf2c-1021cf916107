﻿using System;
using System.Collections.Generic;
using System.Linq;

using Cysharp.Threading.Tasks;

using UnityEngine;

/// <summary>
/// 特效管理器，每个特效对象必须加上EffectUnit组件
/// </summary>
public class EffectMgr : Singleton<EffectMgr>
{
    public List<EffectUnit> UseEffects = new List<EffectUnit>();
    public List<EffectUnit> UnuseEffects = new List<EffectUnit>();

    protected override void Awake()
    {
        DontDestroyOnLoad(gameObject);
    }

    void Update()
    {
        if (Time.timeScale == 0) return;
        for (int i = 0; i < UseEffects.Count; i++)
        {
            UseEffects[i].OnUpdate();
        }

        for (int i = 0; i < UnuseEffects.Count; i++)
        {
            UnuseEffects[i].OnUpdate();
        }
    }

    private void OnDestroy()
    {
        ReleaseAllEffect();
    }

    /// <summary>
    /// 获取model/prefab/Bullet下的特效路径
    /// </summary>
    /// <param name="prefabName"></param>
    /// <returns></returns>
    public string GetEffectPath(string prefabName)
    {
        return $"Assets/Temp/model/prefab/Bullet/{prefabName}.prefab";
    }

    /// <summary>
    /// 显示特效
    /// </summary>
    /// <param name="effectPath"></param>
    /// <param name="pos"></param>
    /// <param name="scale"></param>
    /// <param name="parent"></param>
    /// <param name="duringTime"></param>
    /// <param name="maxActiveNum"></param>
    public async UniTaskVoid ShowEffect(string effectPath, Vector3 pos, float scale = 1,
        Transform parent = null, float duringTime = 0, int maxActiveNum = 5,Action<GameObject> callback = null)
    {
        if (UseEffects.Count(e => e.ResPath == effectPath) >= maxActiveNum) return;
        EffectUnit effect = UnuseEffects.FirstOrDefault(e => e.ResPath == effectPath);
        if (!effect)
        {
            GameObject pref = await ResMgr.LoadResAsyncHandle<GameObject>(effectPath).Task;
            if (!pref) return;
            GameObject obj = Instantiate(pref);
            effect = obj.GetComponent<EffectUnit>();
            if (!effect) effect = obj.AddComponent<EffectUnit>();
        }
        else
        {
            UnuseEffects.Remove(effect);
        }

        parent = !parent ? transform : parent;
        effect.transform.SetParent(parent);
        effect.transform.localScale = Vector3.one * scale;
        effect.ResPath = effectPath;
        if (duringTime > 0) effect.SetPlayTime(duringTime);
        if (parent) effect.transform.localPosition = pos;
        else effect.transform.position = pos;
        effect.gameObject.SetActive(true);
        UseEffects.Add(effect);
        callback?.Invoke(effect.gameObject);
    }

    public void ReleaseEffect(EffectUnit effect)
    {
        UseEffects.Remove(effect);
        if(effect == null || effect.gameObject == null || GameObject.Find(effect.gameObject.name) == null) {
            // 对象不在场景中
        } else {
            // 对象在场景中
            Destroy(effect.gameObject);
        }
    }

    /// <summary>
    /// 释放所有特效资�?
    /// </summary>
    public void ReleaseAllEffect()
    {
        for (int i = 0; i < UseEffects.Count; i++)
        {
            if (UseEffects[i] != null)
            {
                Destroy(UseEffects[i].gameObject);
            }
        }

        UseEffects.Clear();
        for (int i = 0; i < UnuseEffects.Count; i++)
        {
            if (UnuseEffects[i] != null)
            {
                Destroy(UnuseEffects[i].gameObject);
            }
        }

        UnuseEffects.Clear();
    }
}

/// <summary>
/// 特效资源路径
/// </summary>
public class EffectPath
{
    public const string JS_huan_Nv01_skill01_hit = "Assets/Temp/model/prefab/Bullet/JS_huan_Nv01_skill01_hit.prefab";
    public const string JS_jian_Nv01_skill3 = "Assets/Temp/model/prefab/Bullet/JS_jian_Nv01_skill3.prefab";
    public const string behit_NvFuZhu01 = "Assets/Temp/fx/behit_NvFuZhu01.prefab";

    /// <summary>
    /// 特效--战斗背包装备合成
    /// </summary>
    public const string ui_JiaoSe_QiangHua = "Assets/Temp/fx/UI_JiaoSe_QiangHua.prefab";

    public const string ui_GainCoin = "Assets/Temp/fx/UI_GainCoin.prefab";
    public const string BornCircle = "Assets/Temp/fx/BornCircle.prefab";
    public const string WarningPathComp = "Assets/Temp/fx/WarningPathComp.prefab";
    public const string WarningCircleComp = "Assets/Temp/fx/WarningCircleComp.prefab";
    public const string BaoDian01 = "Assets/model/tex/BaoDian01.prefab";
    public const string BaoDian02 = "Assets/model/tex/BaoDian02.prefab";
    public const string BaoDian03 = "Assets/model/tex/BaoDian03.prefab";
};