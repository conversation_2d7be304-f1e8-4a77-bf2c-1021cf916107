local luaID = ('UIRankView')

local UIRankView = {}

-- 订阅事件列表
function UIRankView:GetOpenEventList()
	return
	{
		[EventID.OnRankingListUpdate] = self.UpdateData,
	}
end 

-- 初始化
function UIRankView:OnCreate()
	self.objList.Txt_Btn1.text = GetGameText(luaID, 1)
	self.objList.Txt_Btn2.text = GetGameText(luaID, 2)
	--self.objList.Txt_Btn3.text = GetGameText(luaID, 3)
	--self.objList.Txt_Btn4.text = GetGameText(luaID, 4)
	--self.objList.Txt_Btn5.text = GetGameText(luaID, 9)
	self.objList.Txt_Btn1_1.text = GetGameText(luaID, 1)
	self.objList.Txt_Btn2_1.text = GetGameText(luaID, 2)
	--self.objList.Txt_Btn3_1.text = GetGameText(luaID, 3)
	--self.objList.Txt_Btn4_1.text = GetGameText(luaID, 4)
	--self.objList.Txt_Btn5_1.text = GetGameText(luaID, 9)
	self.objList.Btn_Btn1_1.onClick:AddListenerEx(self.OnClickSelectBtn1)
	self.objList.Btn_Btn2_1.onClick:AddListenerEx(self.OnClickSelectBtn2)
	--self.objList.Btn_Btn3_1.onClick:AddListenerEx(self.OnClickSelectBtn3)
	--self.objList.Btn_Btn4_1.onClick:AddListenerEx(self.OnClickSelectBtn4)
	--self.objList.Btn_Btn5_1.onClick:AddListenerEx(self.OnClickSelectBtn5)
	
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Txt_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_mask.onClick:AddListenerEx(self.OnClickClose)
		
	self.selectTrans = self.objList.Img_SelectTab:GetRectTransform()	
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_Btn1_1, self.objList.Btn_Btn2_1, self.objList.Btn_Btn3_1, self.objList.Btn_mask }	
	self.selectBtn = -1
	self.rankItem = self.objList.RankItem
	self.rankItem.gameObject:SetActive(false)
	self.itemsContent = self.objList.Container:GetRectTransform()
	self.rankItemList = {}
	
	return true
end


-- 窗口开启
function UIRankView:OnOpen()	
	self.OnClickSelectBtn1()
end

function UIRankView.OnClickSelectBtn1()
	local self = UIRankView
	if self.selectBtn == 1 then return end
	self.selectBtn = 1
	self:SetButtonState()
	RankModule.RequestGetRankList(RANK_TYPE.RANK_TYPE_MONEY)
	local data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_MONEY)
	if data then
		self:UpdateView(data)
	end
end

function UIRankView.OnClickSelectBtn2()
	local self = UIRankView
	if self.selectBtn == 2 then return end
	self.selectBtn = 2
	self:SetButtonState()
	RankModule.RequestGetRankList(RANK_TYPE.RANK_TYPE_LEVEL)
	local data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_LEVEL)
	if data then
		self:UpdateView(data)
	end
end

function UIRankView.OnClickSelectBtn3()
	local self = UIRankView
	if self.selectBtn == 3 then return end
	self.selectBtn = 3
	self:SetButtonState()	
	RankModule.RequestGetRankList(RANK_TYPE.RANK_TYPE_STAGECHALLENGERECORD)	
	local data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_STAGECHALLENGERECORD)
	if data then
		self:UpdateView(data)
	end
end
function UIRankView.OnClickSelectBtn4()
	local self = UIRankView
	if self.selectBtn == 4 then return end
	self.selectBtn = 4
	self:SetButtonState()
	RankModule.RequestGetRankList(RANK_TYPE.RANK_TYPE_MATCHRANKRECORD)
	local data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_MATCHRANKRECORD)
	if data then
		self:UpdateView(data)
	end
end
function UIRankView.OnClickSelectBtn5()
	local self = UIRankView
	if self.selectBtn == 5 then return end
	self.selectBtn = 5
	self:SetButtonState()
	RankModule.RequestGetRankList(RANK_TYPE.RANK_TYPE_MATCHSCORERANKRECORD)
	local data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_MATCHSCORERANKRECORD)
	if data then
		self:UpdateView(data)
	end
end

function UIRankView.UpdateData()
	local self = UIRankView	
	local data = nil
	if self.selectBtn == 1 then
		data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_MONEY)--财富
	elseif self.selectBtn == 2 then
		data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_LEVEL)-- 等级
	elseif self.selectBtn == 3 then
		data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_STAGECHALLENGERECORD)-- 挑战关卡记录
	elseif self.selectBtn == 4 then
		data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_MATCHRANKRECORD)-- 真实玩家充值榜
	elseif self.selectBtn == 5 then
		data = RankModule.GetRankData(RANK_TYPE.RANK_TYPE_MATCHSCORERANKRECORD)-- 副本塔
	end
	if data then
		self:UpdateView(data)
	end
end

-- 点击返回按钮
function UIRankView.OnClickClose()
	local self = UIRankView
	self:CloseSelf()	
end

function UIRankView:UpdateView(data)
	local hero = EntityModule.hero
	if not hero then
		return
	end
	for k,v in ipairs(self.rankItemList) do
		v.gameObject:SetActive(false)
	end

	local index = 1
	--HelperL.Dump(data)
	--print("data.RankList:"..#data.RankList)
	local selfRankValue = ''
	for k,v in ipairs(data.RankList) do
		local rankItem = self.rankItemList[index]
		if not rankItem then
			rankItem = {}
			local obj = GameObject.Instantiate(self.rankItem, self.itemsContent)
			local objTrans = obj:GetRectTransform()
			Helper.FillLuaComps(objTrans, rankItem)
			rankItem.gameObject = obj
			rankItem.objTrans = objTrans
			table.insert(self.rankItemList, rankItem)
		end
		local sprite = ''
		--排名
		if k==1 then
			sprite = 'jinbei'
			rankItem.Img_HeadIcon.gameObject:SetActive(true)
			rankItem.Txt_RankLevel.gameObject:SetActive(false)
		elseif k==2 then
			sprite = 'yinbei'
			rankItem.Img_HeadIcon.gameObject:SetActive(true)
			rankItem.Txt_RankLevel.gameObject:SetActive(false)
		elseif k==3 then
			sprite 'tongbei'
			rankItem.Img_HeadIcon.gameObject:SetActive(true)
			rankItem.Txt_RankLevel.gameObject:SetActive(false)
		else
			rankItem.Txt_RankLevel.text = string.format(GetGameText(luaID,8),index)
			rankItem.Txt_RankLevel.gameObject:SetActive(true)
			rankItem.Img_HeadIcon.gameObject:SetActive(false)
		end
		AtlasManager:AsyncGetSprite(sprite, rankItem.Img_HeadIcon)
		rankItem.gameObject:SetActive(true)	
		
		rankItem.Txt_Name.text = v.ActorName
		
		
		--排行类型数据显示
		if data.RankType == RANK_TYPE.RANK_TYPE_MONEY then
			local num_str = HelperL.ExChangeNum(v.Value1)
			rankItem.Txt_ValueName.text = GetGameText(luaID, 10)
			rankItem.Txt_Value.text = num_str
			if data.SelfRank == index then
				selfRankValue = string.format(GetGameText(luaID, 10), num_str)
			end
			if k==1 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,13))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			elseif k==2 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,14))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=purple>%s</color>',v.ActorName)
			elseif k==3 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,15))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			else
				rankItem.Txt_Rank.gameObject:SetActive(false)
			end
		elseif data.RankType == RANK_TYPE.RANK_TYPE_LEVEL then
			rankItem.Txt_ValueName.text = GetGameText(luaID, 11)
			rankItem.Txt_Value.text = v.Value1
			if data.SelfRank == index then
				selfRankValue = string.format(GetGameText(luaID, 11), v.Value1)
			end
			if k==1 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,16))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			elseif k==2 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,17))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=purple>%s</color>',v.ActorName)
			elseif k==3 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,18))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			else
				rankItem.Txt_Rank.gameObject:SetActive(false)
			end
		elseif data.RankType == RANK_TYPE.RANK_TYPE_STAGECHALLENGERECORD then
			--rankItem.StarContent.gameObject:SetActive(false)
			rankItem.Txt_Value.text = string.format(GetGameText(luaID, 6), v.Value1, v.Value2)
			if data.SelfRank == index then
				selfRankValue = string.format(GetGameText(luaID, 6), v.Value1, v.Value2)
			end
			if k==1 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,19))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			elseif k==2 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,20))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=purple>%s</color>',v.ActorName)
			elseif k==3 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,21))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			else
				rankItem.Txt_Rank.gameObject:SetActive(false)
			end
		elseif data.RankType == RANK_TYPE.RANK_TYPE_MATCHRANKRECORD then
			--rankItem.StarContent.gameObject:SetActive(true)
			local segConfig = Schemes.SegmentUpStar:GetBySegAndStar(v.Value1, v.Value2)
			if segConfig then
				rankItem.Txt_Value.text = '<color=#fa1800>'..segConfig.Name..'</color>'
			else
				rankItem.Txt_Value.text = ""
			end
			rankItem.Txt_Value.text = string.format(GetGameText(luaID, 12), v.Value1)
			if data.SelfRank == index then
				selfRankValue = string.format(GetGameText(luaID, 12), v.Value1)
			end
			if k==1 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,22))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			elseif k==2 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,23))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=purple>%s</color>',v.ActorName)
			elseif k==3 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,24))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			else
				rankItem.Txt_Rank.gameObject:SetActive(false)
			end
		elseif data.RankType == RANK_TYPE.RANK_TYPE_MATCHSCORERANKRECORD then
			--rankItem.StarContent.gameObject:SetActive(false)
			rankItem.Txt_Value.text = string.format(GetGameText(luaID, 7), v.Value1, v.Value2 / 100)
			if data.SelfRank == index then
				selfRankValue = string.format(GetGameText(luaID, 7), v.Value1, v.Value2 / 100)
			end
			if k==1 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,25))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			elseif k==2 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,26))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=purple>%s</color>',v.ActorName)
			elseif k==3 then
				rankItem.Txt_Rank.text = string.format(GetGameText(luaID,27))
				rankItem.Txt_Rank.gameObject:SetActive(true)
				rankItem.Txt_Name.text = string.format('<color=yellow>%s</color>',v.ActorName)
			else
				rankItem.Txt_Rank.gameObject:SetActive(false)
			end
		end

		index = index + 1
	end
	self.objList.Txt_SelfName.text = hero.name
	local selfData = RankModule.MatchRecordData
	if not selfData then
		self.objList.Txt_SelfRank.text = GetGameText(luaID, 5)
		self.objList.Txt_SelfValue.text = ""
	else		
		if data.SelfRank == 0 then
			self.objList.Txt_SelfRank.text = ''
			self.objList.Txt_SelfValue.text = GetGameText(luaID, 5)
		else
			HelperL.Dump(selfData)
			self.objList.Txt_SelfRank.text = string.format(GetGameText(luaID,8), data.SelfRank)
			if selfRankValue and selfRankValue~='' then
				self.objList.Txt_SelfValue.text = selfRankValue

			else
				self.objList.Txt_SelfValue.text = GetGameText(luaID, 5)
			end
			
			
		end
	end
end

function UIRankView:SetButtonState()
	self.objList.Btn_Btn1.gameObject:SetActive(self.selectBtn == 1)
	self.objList.Btn_Btn2.gameObject:SetActive(self.selectBtn == 2)
	--self.objList.Btn_Btn3.gameObject:SetActive(self.selectBtn == 3)
	--self.objList.Btn_Btn4.gameObject:SetActive(self.selectBtn == 4)
	--self.objList.Btn_Btn5.gameObject:SetActive(self.selectBtn == 5)
	self.objList.Btn_Btn1_1.gameObject:SetActive(self.selectBtn ~= 1)
	self.objList.Btn_Btn2_1.gameObject:SetActive(self.selectBtn ~= 2)
	--self.objList.Btn_Btn3_1.gameObject:SetActive(self.selectBtn ~= 3)
	--self.objList.Btn_Btn4_1.gameObject:SetActive(self.selectBtn ~= 4)
	--self.objList.Btn_Btn5_1.gameObject:SetActive(self.selectBtn ~= 5)
	
	--self.objList.Txt_SelectTab.text = GetGameText(luaID, self.selectBtn+1)
	--if self.selectBtn == 1 then
		--self.objList.Img_SelectTab.transform.position = self.objList.Btn_Btn1.transform.position
	--elseif self.selectBtn == 2 then
		--self.objList.Img_SelectTab.transform.position = self.objList.Btn_Btn2.transform.position
	--elseif self.selectBtn == 3 then
		--self.objList.Img_SelectTab.transform.position = self.objList.Btn_Btn3.transform.position
	--end
end

-- 窗口销毁
function UIRankView:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end
return UIRankView