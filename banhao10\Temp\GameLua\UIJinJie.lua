--[[
********************************************************************
    created:    2024/02/08
    author :    李锦剑
    purpose:    装备进阶/培养
*********************************************************************
--]]
local luaID = ('UIJinJie')

---@class UIJinJie:UIWndBase 装备宝箱界面
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.EquipmentReplace] = m.UpdateView,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()

end

--------------------------------------------------------------------
--创建时事件
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Txt_Title1.text = GetGameText(luaID, 1)
    m.objList.Txt_Title2.text = GetGameText(luaID, 2)
    m.AttributeItemList1 = {}
    m.AttributeItemList2 = {}

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m:OnOpen(equipID)
    m.lastTime = 0
    m.selectEquipID = equipID
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Get.onClick:AddListenerEx(m.OnClickUpgrade)
    m.objList.Btn_Upgrade.onClick:AddListenerEx(m.OnClickUpgrade)
end

--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.CreationAttribute2(parent, index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Attribute2)
    ---更新数据
    ---@param data {Quality:integer, IsLock:boolean, Content1:string, Content2:string}
    item.UpdateData = function(data)
        if data then
            local str, _ = string.gsub(data.Content1 or '', " ", "　")
            item.com.Txt_Value1.text = str
            str, _ = string.gsub(data.Content2 or '', " ", "　")
            item.com.Txt_Value2.text = str
            item.com.Img_Lock.gameObject:SetActive(data.IsLock == true)
            if not data.IsLock and data.Quality > 0 then
                item.com.Img_Type.gameObject:SetActive(true)
                item.com.Img_Type.color = HelperL.GetQualityColorRGBA(data.Quality)
            else
                item.com.Img_Type.gameObject:SetActive(false)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    if not m.selectEquipID then
        return
    end
    if not m.JinJieUI_Item then
        m.JinJieUI_Item = _GAddSlotItem(m.objList.Obj_Equip1)
    end
    m.JinJieUI_Item:SetItemID(m.selectEquipID)

    local equipCfg = Schemes.Equipment:Get(m.selectEquipID)
    local smeltID = equipCfg.SmeltID
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if not equipSmeltStar then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end
    local str = equipCfg.GoodsName
    if starLvl > 0 then
        str = str .. "+" .. starLvl
    end
    m.objList.Txt_EquipName2.text = str

    local goods_id = equipSmeltStar.CostGoodsID1
    local goods_id2 = equipSmeltStar.CostGoodsID2
    local cost_goodsnum = equipSmeltStar.CostGoodsID1Num
    local cost_goodsnum2 = equipSmeltStar.CostGoodsID2Num
    local goodsNum = SkepModule:GetGoodsCount(goods_id)
    local goodsNum2 = SkepModule:GetGoodsCount(goods_id2)

    m.objList.Btn_Get.gameObject:SetActive(false)
    m.objList.Btn_Upgrade.gameObject:SetActive(false)
    m.objList.Btn_FullLevel.gameObject:SetActive(false)
    m.objList.Img_Exp.gameObject:SetActive(false)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
    --是否是满级
    if starLvl >= maxLevel then
        m.objList.Btn_FullLevel.gameObject:SetActive(true)
    else
        m.objList.Img_Exp.gameObject:SetActive(true)
        if goods_id > 0 and goodsNum >= cost_goodsnum and goodsNum2 >= cost_goodsnum2 then
            m.objList.Btn_Upgrade.gameObject:SetActive(true)
        else
            m.objList.Btn_Get.gameObject:SetActive(true)
        end
        AtlasManager:AsyncGetGoodsSprite(goods_id, m.objList.Img_Cost1)
        AtlasManager:AsyncGetGoodsSprite(goods_id2, m.objList.Img_Cost2)
    end
    local color = goodsNum >= cost_goodsnum and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_Cost1.text = string.format('<color=%s>%s/%s</color>', color, goodsNum, cost_goodsnum)
    local color2 = goodsNum2 >= cost_goodsnum2 and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_Cost2.text = string.format('<color=%s>%s/%s</color>', color2, goodsNum2, cost_goodsnum2)
    m.objList.Txt_Cost2.gameObject:SetActive(cost_goodsnum2 > 0)


    --获取基础属性
    local list2 = HelperL.Split(equipCfg.EffectTipsID, ";")
    local attrList = {}

    local content
    local commonProp = Schemes.CommonProp:Get(list2[1])
    if commonProp then
        local _value = tonumber(commonProp.StrValues[0]) or 0
        --获取当前级基础属性
        local value1 = _value
        local commonProp2 = Schemes.CommonProp:Get(equipSmeltStar.PropId)
        if commonProp2 then
            value1 = _value * (tonumber(commonProp2.StrValues[0]) or 0)
        end

        local value2 = 0
        --不是满级，就获取下一级基础属性
        if starLvl < maxLevel then
            local equipSmeltStar2 = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl + 1)
            if equipSmeltStar2 then
                commonProp2 = Schemes.CommonProp:Get(equipSmeltStar2.PropId)
                if commonProp2 then
                    value2 = _value * (tonumber(commonProp2.StrValues[0]) or 0)
                end
            end
        end

        local _attrType = PropertyCompute.GetCommonPropPropType(commonProp.Id)
        local unit = ''
        if AttributeShowType[_attrType] == 2 then
            value1 = HelperL.Round((value1 / 100), 2)
            value2 = HelperL.Round((value2 / 100), 2)
            unit = '%'
        else
            value1 = math.floor(value1)
            value2 = math.floor(value2)
        end

        content = string.format("%s %s", GetAttributeTypeDesc(_attrType), value1 .. unit)
        if value2 > 0 then
            content = string.format("%s  (<color=#00FF00>%s</color>)", content, value2 .. unit)
        end

        table.insert(attrList, {
            Quality = 0,
            IsLock = false,
            Content1 = content,
            Content2 = '',
        })
    end
    local num = math.max(#m.AttributeItemList1, #attrList)
    for i = 1, num, 1 do
        if not m.AttributeItemList1[i] then
            m.AttributeItemList1[i] = m.CreationAttribute2(m.objList.Grid_Attribute1, i)
        end
        m.AttributeItemList1[i].UpdateData(attrList[i])
    end

    --获取特殊属性
    attrList = {}
    local isLock, quality
    for i = 2, #list2, 1 do
        commonProp = Schemes.CommonProp:Get(list2[i])
        if commonProp then
            quality = i - 1
            isLock = quality > equipCfg.QualityLevel

            if isLock then
                content = string.format(GetGameText(luaID, 3), commonProp.Remark, HelperL.GetNameByQuality(quality))
            else
                content = string.format("<color=#%s>%s</color>", HelperL.GetColorByQualityEquip(quality),
                    commonProp.Remark)
            end
            table.insert(attrList, {
                Quality = quality,
                IsLock = isLock,
                Content1 = content,
                Content2 = '',
            })
        end
    end
    num = math.max(#m.AttributeItemList2, #attrList)
    for i = 1, num, 1 do
        if not m.AttributeItemList2[i] then
            m.AttributeItemList2[i] = m.CreationAttribute2(m.objList.Grid_Attribute2, i)
        end
        m.AttributeItemList2[i].UpdateData(attrList[i])
    end
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(result, content)
    if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
        -- m.UpdateView()
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
    end
end

--------------------------------------------------------------------
-- 装备升级事件
--------------------------------------------------------------------
function m.OnClickUpgrade()
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('装备升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local equipCfg = Schemes.Equipment:Get(m.selectEquipID)
    local smeltID = equipCfg.SmeltID
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

return m
