using UnityEngine;

/// <summary>
/// 声音ID
/// </summary>
public class SoundID {
    // ---------------音乐---------------------- -

    /// <summary>
    /// /音乐--登入界面
    /// </summary>
    public const int Login = 1;
    /// <summary>
    /// 音乐--主界面
    /// </summary>
    public const int Main = 1;

    //-------------- - 音效---------------------- -

    /// <summary>
    /// 音效--按钮通用
    /// </summary>
    public const int CommonButtonSound = 8000;
    /// <summary>
    /// 音效--关闭按钮通用
    /// </summary>
    public const int CommonCloseButtonSound = 8001;
    /// <summary>
    /// -音效--升级升星成功
    /// </summary>
    public const int Upgrade = 8002;
    /// <summary>
    /// 音效--获得奖励（包括十连抽）
    /// </summary>
    public const int GetAewarded = 8003;
    /// <summary>
    /// 音效--胜利
    /// </summary>
    public const int Success = 8004;
    /// <summary>
    /// 音效--失败
    /// </summary>
    public const int Failure = 8005;
    /// <summary>
    /// 音效--复活界面
    /// </summary>
    public const int ReviveDialog = 8006;
    /// <summary>
    /// 音效--战斗背包装备合成
    /// </summary>
    public const int Synthesis = 8007;
    /// <summary>
    /// 音效--战斗背包装备取放
    /// </summary>
    public const int EquipDrag = 8008;
    /// <summary>
    /// 音效--战斗刷新3选1
    /// </summary>
    public const int RefreshSkill = 8009;
    /// <summary>
    /// 音效--战斗弹出3选1界面
    /// </summary>
    public const int SkillUI = 8010;
    /// <summary>
    /// 音效--战斗增加金币
    /// </summary>
    public const int AddGold = 8011;
}

public class AudioManager {
    public static void CSharpPlaySound(int soundID) {
        LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", soundID);
    }

    public static void CSharpPlayMusic(int soundID) {
        LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlayMusic", soundID);
    }

    public static void CSharpPauseMusic() {
        LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPauseMusic");
    }
}