--[[
********************************************************************
    created:    2024/04/02
    author :    李锦剑
    purpose:    装备背包
*********************************************************************
--]]
local luaID = ('UIGoodTips')

local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
	m.<PERSON>()
	return true
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
	m.objList.Btn_Ok.onClick:AddListenerEx(m.OnClickClose)
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(itemID)
	local cfg = Schemes:GetGoodsConfig(itemID)
	if cfg then
		local quality = 0
		if HelperL.IsEuipType(cfg.ID) then
			quality = cfg.QualityLevel
		else
			quality = cfg.Quality
		end
		m.objList.Txt_Name.text = string.format(
			"<color=#%s>%s</color>",
			HelperL.GetColorByQualityEquip(quality),
			cfg.GoodsName
		)
		if not m.SlotItem then
			m.SlotItem = _GAddSlotItem(m.objList.Obj_Item)
			m.SlotItem:EnableClick(false)
		end
		m.SlotItem:SetItemID(cfg.ID)
		m.objList.Txt_Desc.text = HelperL.GetContenBRText(cfg.TipsDes)
	end
end

--------------------------------------------------------------------
-- 窗口关闭
--------------------------------------------------------------------
function m.OnClickClose()
	m:CloseSelf()
end

return m
