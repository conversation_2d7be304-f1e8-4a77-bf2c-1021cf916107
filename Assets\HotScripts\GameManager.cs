// ReSharper disable InconsistentNaming
// ReSharper disable Ident<PERSON>Typo

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;

using Apq;
using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using CsvTables;

using Cysharp.Threading.Tasks;

using DataStructure;

using DG.Tweening;

using HotScripts;

using Newtonsoft.Json;

using Props;

using RxEventsM2V;

using RxEventsV2M;

using Thing;

using UniRx;

using UnityEngine;

using View;

using ViewModel;

using X;

using X.PB;

[DisallowMultipleComponent]
public class GameManager : MonoBehaviour
{
    public CameraMovement cameraMovement;

    private MonsterPool MonsterPool_m;

    /// <summary>
    ///     是否在战斗中(背包界面不算)
    /// </summary>
    public BubblingList<bool> IsFighting { get; } = new(nameof(IsFighting));

    /// <summary>
    ///     玩家数据
    /// </summary>
    public ActorThing Actor { get; set; }

    /// <summary>
    ///     关卡管理器
    /// </summary>
    public StageMgr StageMgr { get; set; }

    /// <summary>
    ///     当前回合
    /// </summary>
    public StageRound StageRound { get; set; }

    /// <summary>
    ///     地图管理器
    /// </summary>
    public MapMgr MapMgr { get; set; }

    /// <summary>
    ///     子弹池
    /// </summary>
    public BulletPool BulletPool { get; set; }

    /// <summary>
    ///     坐标系池
    /// </summary>
    public CoordinatePool CoordinatePool { get; set; }

    /// <summary>
    ///     怪物池
    /// </summary>
    public MonsterPool MonsterPool
    {
        get
        {
            if (MonsterPool_m)
            {
                return MonsterPool_m;
            }

            GameObject gameObj = new(nameof(MonsterPool));
            MonsterPool_m = gameObj.GetOrAddComponent<MonsterPool>();
            return MonsterPool_m;
        }
    }

    /// <summary>
    ///     所有存活的怪物数据
    /// </summary>
    public BubblingList<MonsterThing> Monsters { get; } = new();
    
    /// <summary>
    /// V64.0 - 批量死亡处理队列，避免大量怪物同时死亡造成卡顿
    /// </summary>
    private readonly Queue<MonsterThing> _batchDeathQueue = new();
    
    /// <summary>
    /// V64.0 - 批量死亡处理的取消令牌
    /// </summary>
    private CancellationTokenSource _batchDeathCTS = new();
    
    /// <summary>
    /// V64.0 - 是否正在进行批量死亡处理
    /// </summary>
    private bool _isBatchDeathProcessing = false;
    
    /// <summary>
    /// V64.0 - 每帧最大处理的死亡怪物数量
    /// </summary>
    private const int MAX_DEATH_PROCESS_PER_FRAME = 3;

    // /// <summary>
    // /// 战斗平面的格子容器
    // /// </summary>
    // public GameObject GridContainer { get; set; }

    /// <summary>
    ///     获取或设置已出生的玩家角色(界面)
    /// </summary>
    public PlayerActor PlayerActor { get; set; }

    public TimeManager TimeManager { get; private set; }

    public bool IsNeedOpenFightBag { get; set; }

    public bool UIOfferTimesOpend { get; set; }

    protected CancellationTokenSource CTS_WatchOfferTimes { get; set; } = new();
    /// <summary>
    /// 等待出生的怪物数量
    /// </summary>
    private int waitingBornMonsterNum { get; set; } = 0;
    
    /// <summary>
    /// 战斗开始时间
    /// </summary>
    private float battleStartTime { get; set; } = 0f;
    
    /// <summary>
    /// 最大战斗时间（秒）
    /// </summary>
    private int maxBattleTime { get; set; } = 300;
    
    /// <summary>
    /// 是否已经开始战斗倒计时
    /// </summary>
    private bool isBattleTimerStarted { get; set; } = false;
    
    /// <summary>
    /// 倒计时检测的取消令牌
    /// </summary>
    private CancellationTokenSource battleTimerCTS { get; set; } = new();

    public void Awake()
    {
        SingletonMgr.Instance.BattleMgr = this;

        DOTween.SetTweensCapacity(1000, 1000);
        TimeManager = gameObject.GetOrAddComponent<TimeManager>();

        StageMgr = gameObject.GetOrAddComponent<StageMgr>();
        StageRound = gameObject.GetOrAddComponent<StageRound>();

        MapMgr = gameObject.GetOrAddComponent<MapMgr>();
        MapMgr.MapsObj = transform.GetOrAddChildGameObject("Maps");

        // 子弹池根节点
        BulletPool = transform.GetOrAddChildGameObject(nameof(BulletPool)).GetOrAddComponent<BulletPool>();
        // 坐标系池根节点
        CoordinatePool = transform.GetOrAddChildGameObject(nameof(CoordinatePool)).GetOrAddComponent<CoordinatePool>();
    }

    public void Start()
    {
        // 监视怪物被移除
        Monsters.ObserveRemove().Subscribe(_ =>
        {
            // 怪被打完，且刷怪结束，则回合结束
            if (Monsters.Count == 0 && waitingBornMonsterNum == 0 && StageRound.MonsterSpawner is { IsBrushFinish: { Value: true } } &&
                StageRound.MonsterSpawner.MonsterQueue.Count == 0)
            {
                MessageBroker.Default.Publish(new RoundEnd { Passed = true });
            }
        }).AddTo(this);

        // 处理事件:出生玩家
        MessageBroker.Default.Receive<BornPlayer>().Subscribe(e =>
        {
            Task_BornPlayer(e).Forget();
        }).AddTo(this);

        // 处理事件:出生宠物
        MessageBroker.Default.Receive<BornPet>().Subscribe(_ =>
        {
            //	foreach (var goodsID in LuaToCshapeManager.Instance.PetFightEquipmentIDs)
            //	{
            //		if (goodsID <= 0) continue;

            //		var csvRow_Equip = EquipmentCsv.Instance.Dic[goodsID];

            //		// 按配置克隆宠物的预制件
            //		var csvRow_Creature = CreatureScheme.Instance.GetItem(csvRow_Equip.RightWeapon);
            //		string petModlePath = $"Assets/Temp/{csvRow_Creature.Prefab}.prefab";
            //		var petPrefab = await ResMgr.LoadResAsyncHandle<GameObject>(petModlePath).Task;
            //		var petObject = Instantiate(petPrefab, player.transform.parent);

            //		// 初始位置:距离和随机角度
            //		var distanceToMaster = 12f;
            //		var angle = RandomNum.RandomFloat(0, 360);
            //		var p = player.transform.position + distanceToMaster * Vector3.right;
            //		petObject.transform.position = p.RotateAround(player.transform.position, Vector3.forward, angle);
            //		petObject.transform.localScale = 2.25f * Vector3.one;
            //		//Debug.Log($"player位置:{player.transform.position}");
            //		//Debug.Log($"宠物初始位置:{petObject.transform.position}");

            //		var pet = petObject.AddComponent<Pet>();
            //		//pet.FightProp.InitPet(player);

            //		//// 给宠物添加技能
            //		//if (csvRow_Equip.ConsignmentStyle > 0)
            //		//{
            //		//	pet.SkillScheduler.AddSkill(csvRow_Equip.ConsignmentStyle);
            //		//}

            //		pet.CreatureThing.Creature = pet;
            //		pet.Master = player;
            //		player.CreatureThing.Pets.Add(pet);
            //		//pet.InitPropsEvents();
            //		pet.MoveCircle().Forget();
            //	}
        }).AddTo(this);

        // 处理事件:出生怪物
        MessageBroker.Default.Receive<BornMonster>().Subscribe(e =>
        {
            Task_BornMonster(e).Forget();
        }).AddTo(this);

        // 处理事件:出生子弹
        MessageBroker.Default.Receive<BornBullet>().Subscribe(e =>
        {
            Task_BornBullet(e).Forget();
        }).AddTo(this);

        // 处理事件:出生一个围击炸弹
        MessageBroker.Default.Receive<BornSiegeBomb>().Subscribe(e =>
        {
            Task_BornSiegeBomb(e).Forget();
        }).AddTo(this);

        // 处理事件:物件死亡
        MessageBroker.Default.Receive<ThingDead>().Subscribe(e =>
        {
            switch (e.Thing)
            {
                // 怪物死亡
                case MonsterThing monster:
                    {
                        KillCount.Value++;

                        // 玩家吸血
                        double suckPct = Actor.GetTotalDouble(PropType.KilledHpAbsorbPct).FirstOrDefault();
                        if (suckPct > 0)
                        {
                            suckPct = Math.Clamp(suckPct, 0, 1);
                            double maxHp = Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault();
                            // 按比例吸血
                            double suck = maxHp * suckPct;
                            double nActorHP = Actor.Hp.Value + suck;
                            nActorHP = Math.Clamp(nActorHP, Actor.Hp.Value, maxHp);
                            Actor.Hp.Value = nActorHP;
                        }

                        // 回合是否结束了
                        bool isRoundEnd = Monsters.Count <= 0 && waitingBornMonsterNum == 0 && StageRound.MonsterSpawner is
                        { IsBrushFinish: { Value: true } } && StageRound.MonsterSpawner.MonsterQueue.Count == 0;

                        // 没重生过的怪
                        if (monster.ReviveCount == 0 && !isRoundEnd)
                        {
                            // 且怪可重生
                            long maxRebirthTimes = monster.GetTotalLong(PropType.MaxRebirthTimes).FirstOrDefault();
                            if (maxRebirthTimes > 0)
                            {
                                StageRound.MonsterSpawner.BornMonster(monster.Position, 3,
                                    monster.CsvRow_BattleBrushEnemy.RebirthIds,
                                    monster.CsvRow_BattleBrushEnemy.RebirthCounts,
                                    monster.ReviveCount + 1);
                            }
                        }

                        

                        // 是否通关了
                        bool passed = isRoundEnd && !SingletonMgr.Instance.GlobalMgr
                            .ListBrushMonster_Stage(StageMgr.CsvRow_Stage.Value)
                            .ContainsKey(Actor.RoundNo.Value + 1);

                        // 还没通关,就给经验值
                        if (!passed)
                        {
                            // 玩家增加供选经验值
                            Actor.AddOfferExp(monster.CsvRow_BattleBrushEnemy.Skill);

                            List<int> propIds = Actor.GetTotalLong(PropType.KilledAwardPropIdList)
                                .ConvertAll(x => (int)x);
                            List<double> propPriorities = Actor.GetTotalDouble(PropType.KilledAwardPropIdPriorityList);
                            // 记录新加入的属性
                            List<CommonProp> lstPropsAdd = new();
                            Actor.CreateProps(propIds, propPriorities).ForEach(prop =>
                            {
                                lstPropsAdd.AddRange(Actor.AddBattleProp(prop));
                            });
                            // 分发新加入的属性
                            Actor.DispatchBattleProps(lstPropsAdd);
                        }

                        Monsters.Remove(monster);

                        // V64.0 - 使用批量死亡处理，避免大量怪物同时死亡造成卡顿
                        AddMonsterToBatchDeathQueue(monster);
                        if (isRoundEnd)
                        {
                            StageRound.MonsterSpawner?.StopBrush();
                            // V64.0 - 批量处理剩余怪物死亡
                            var remainingMonsters = new List<MonsterThing>(Monsters);
                            Monsters.Clear();
                            foreach (var monster1 in remainingMonsters)
                            {
                                AddMonsterToBatchDeathQueue(monster1);
                            }
                        }
                    }
                    break;
                // 玩家死亡
                case ActorThing actor:
                    {
                        // 停止战斗倒计时
                        StopBattleTimer();
                        
                        // 暂停游戏
                        LuaToCshapeManager.Instance.PauseOrResumeBattle(0);
                        
                        // 弹出复活界面
                        LuaManager.Instance.RunLuaFunction("DataService.OpenReviveDialog");
                    }
                    break;
            }
        }).AddTo(this);

        // 处理事件:关闭战斗背包
        MessageBroker.Default.Receive<CloseFightBag>().Subscribe(_ =>
        {
            // 角色已出生过，通过Npc开始回合
            if (PlayerActor)
            {
                PlayerActor.DenyMove = false;
                return;
            }

            // 角色未出生过，直接开始回合
            MessageBroker.Default.Publish(new RoundStart());
        }).AddTo(this);

        // 处理事件:回合结束
        MessageBroker.Default.Receive<RoundEnd>().Subscribe(e =>
        {
            Task_RoundEnd(e).Forget();
        }).AddTo(this);

        // 处理事件:回合开始
        MessageBroker.Default.Receive<RoundStart>().Subscribe(e =>
        {
            Task_RoundStart(e).Forget();
        }).AddTo(this);

        // 处理事件:物件受击
        MessageBroker.Default.Receive<HitThingCells>().Subscribe(e =>
        {
            bool hitRole = false;
            if (e.Thing.Camp == Camp.Player)
            {
                hitRole = true;
            }
            // 飘字的位置
            Vector3 hudStartPos = e.Thing.Position;
            if (e.Inc_Hp > 0)
            {
                //掉血飘字
                HudMgr.Instance.SpwanDamageHud(hudComp =>
                {
                    hudComp.Init(hudStartPos);
                    hudComp.SetDamageNumber((int)e.Inc_Hp, e.IsCritical,hitRole);
                }).Forget();
            }

            if (e.Inc_Armor > 0)
            {
                //掉护甲飘字
                HudMgr.Instance.SpwanDamageHud(hudComp =>
                {
                    hudComp.Init(hudStartPos);
                    hudComp.SetDamageNumber((int)e.Inc_Armor, e.IsCritical,hitRole);
                }).Forget();
            }

            if (e.Thing.Camp == Camp.Player && e.Inc_Hp > 0)
            {
                Actor.TotalTakenDamage_Round += e.Inc_Hp;

                UIFight ui = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
                if (ui != null)
                {
                    ui.PlayerHpBarDoFade();
                }
            }
        }).AddTo(this);

        // 处理事件:物件Cd计时
        MessageBroker.Default.Receive<ThingCd>().Where(e => e.Thing is GunThing
        {
            Actor: not null,
            CsvRow_Gun:
            {
                Value: { GunType: not ActionType.CoinBag }
            }
        }).Subscribe(e =>
        {
            GunThing gun = (e.Thing as GunThing)!;

            Debug.Log($"枪[{gun.CsvRow_Gun.Value.Id}]的CD时长: {e.CdMax} 秒");

            UIFight uiFight = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (uiFight != null)
            {
                uiFight.OnGunCDReset(gun.Guid, (float)gun.GetTotalDouble(PropType.Cd).FirstOrDefault());
            }
        }).AddTo(this);

        // 处理事件:播放枪的射击声音
        MessageBroker.Default.Receive<PlayShootSound>().Subscribe(e =>
        {
            AudioPlayer.Instance.PlaySound(e.Shooter.Thing.GetTotalString(PropType.ShootSound).FirstOrDefault())
                .Forget();
        }).AddTo(this);

        // 处理事件:经验值变化
        MessageBroker.Default.Receive<ShowOfferExp>().Subscribe(e =>
        {
            UIFight ui = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (ui)
            {
                ui.ExpChange(e.CurrentValue, e.MaxValue);
            }
        }).AddTo(this);

        // 处理事件:银币变化
        MessageBroker.Default.Receive<ShowCoins>().Subscribe(e =>
        {
            UIFight ui = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (ui)
            {
                ui.CoinChange(e.Num);
            }
        }).AddTo(this);

        // 处理事件:枪升级了(战斗中)
        MessageBroker.Default.Receive<GunLvlUp>().Subscribe(e =>
        {
            UIFight uiFight = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (uiFight != null)
            {
                uiFight.OnGunLevelUp(e.Gun.Guid, e.Gun.ThingLvl.Value,
                    e.Gun.CsvRow_Gun.Value.LvlIcons[e.Gun.ThingLvl.Value - 1]);
            }
        }).AddTo(this);

        EnterBattle().Forget();
    }

    public void OnDestroy()
    {
        // 停止战斗倒计时
        StopBattleTimer();
        
        StageRound.StopMonsterSpawner();
        StopBattle();

        DOTween.KillAll();

        SingletonMgr.Instance.BattleMgr = null;
    }

    /// <summary>
    ///     加载地图、出生角色后开始刷怪
    /// </summary>
    private async UniTaskVoid Task_BornPlayer(BornPlayer e)
    {
        // 角色初始位置
        Actor.Position = new Vector3(
            StageRound.Map.CsvRow_CatMainMap.Value.ActorPos[0],
            StageRound.Map.CsvRow_CatMainMap.Value.ActorPos[1],
            StageRound.Map.CsvRow_CatMainMap.Value.ActorPos.Skip(2).FirstOrDefault());

        if (PlayerActor)
        {
            Destroy(PlayerActor.gameObject);
        }

        string prefabPath =
            $"Assets/Temp/model/prefab/wxr/{e.ActorThing.GetTotalString(PropType.Model).FirstOrDefault()}.prefab";
        GameObject prefab = await ResMgrAsync.LoadResAsync<GameObject>(prefabPath);

        PlayerActor = Instantiate(prefab, transform).GetOrAddComponent<PlayerActor>();
        PlayerActor.transform.position = e.ActorThing.Position;
        PlayerActor.SyncToThing = true;
        PlayerActor.Thing = e.ActorThing;
        e.ActorThing.ThingBehaviour = PlayerActor;

        // 添加刚体和碰撞盒
        // V32.0 玩家角色添加Rigidbody2D组件，用于与MoveType=0怪物的物理碰撞
        Rigidbody2D playerRigidbody = PlayerActor.gameObject.GetOrAddComponent<Rigidbody2D>();
        playerRigidbody.bodyType = RigidbodyType2D.Dynamic; // Dynamic类型，支持物理碰撞
        playerRigidbody.gravityScale = 0; // 不受重力影响
        playerRigidbody.drag = 0; // 移动时不受阻力
        playerRigidbody.angularDrag = 5f; // 添加角阻力，防止旋转
        playerRigidbody.freezeRotation = true; // 冻结旋转
        playerRigidbody.mass = 100f; // V32.1 设置质量为100，无法推动MoveType=0怪物
        
        CircleCollider2D actorCollider = PlayerActor.gameObject.GetOrAddComponent<CircleCollider2D>();
        actorCollider.isTrigger = false; // V32.0 改为非触发器，产生真实的物理碰撞
        actorCollider.radius = e.ActorThing.TotalProp_Radius;
        
        // V32.0 创建玩家物理材质
        PhysicsMaterial2D playerPhysicsMaterial = new PhysicsMaterial2D("PlayerMaterial");
        playerPhysicsMaterial.friction = 0.5f; // 适度摩擦力
        playerPhysicsMaterial.bounciness = 0.1f; // 低弹性，避免反弹
        actorCollider.sharedMaterial = playerPhysicsMaterial;
        
        Debug.Log($"V32.1 玩家碰撞设置完成 - 位置:{e.ActorThing.Position} 半径:{e.ActorThing.TotalProp_Radius} " +
                  $"质量:{playerRigidbody.mass} 刚体类型:{playerRigidbody.bodyType} 碰撞盒触发器:{actorCollider.isTrigger}");
        
        // V32.0 设置玩家与怪物的碰撞层级规则
        // V60.1 修复Layer问题：使用项目中实际存在的Hero层级
        int heroLayer = LayerMask.NameToLayer("Hero");
        if (heroLayer == -1)
        {
            Debug.LogError("V60.1 Hero层级不存在，使用Default层级");
            PlayerActor.gameObject.layer = 0; // Default层级
        }
        else
        {
            PlayerActor.gameObject.layer = heroLayer;
            Debug.Log($"V60.1 玩家设置为Hero层级: {heroLayer}");
        }

        // 添加移动组件
        PlayerActor.PlayerMove = PlayerActor.gameObject.GetOrAddComponent<PlayerMove>();
        PlayerActor.DenyMove = false;

        CancellationTokenSource cts = CancellationTokenSource.CreateLinkedTokenSource(CTS_WatchOfferTimes.Token,
            this.GetCancellationTokenOnDestroy());
        WatchOfferTimes(cts.Token).Forget();

        // 重建刷怪器并启动
        StageRound.StartMonsterSpawner();
        //设置玩家枪cd显示
        UIMgr.Instance.GetUI<UIFight>(UIType.UIFight).SetUIInfoOnRoundStart();

        // 启动战斗倒计时
        StartBattleTimer();

        // 依次延时启动枪
        _ = Actor.Guns.Where(g => g.IsHidden).Select((x, i) => x.StartCdExecutor(i * 50)).ToList();
    }

    private async UniTaskVoid Task_BornMonster(BornMonster e)
    {
        waitingBornMonsterNum++;
        // 出生预示
        if (e.MonsterThing.ReviveCount == 0)
        {
            EffectMgr.Instance.ShowEffect(EffectPath.BornCircle, e.MonsterThing.Position, 2, null,
                e.MonsterThing.CsvRow_BattleBrushEnemy.AttackRate * 0.001f).Forget();
            await UniTask.Delay(e.MonsterThing.CsvRow_BattleBrushEnemy.AttackRate);
        }

        MonsterBase monster = await MonsterPool.GetOrCreateMonsterFromPool(e.CsvRow_Brush.EnemyName, e.MonsterThing);

        // V33.0 基于Mass字段设置怪物预制体缩放（只影响视觉大小，不影响碰撞盒）
        float massScale = (float)e.MonsterThing.GetTotalDouble(PropType.Mass).FirstOrDefault();
        if (massScale <= 0) massScale = 1f; // 默认值为1
        
        // 记录原始缩放值用于调试
        Vector3 originalScale = monster.transform.localScale;
        
        // 应用Mass缩放：预制体原来大小 * Mass配置值（只影响视觉）
        monster.transform.localScale = originalScale * massScale;
        
        // 加上刚体组件，用于物理碰撞
        Rigidbody2D monsterRigidbody = monster.gameObject.GetOrAddComponent<Rigidbody2D>();
        monsterRigidbody.bodyType = RigidbodyType2D.Dynamic; // 改为Dynamic类型，支持碰撞
        monsterRigidbody.gravityScale = 0; // 不受重力影响
        monsterRigidbody.drag = 5f; // 添加阻力，防止滑动
        monsterRigidbody.angularDrag = 5f; // 添加角阻力，防止旋转
        monsterRigidbody.freezeRotation = true; // 冻结旋转
        
        // V31.2 根据MoveType设置质量：MoveType=0设置超大质量，MoveType>0设置质量为1
        int moveType = e.MonsterThing.CsvRow_BattleBrushEnemy.MoveType;
        if (moveType == 0)
        {
            // MoveType=0的怪物设置超大质量，不会被推动
            monsterRigidbody.mass = 999999999f;
        }
        else
        {
            // MoveType>0的怪物设置质量为1，可以被推动
            monsterRigidbody.mass = 1f;
        }
        
        // V35.0 加上碰撞盒（抵消Mass缩放影响，保证实际碰撞大小等于Radius配置值）
        CircleCollider2D monsterCollider = monster.gameObject.GetOrAddComponent<CircleCollider2D>();
        // V35.0 计算抵消Mass缩放影响的半径值
        float originalRadius = (float)e.CsvRow_Brush.Radius;
        // 关键修复：碰撞盒半径 = 表格配置值 ÷ Mass缩放值，抵消预制体缩放的影响
        float compensatedRadius = originalRadius / massScale;
        monsterCollider.radius = compensatedRadius;
        // 设置为非触发器，产生真实的物理碰撞
        monsterCollider.isTrigger = false;
        
        // 99999999 同一只怪物的完整信息统一输出
        //Debug.Log($"99999999 怪物完整信息 - ID:{e.CsvRow_Brush.Id} 名称:{e.CsvRow_Brush.EnemyName} " +
                  //$"Mass配置值:{massScale} 预制体缩放:{monster.transform.localScale} " +
                  //$"Radius配置值:{originalRadius} 碰撞盒设置半径:{compensatedRadius} " +
                  //$"实际碰撞大小:{compensatedRadius * massScale} MoveType:{moveType}");
        
        // 创建物理材质，防止怪物相互穿透
        PhysicsMaterial2D monsterPhysicsMaterial = new PhysicsMaterial2D("MonsterMaterial");
        monsterPhysicsMaterial.friction = 0.8f; // 高摩擦力，防止滑动
        monsterPhysicsMaterial.bounciness = 0.3f; // 适度弹性，让碰撞更明显
        monsterCollider.sharedMaterial = monsterPhysicsMaterial;
        
        // V32.0 设置碰撞层级：所有怪物都使用Monster层级
        // V60.1 修复Layer问题：使用项目中实际存在的Monster层级
        int monsterLayer = LayerMask.NameToLayer("Monster");
        if (monsterLayer == -1)
        {
            Debug.LogError("V60.1 Monster层级不存在，使用Default层级");
            monster.gameObject.layer = 0; // Default层级
        }
        else
        {
            monster.gameObject.layer = monsterLayer;
            Debug.Log($"V60.1 怪物设置为Monster层级: {monsterLayer} MoveType:{moveType}");
        }
        
        // V60.1 如果需要控制不同MoveType的碰撞，通过物理矩阵设置
        // 这里先保留所有怪物都在Monster层级，后续可以通过Physics2D.IgnoreLayerCollision控制
        
        // 调试日志：怪物碰撞组件设置
        //Debug.Log($"333333 怪物碰撞设置完成 - ID:{e.MonsterThing.CsvRow_BattleBrushEnemy.Id} 位置:{e.MonsterThing.Position} 半径:{e.MonsterThing.TotalProp_Radius} 刚体类型:{monsterRigidbody.bodyType} 碰撞盒触发器:{monsterCollider.isTrigger} 层级:{monster.gameObject.layer} 阻力:{monsterRigidbody.drag} 摩擦力:{monsterPhysicsMaterial.friction} 弹性:{monsterPhysicsMaterial.bounciness}");
        //添加寻路组件
        monster.MonsterMoveAI = monster.gameObject.GetOrAddComponent<MonsterMoveAI>();
        monster.MonsterMoveAI.NavAgent = monster.gameObject.GetOrAddComponent<NavAgent>();
        monster.MonsterMoveAI.NavAgent.NavData = StageRound.Map.NavData;

        // 怪物初始位置
        monster.transform.position = e.MonsterThing.Position;
        monster.SyncToThing = true;

        //Debug.Log($"新生怪物的速度:{e.MonsterThing.GetTotalDouble(PropType.Speed).FirstOrDefault()}");

        // 显示怪物
        monster.gameObject.SetActive(true);
        waitingBornMonsterNum--;
        // 新出生的怪物加到怪物列表
        Monsters.Add(e.MonsterThing);

        // 下次可以撞击敌人的时间
        e.MonsterThing.NextImpactEnemyTime.Value = Time.time;

        // 启动枪(依次延时)
        _ = e.MonsterThing.Guns.Where(g => g.IsHidden).Select((x, i) => x.StartCdExecutor(i * 50)).ToList();

        // 开始移动
        //monster.StartMove();
    }

    private async UniTaskVoid Task_BornBullet(BornBullet e)
    {
        // // 子弹类型
        // var bulletType = (BulletType)e.Bullet.GetTotalLong(PropType.BulletType).FirstOrDefault();
        
        // V58.0 修改Img字段解析：支持"图片名称;预制体名称"格式
        string imgFieldValue = e.Bullet.GetTotalString(PropType.Img).FirstOrDefault();
        string bulletImg = "";
        string bulletPrefab = "";
        
        if (!string.IsNullOrWhiteSpace(imgFieldValue))
        {
            string[] imgParts = imgFieldValue.Split(';');
            // 参数1：子弹飞行图片名称
            bulletImg = imgParts.Length > 0 ? imgParts[0].Trim() : "";
            // 参数2：子弹飞行特效预制体
            bulletPrefab = imgParts.Length > 1 ? imgParts[1].Trim() : "";
            
            // 处理参数1为"0"的情况（不显示图片）
            if (bulletImg == "0")
                bulletImg = "";
            
            // 处理参数2为"0"或空的情况（不显示预制体）
            if (bulletPrefab == "0" || string.IsNullOrWhiteSpace(bulletPrefab))
                bulletPrefab = "";
                
            // Debug.Log($"V58.0 子弹Img字段解析: 原始值='{imgFieldValue}', 图片='{bulletImg}', 预制体='{bulletPrefab}'");
        }
        
        // 子弹缩放
        float bulletScale = (float)e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.BulletImgScale)
            .FirstOrDefault();
        bulletScale = bulletScale <= float.Epsilon ? 1 : bulletScale;

        e.Bullet.Provider_BeforeInitView = view =>
        {
            if (view is MonsterMissileTrackPos monsterMissileTrackPos)
            {
                // V5.11彻底解决方案：直接从怪物预制体获取位置，确保强关联
                Vector3 shooterPosition;
                if (e.Bullet.CdExecutor.Thing is MonsterThing monsterThing && monsterThing.Monster != null)
                {
                    shooterPosition = monsterThing.Monster.transform.position;
                    //Debug.Log($"44444444 怪物抛物线子弹从预制体获取位置 - 预制体位置:{shooterPosition}");
                }
                else
                {
                    shooterPosition = e.Bullet.Position;
                    //Debug.Log($"44444444 怪物抛物线子弹备用位置 - 子弹位置:{shooterPosition}");
                }
                
                Vector3 targetPosition = e.Bullet.TrackPosition!.Value;
                
                // 调试日志：发射点、目标点
                //Debug.Log($"44444444 怪物抛物线子弹 - 发射点:{shooterPosition} 目标点:{targetPosition}");
                
                // 创建导弹发射器，使用正确的发射点
                GameObject missileEjector =
                    SingletonMgr.Instance.BattleMgr.CoordinatePool.GetOrCreateCoordinate("MissileEjector");
                missileEjector.transform.position = shooterPosition;
                missileEjector.transform.right = targetPosition - shooterPosition;
                monsterMissileTrackPos.MissileLocusGenerator = new MissileLocusGenerator
                {
                    MissileEjector = missileEjector, TargetPosition = e.Bullet.TrackPosition
                };
                monsterMissileTrackPos.Locus =
                    monsterMissileTrackPos.MissileLocusGenerator.CalcKeyPositions(shooterPosition);
            }
            else if (view is ActorMissileTrackPos actorMissileTrackPos)
            {
                // V5.11彻底解决方案：直接从玩家预制体获取位置，确保强关联
                Vector3 shooterPosition;
                if (SingletonMgr.Instance.BattleMgr.PlayerActor != null)
                {
                    shooterPosition = SingletonMgr.Instance.BattleMgr.PlayerActor.transform.position;
                    //Debug.Log($"44444444 枪械抛物线子弹从预制体获取位置 - 预制体位置:{shooterPosition}");
                }
                else
                {
                    shooterPosition = e.Bullet.Position;
                    //Debug.Log($"44444444 枪械抛物线子弹备用位置 - 子弹位置:{shooterPosition}");
                }
                
                Vector3 targetPosition = e.Bullet.TrackPosition!.Value;
                
                // 调试日志：发射点、目标点
                //Debug.Log($"44444444 枪械抛物线子弹 - 发射点:{shooterPosition} 目标点:{targetPosition}");
                
                // 创建导弹发射器，使用正确的发射点
                GameObject missileEjector =
                    SingletonMgr.Instance.BattleMgr.CoordinatePool.GetOrCreateCoordinate("MissileEjector");
                missileEjector.transform.position = shooterPosition;
                missileEjector.transform.right = targetPosition - shooterPosition;
                actorMissileTrackPos.MissileLocusGenerator = new MissileLocusGenerator
                {
                    MissileEjector = missileEjector, TargetPosition = e.Bullet.TrackPosition
                };
                actorMissileTrackPos.Locus =
                    actorMissileTrackPos.MissileLocusGenerator.CalcKeyPositions(shooterPosition);
            }
            else if (view is ActorMissileTrackEnemy actorMissileTrackEnemy)
            {
                // V60.4 - 设置导弹追击敌人的目标
                actorMissileTrackEnemy.TargetEnemy = e.Bullet.AttackBaseDirFollowThing;
                //Debug.Log($"777777777 ActorMissileTrackEnemy 初始化 - 目标敌人:{actorMissileTrackEnemy.TargetEnemy?.GetType().Name} 位置:{actorMissileTrackEnemy.TargetEnemy?.Position}");
            }
        };

        Type bulletCls = e.Bullet.GetBulletViewClass();
        BulletBase bullet = await BulletPool.GetOrCreateFromPool(bulletCls, e.Bullet);

        if (!string.IsNullOrWhiteSpace(bulletImg))
        {
            string sortingLayerName = bullet switch
            {
                ActorMissileTrackEnemy => "PlayerBullet", // V60.6 移到前面避免被ActorLocusBullet覆盖
                ActorMissileTrackPos => "PlayerBullet",
                ActorLocusBullet => "PlayerBullet",
                _ => "EnemyBullet"
            };
            bullet.SpriteRenderer.SetSprite(bulletImg, sortingLayerName, bulletScale, 0,
                    bullet.GetCancellationTokenOnDestroy())
                .Forget();
        }

        // V58.0 处理子弹飞行特效预制体
        if (!string.IsNullOrWhiteSpace(bulletPrefab))
        {
            await LoadBulletPrefabAsync(bullet, bulletPrefab, bulletScale);
        }

        // 子弹朝向运动方向
        Vector3 dir_1 = (bullet.BulletThing.Position - e.PositionPre).normalized;
        bullet.transform.right = dir_1;

        // 重要：先确保transform.position是正确的，再启用双向同步
        // 这样避免对象池复用时的脏数据被同步回bullet.Position
        bullet.transform.position = bullet.BulletThing.Position;
        
        // 等一帧确保位置设置完成，再启用双向同步
        await UniTask.NextFrame();
        bullet.SyncToThing = true;

        // 子弹生命开始时间、下次可以击中敌人的时间
        e.Bullet.LifeBeginTime.Value = e.Bullet.NextHitEnemyTime.Value = Time.time;

        Debug.Log(
            $"子弹出生时间:{Time.time} 剩余反弹次数:{e.Bullet.BounceTimes.Value} 最大生命时长:{e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod).FirstOrDefault()}");

        // // 等一帧再移动
        // await UniTask.NextFrame();

        if (bullet is ActorLocusBullet actorLocusBullet)
        {
            // 生成子弹轨迹后显示并开始自转和移动
            actorLocusBullet.GenLocusThenStartMove().Forget();
        }
        else if (bullet is MonsterLocusBullet monsterLocusBullet)
        {
            // 生成子弹轨迹后显示并开始自转和移动
            monsterLocusBullet.GenLocusThenStartMove().Forget();
        }
        else
        {
            // 子弹开始移动
            bullet.StartMove();
        }
    }

    private async UniTaskVoid Task_BornSiegeBomb(BornSiegeBomb e)
    {
        // 找到子弹配置
        BulletCfg.Types.CSVRow csvRow_Bullet = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Pb
            .CSVTable.First(x =>
                x.BulletId == e.Bullet.CdExecutor.Thing.GetTotalLong(PropType.BulletId).FirstOrDefault() &&
                x.BulletLvl == e.Bullet.CdExecutor.Thing.ThingLvl.Value);

        // V58.0 修改Img字段解析：支持"图片名称;预制体名称"格式
        string imgFieldValue = csvRow_Bullet.Img;
        string bulletImg = "";
        string bulletPrefab = "";
        
        if (!string.IsNullOrWhiteSpace(imgFieldValue))
        {
            string[] imgParts = imgFieldValue.Split(';');
            // 参数1：子弹飞行图片名称
            bulletImg = imgParts.Length > 0 ? imgParts[0].Trim() : "";
            // 参数2：子弹飞行特效预制体
            bulletPrefab = imgParts.Length > 1 ? imgParts[1].Trim() : "";
            
            // 处理参数1为"0"的情况（不显示图片）
            if (bulletImg == "0")
                bulletImg = "";
            
            // 处理参数2为"0"或空的情况（不显示预制体）
            if (bulletPrefab == "0" || string.IsNullOrWhiteSpace(bulletPrefab))
                bulletPrefab = "";
                
            // Debug.Log($"V58.0 SiegeBomb子弹Img字段解析: 原始值='{imgFieldValue}', 图片='{bulletImg}', 预制体='{bulletPrefab}'");
        }
        
        // 子弹缩放
        float bulletScale = (float)e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.BulletImgScale)
            .FirstOrDefault();
        bulletScale = bulletScale <= float.Epsilon ? 1 : bulletScale;

        e.Bullet.Provider_BeforeInitView = view =>
        {
            if (view is MonsterSiegeBomb monsterSiegeBomb)
            {
                monsterSiegeBomb.TargetPosition = e.TargetPosition;
            }
        };

        Type bulletCls = e.Bullet.GetBulletViewClass();
        BulletBase bullet = await BulletPool.GetOrCreateFromPool(bulletCls, e.Bullet);

        if (!string.IsNullOrWhiteSpace(bulletImg))
        {
            string sortingLayerName = bullet switch
            {
                ActorLocusBullet => "PlayerBullet",
                ActorMissileTrackPos => "PlayerBullet",
                _ => "EnemyBullet"
            };
            bullet.SpriteRenderer.SetSprite(bulletImg, sortingLayerName, bulletScale, 0,
                    bullet.GetCancellationTokenOnDestroy())
                .Forget();
        }

        // V58.0 处理子弹飞行特效预制体
        if (!string.IsNullOrWhiteSpace(bulletPrefab))
        {
            await LoadBulletPrefabAsync(bullet, bulletPrefab, bulletScale);
        }

        // 子弹初始位置
        bullet.transform.position = bullet.BulletThing.Position;
        bullet.SyncToThing = true;

        // 重要：先确保transform.position是正确的，再启用双向同步
        // 这样避免对象池复用时的脏数据被同步回bullet.Position
        bullet.transform.position = bullet.BulletThing.Position;
        
        // 等一帧确保位置设置完成，再启用双向同步
        await UniTask.NextFrame();
        bullet.SyncToThing = true;

        // // 子弹生命开始时间、下次可以击中敌人的时间
        // e.Bullet.LifeBeginTime.Value = e.Bullet.NextHitEnemyTime.Value = Time.time;

        // Debug.Log(
        //     $"子弹出生时间:{Time.time} 剩余反弹次数:{e.Bullet.BounceTimes.Value} 最大生命时长:{e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod).FirstOrDefault()}");

        // 显示子弹
        bullet.gameObject.SetActive(true);

        // 等一帧再移动
        await UniTask.NextFrame();

        // // 子弹开始自转
        // bullet.StartRotate();
        // 子弹开始移动
        bullet.StartMove();
    }

    public async UniTaskVoid Task_RoundStart(RoundStart e)
    {
        // 设置回合
        StageRound.RoundNo.Value = Actor.RoundNo.Value;
        // 切换地图
        UniTask task_SwitchMap = StageRound.SwitchMap();

        // 从本地数据中读取枪并重建枪和角色
        string jsonGunsInBag =
            SingletonMgr.Instance.ActorDataMgr.GetStringByIndex((int)ActorDataCatalog.BattleBagGuns, 1);
        if (string.IsNullOrEmpty(jsonGunsInBag))
        {
            jsonGunsInBag = "[]";
        }

        List<GunItem> gunsInBag = JsonConvert.DeserializeObject<List<GunItem>>(jsonGunsInBag) ?? new List<GunItem>();
        Actor.ReCreateGuns(gunsInBag);
        Actor.ReCalcHoistAndTotal_All();

        Actor.TotalTakenDamage_Round = 0;
        IsFighting.Value = true;
        KillCount.Value = 0;
        // 护甲每回合都从最大值开始
        double maxArmorPct = Actor.GetTotalDouble(PropType.MaxArmorPct).FirstOrDefault();
        double maxArmor = Actor.TotalProp_MaxHp * maxArmorPct;
        Actor.Armor.Value = maxArmor;

        {
            // 比较当前最大血量与进背包时的最大血量，当前血量随之增加(不随之减少)
            double maxHp = Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault();
            double maxHp_Store =
                SingletonMgr.Instance.ActorDataMgr.GetDoubleByIndex((int)ActorDataCatalog.BattleHp, 1);
            double currentHp = Actor.Hp.Value;
            double inc = maxHp - maxHp_Store;
            if (inc > 0)
            {
                currentHp += inc;
            }

            // 当前血量不能超过最大血量
            if (currentHp > maxHp)
            {
                currentHp = maxHp;
            }

            Actor.Hp.Value = currentHp;
        }

        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(400));
        // Globals.resetControls = false;
        //Globals.focusOnPlayer = false;
        TimeManager.ResetTimescale();

        // 等到切换地图完成
        await task_SwitchMap;
        // 出生角色并开始刷怪
        MessageBroker.Default.Publish(new BornPlayer { ActorThing = Actor });
    }

    public async UniTaskVoid Task_RoundEnd(RoundEnd e)
    {
        // Debug.Log($"回合结束:{DateTime.Now:O}");
        await UniTask.SwitchToMainThread();

        // 注释掉停止战斗倒计时，因为倒计时应该持续整个副本过程
        // StopBattleTimer();

        IsFighting.Value = false;

        // 停止枪
        _ = Actor.Guns.Select(g =>
        {
            g.StopCdExecutor();
            return g;
        }).ToList();

        if (e.Passed)
        {
            // 给这回合的通过奖励
            Actor.Coins.Value += StageRound.RoundCfg.Coins +
                                 Actor.GetTotalLong(PropType.RoundCoin).FirstOrDefault();
            EffectMgr.Instance.ShowEffect(EffectPath.ui_GainCoin, transform.position, 1, UIMgr.Instance.UIRoot, 2)
                .Forget();
        }

        // Debug.Log($"已发放金币:{DateTime.Now:O}");

        // 保存该回合的统计
        Dictionary<string, string> ps = new()
        {
            ["ApiVersion"] = "v1",
            ["ActorId"] = Actor.ActorId.ToString(),
            ["StageType"] = Actor.StageType.ToString(),
            //["StageLvl"] = BattleProgress.ActorBattleProgress.StageLvl.ToString(),
            //["GotCoins"] = RoundStat.Instance.GotCoins.Value.ToString(),
            ["KillCount"] = Score.Value.ToString()
        };
        UnityHttpHelper.GetResponseString(
            LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/AddActorBattleStat_Day",
            HttpMethod.Post, ps).Forget();

        Dictionary<int, List<BattleBrushEnemy.Item>> lst =
            SingletonMgr.Instance.GlobalMgr.ListBrushMonster_Stage(StageMgr.CsvRow_Stage.Value);
        if (lst.ContainsKey(Actor.RoundNo.Value + 1))
        {
            // 无伤奖励属性
            if (Actor.TotalTakenDamage_Round <= double.Epsilon)
            {
                List<long> propIds = Actor.GetTotalLong(PropType.NotInjuriedPropIdList);
                // 记录新加入的属性
                List<CommonProp> lstPropsAdd = new();
                propIds.ForEach(id =>
                {
                    CommonProp prop = new CommonProp().SetRowData(SingletonMgr.Instance.CsvLoaderMgr
                        .GetOrAddLoader<CommonPropCsv>()
                        .Dic[(int)id]);
                    lstPropsAdd.AddRange(Actor.AddBattleProp(prop));
                });
                // 分发新加入的属性
                Actor.DispatchBattleProps(lstPropsAdd);
            }

            // 进入下一回合  +++++++++++++++++++++++++刷枪后再保存进度
            Actor.RoundNo.Value++;
            Actor.SaveProgress();

            // 保存当前血量和最大血量
            List<double> lstHp = new() { Actor.Hp.Value, Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault() };
            SingletonMgr.Instance.ActorDataMgr.SetDouble((int)ActorDataCatalog.BattleHp, lstHp.ToArray());

            if (UIMgr.Instance.GetGuideStep() == 8)
            {
                UIMgr.Instance.OpenUI(UIType.UIFightGuide, 110, (ui) => { ui.GetComponent<UIFightGuide>().Init(); });
            }
            // 创建Npc
            await StageRound.Map.NpcMgr.CreateNpc(Timing.AfterRoundSuccess);
            StageRound.Map.NpcMgr.NpcList.ForEach(t =>
            {
                t.Provider_ActorEnterTrigger = () =>
                {
                    if (t.NpcThing.CsvRow_Npc.Value.TriggerActions.Contains(TriggerAction.ShowUi))
                    {
                        if (t.NpcThing.CsvRow_Npc.Value.ShowUiId == 0)
                        {
                            StageRound.Map.NpcMgr.NpcList.Remove(t);

                            //if (UIMgr.Instance.GetGuideStep() == 8)
                            //{
                            //    UIMgr.Instance.OpenUI(UIType.FightBag, 110, ui =>
                            //    {
                            //        ui.GetComponent<UIFightGuide>().Init();
                            //    });
                            //}
                            //else
                            {
                                // UIFight uiFight = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
                                //uiFight.BottomRoot.DOLocalMoveY(uiFight.BottomRoot.localPosition.y - 520, 0.5f).onComplete = () =>
                                {
                                    // 打开背包前,保存最大血量
                                    PlayerActor.DenyMove = true;
                                    SingletonMgr.Instance.ActorDataMgr.SetDoubleByIndex((int)ActorDataCatalog.BattleHp,
                                        1, Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                                    // 打开背包界面
                                    UIMgr.Instance.OpenUI(UIType.FightBag, default, uiObj =>
                                    {
                                        UIFightBag uiFightBag = uiObj.GetComponent<UIFightBag>();
                                        if (uiFightBag)
                                        {
                                            uiFightBag.Init();
                                            uiFightBag.ResetProgress(true).Forget();
                                        }
                                    });
                                }
                            }
                        }
                    }

                    if (t.NpcThing.CsvRow_Npc.Value.TriggerActions.Contains(TriggerAction.NextRound))
                    {
                        // 销毁所有NPC
                        StageRound.Map.NpcMgr.NpcList.Where(p => p && p != t).ToList().ForEach(p =>
                        {
                            Destroy(p.gameObject);
                        });
                        StageRound.Map.NpcMgr.NpcList.Clear();

                        // 开始下一回合
                        MessageBroker.Default.Publish(new RoundStart());
                    }

                    if (t.NpcThing.CsvRow_Npc.Value.TriggerActions.Contains(TriggerAction.DestroyNpc))
                    {
                        Destroy(t.gameObject, 0.1f);
                    }
                };
            });
        }
        else
        {
            // 没有下一回合，通关了

            // 清除战斗进度
            LuaToCshapeManager.Instance.ClearProgress();
            //CTS_WatchOfferTimes.Cancel();
            //CTS_WatchOfferTimes = new();
            //int addFactor = (int)Actor.GetHoistedDouble(PropType.StagePrizePass).FirstOrDefault();
            //int battleBrushEnemyId = dic[BattleProgress.ActorBattleProgress.RoundNo].BattleBrushEnemyId;
            //LuaManager.Instance.LuaState_.Call("DataService.OpenSettleAccounts", Globals.CsvRow_CatMainStage.Id, true, battleBrushEnemyId, addFactor, true);
            SettleAccounts(true);
        }
    }

    /// <summary>
    ///     副本结算
    /// </summary>
    /// <param name="isPassed">是否通关</param>
    public void SettleAccounts(bool isPassed)
    {
        //Debug.Log($"2222222 副本结算: isPassed={isPassed}");
        
        // 停止战斗倒计时
        StopBattleTimer();
        
        // 停止刷怪
        StageRound.MonsterSpawner?.StopBrush();
        StageRound.MonsterSpawner?.MonsterQueue.Clear();
        
        // 所有怪物停止枪并清空
        foreach (var monster in Monsters)
        {
            monster.StopCdExecutor();
            monster.Guns.ToList().ForEach(g=>g.StopCdExecutor());
        }
        Monsters.Clear();
        // 角色停止枪
        Actor.Guns.ToList().ForEach(g=>g.StopCdExecutor());
        
        // 调用胜利/失败界面
        if (isPassed)
        {
            //Debug.Log($"2222222 战斗胜利，调用胜利界面");
            // 清除战斗进度
            LuaToCshapeManager.Instance.ClearProgress();
            
            // 调用Lua胜利界面
            try
            {
                // 获取当前回合信息用于结算
                var dic = StageMgr.Dic_Stage;
                int battleBrushEnemyId = 0;
                int addFactor = 0;
                
                if (dic != null && dic.ContainsKey(Actor.RoundNo.Value))
                {
                    battleBrushEnemyId = dic[Actor.RoundNo.Value].BattleBrushEnemyId;
                }
                
                // 调用Lua胜利结算界面
                LuaManager.Instance.LuaState_.Call("DataService.OpenSettleAccounts", 
                    SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id, true, battleBrushEnemyId, addFactor, true);
                //Debug.Log($"2222222 成功调用Lua胜利界面");
            }
            catch (Exception ex)
            {
                //Debug.LogError($"2222222 调用Lua胜利界面失败: {ex.Message}");
            }
        }
        else
        {
            //Debug.Log($"2222222 战斗失败，调用失败界面");
            // 调用失败界面的逻辑
            try
            {
                LuaManager.Instance.LuaState_.Call("DataService.OpenSettleAccounts", 
                    SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id, false, 0, 0, false);
                //Debug.Log($"2222222 成功调用Lua失败界面");
            }
            catch (Exception ex)
            {
                //Debug.LogError($"2222222 调用Lua失败界面失败: {ex.Message}");
            }
        }
    }

    public void BattleQuit()
    {
        // 停止战斗倒计时
        StopBattleTimer();
        
        // 停止刷怪
        StageRound.MonsterSpawner?.StopBrush();
        StageRound.MonsterSpawner?.MonsterQueue.Clear();

        // 所有怪物停止枪并清空
        foreach (var monster in Monsters)
        {
            monster.StopCdExecutor();
            monster.Guns.ToList().ForEach(g => g.StopCdExecutor());
        }
        Monsters.Clear();
        // 角色停止枪
        Actor.Guns.ToList().ForEach(g => g.StopCdExecutor());
    }

    /// <summary>
    ///     战斗入口(读取角色属性、武器列表、进度恢复、关卡配置、加载地图、打开背包界面)
    /// </summary>
    public async UniTaskVoid EnterBattle()
    {
        // 重置战斗倒计时状态
        StopBattleTimer();
        
        // 初始化角色数据
        Actor = new ActorThing
        {
            ActorId = LuaDataSrvClient.Instance.GetActorID(), ActorProp = LuaDataSrvClient.Instance.GetPlayerProp()
        };
        // 角色出战的武器
        Actor.InitWeapons(LuaDataSrvClient.Instance.GetWeapons(true));

        #region 进度恢复

        // 等到通用属性表加载完成
        await UniTask.WaitUntil(() => SingletonMgr.Instance.GlobalMgr.CommonPropCfgLoaded.Value);

        Actor.ReadProgress();

        #endregion

        // 设置关卡
        StageMgr.SetStage(Actor.StageLvl, Actor.StageType);
        // 提前设置回合(切换地图)
        StageRound.RoundNo.Value = Actor.RoundNo.Value;
        StageRound.SwitchMap().Forget();

        UIMgr.Instance.OpenUI(UIType.UIFight, default, uiObj =>
        {
            UIFight uiFight = uiObj.GetComponent<UIFight>();
            if (uiFight)
            {
                uiFight.Init();
                uiFight.SetUIInfo();
            }

            MessageBroker.Default.Publish(new CloseFightBag());
            // 打开背包前,保存最大血量
            //SingletonMgr.Instance.ActorDataMgr.SetDoubleByIndex((int)ActorDataCatalog.BattleHp, 1,
            //    Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
            ////打开背包界面
            //UIMgr.Instance.OpenUI(UIType.FightBag, default, uiBag =>
            //{
            //    var uiFightBag = uiBag.GetComponent<UIFightBag>();
            //    if (uiFightBag)
            //    {
            //        uiFightBag.Init();
            //        uiFightBag.ResetProgress(Actor.RefreshTimes == 0).Forget();
            //    }
            //});
        });
    }

    public async UniTaskVoid WatchOfferTimes(CancellationToken token)
    {
        for (;; await UniTask.NextFrame())
        {
            if (token.IsCancellationRequested)
            {
                break;
            }

            if (Time.deltaTime <= 0)
            {
                continue;
            }

            if (Actor.OfferTimes.Value > 0 && !UIOfferTimesOpend)
            {
                UIOfferTimesOpend = true;
                UIUpgradeProperty ui = UIMgr.Instance.GetUI<UIUpgradeProperty>(UIType.UIUpgradeProperty);
                if (ui != null && ui.gameObject.activeSelf)
                {
                    return;
                }

                //Actor.OfferTimes.Value--;
                UIMgr.Instance.OpenUI(UIType.UIUpgradeProperty, 101);
            }
        }
    }

    /// <summary>
    ///     在距离区间内查找怪物
    /// </summary>
    /// <param name="pos">圆心</param>
    /// <param name="radius">半径</param>
    /// <param name="howFind">查找方法</param>
    /// <param name="maxDistance">最大距离</param>
    /// <param name="maxCount">最多找多少个</param>
    /// <param name="minDistance">最小距离</param>
    /// <param name="predicate">怪物范围条件</param>
    public List<DistanceThing> FindMonster(Vector3 pos, float radius, FindActionTarget howFind,
        float maxDistance, int maxCount = 1, float minDistance = 0,
        Func<MonsterThing, bool> predicate = null)
    {
        List<DistanceThing> lstDistanceMonster = Monsters.Where(x => predicate?.Invoke(x) ?? true)
            .Select(x => new DistanceThing
            {
                Thing2 = x,
                Distance = pos.CalcDistance2D_SolidCircleToSolidCircle(radius, x.Position, x.TotalProp_Radius)
            })
            .Where(x => minDistance <= x.Distance && x.Distance <= maxDistance).ToList();

        return howFind switch
        {
            FindActionTarget.NearestEnemy => lstDistanceMonster
                .OrderBy(x => x.Distance)
                .Take(maxCount)
                .ToList(),
            FindActionTarget.RandomEnemy => lstDistanceMonster
                .OrderBy(_ => RandomNum.RandomInt())
                .Take(maxCount)
                .ToList(),
            _ => null
        };
    }

    /// <summary>
    ///     结束战斗，退出战斗场景
    /// </summary>
    public void StopBattle()
    {
        // 玩家的枪停止射击
        Actor.Guns.ToList().ForEach(g => g.CTS_CdExecutor.Cancel());
        Monsters.ToList().ForEach(_ => _.CTS_CdExecutor.Cancel());
    }

    #region 回合统计

    /// <summary>
    ///     回合内增加的金币数
    /// </summary>
    public IntReactiveProperty GotCoins { get; } = new();

    /// <summary>
    ///     回合内增加的杀怪数
    /// </summary>
    public IntReactiveProperty KillCount { get; } = new();

    /// <summary>
    ///     回合内增加的积分数
    /// </summary>
    public IntReactiveProperty Score { get; } = new();

    #endregion

    /// <summary>
    /// 开始战斗倒计时
    /// </summary>
    public void StartBattleTimer()
    {
        if (isBattleTimerStarted) 
        {
            // Debug.Log($"2222222 战斗倒计时已经开始，跳过重复启动");
            return;
        }
        
        // 强制从CatMainStage配置中获取最大战斗时间，不允许其他地方覆盖
        // Debug.Log($"2222222 Actor信息: StageLvl={Actor?.StageLvl}, StageType={Actor?.StageType}");
        
        if (SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage != null)
        {
            try
            {
                var configTime = SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Maxtime;
                // Debug.Log($"2222222 强制从配置读取最大战斗时间: {configTime}秒, 关卡ID={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id}");
                // Debug.Log($"2222222 配置对象详细信息: FrontType={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.FrontType}, Id={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id}, Name={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Name}");
                
                // 检查是否是正确的关卡配置
                if (Actor != null)
                {
                    // Debug.Log($"2222222 期望的关卡: StageLvl={Actor.StageLvl}, StageType={Actor.StageType}");
                    // Debug.Log($"2222222 实际加载的关卡: Id={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id}, FrontType={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.FrontType}");
                    
                    if (SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id != Actor.StageLvl)
                    {
                        // Debug.LogWarning($"2222222 关卡ID不匹配！期望={Actor.StageLvl}, 实际={SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Id}");
                    }
                }
                
                // 强制设置为配置值，不允许任何覆盖
                maxBattleTime = configTime;
                
                // 如果配置值无效，强制设置为300
                if (maxBattleTime <= 0)
                {
                    // Debug.LogWarning($"2222222 配置的战斗时间无效: {maxBattleTime}，强制设置为300秒");
                    maxBattleTime = 300;
                }
                
                // Debug.Log($"2222222 最终确定的战斗时间: {maxBattleTime}秒");
            }
            catch (Exception ex)
            {
                // Debug.LogError($"2222222 访问Maxtime字段失败: {ex.Message}, 强制设置为300秒");
                maxBattleTime = 300;
            }
        }
        else
        {
            // Debug.LogWarning("2222222 CsvRow_CatMainStage为空，强制设置为300秒");
            maxBattleTime = 300;
        }
        
        battleStartTime = Time.time;
        isBattleTimerStarted = true;
        
        // Debug.Log($"2222222 开始战斗倒计时: 最大时间={maxBattleTime}秒, 开始时间={battleStartTime}, 当前时间={Time.time}");
        
        // 开始倒计时检测
        Task_BattleTimer(battleTimerCTS.Token).Forget();
    }
    
    /// <summary>
    /// 停止战斗倒计时
    /// </summary>
    public void StopBattleTimer()
    {
        // Debug.Log($"2222222 停止战斗倒计时: isBattleTimerStarted={isBattleTimerStarted}");
        isBattleTimerStarted = false;
        battleTimerCTS?.Cancel();
        battleTimerCTS = new CancellationTokenSource();
        
        // V64.0 - 停止批量死亡处理
        StopBatchDeathProcessing();
    }
    
    /// <summary>
    /// V64.0 - 停止批量死亡处理
    /// </summary>
    private void StopBatchDeathProcessing()
    {
        _batchDeathCTS?.Cancel();
        _batchDeathCTS = new CancellationTokenSource();
        _batchDeathQueue.Clear();
        _isBatchDeathProcessing = false;
    }
    
    /// <summary>
    /// 获取剩余战斗时间（秒）
    /// </summary>
    public float GetRemainingBattleTime()
    {
        if (!isBattleTimerStarted) 
        {
            // Debug.Log($"2222222 战斗倒计时未开始，返回最大时间: {maxBattleTime}秒");
            return maxBattleTime;
        }
        
        float elapsedTime = Time.time - battleStartTime;
        float remainingTime = maxBattleTime - elapsedTime;
        float result = Mathf.Max(0, remainingTime);
        
        // 每次调用都打印详细信息（临时调试）
        // Debug.Log($"2222222 倒计时计算: 当前时间={Time.time}, 开始时间={battleStartTime}, 已过时间={elapsedTime}秒, 最大时间={maxBattleTime}秒, 计算剩余时间={remainingTime}秒, 最终剩余时间={result}秒");
        
        return result;
    }
    
    /// <summary>
    /// 战斗倒计时任务
    /// </summary>
    private async UniTaskVoid Task_BattleTimer(CancellationToken token)
    {
        try
        {
            float lastMonsterCheckTime = 0f;
            // Debug.Log($"2222222 Task_BattleTimer开始执行");
            
            while (!token.IsCancellationRequested && isBattleTimerStarted)
            {
                await UniTask.Delay(100, cancellationToken: token); // 每100毫秒检查一次
                
                if (token.IsCancellationRequested) 
                {
                    // Debug.Log($"2222222 Task_BattleTimer被取消");
                    return;
                }
                
                float remainingTime = GetRemainingBattleTime();
                
                // 增加详细的调试日志
                if (Time.frameCount % 60 == 0) // 每秒打印一次
                {
                    // Debug.Log($"2222222 倒计时检查: 剩余时间={remainingTime}秒, 角色血量={Actor.Hp.Value}, 是否存活={Actor.Hp.Value > float.Epsilon}");
                }
                
                // 时间到了，角色没有死亡，战斗胜利
                if (remainingTime <= 0 && Actor.Hp.Value > float.Epsilon)
                {
                    // Debug.Log($"2222222 倒计时结束，角色存活，战斗胜利！剩余时间={remainingTime}, 角色血量={Actor.Hp.Value}");
                    StopBattleTimer();
                    SettleAccounts(true);
                    return;
                }
                
                // 每秒检测一次怪物循环刷新
                if (Time.time - lastMonsterCheckTime >= 1f)
                {
                    lastMonsterCheckTime = Time.time;
                    CheckAndRestartMonsterSpawning();
                }
            }
            
            // Debug.Log($"2222222 Task_BattleTimer循环结束: token.IsCancellationRequested={token.IsCancellationRequested}, isBattleTimerStarted={isBattleTimerStarted}");
        }
        catch (OperationCanceledException)
        {
            // Debug.Log($"2222222 Task_BattleTimer被正常取消");
            // 正常取消，不需要处理
        }
        catch (Exception ex)
        {
            // Debug.LogError($"2222222 Task_BattleTimer异常: {ex.Message}, 堆栈: {ex.StackTrace}");
        }
    }
    
    /// <summary>
    /// 检查并重新开始怪物刷新
    /// </summary>
    private void CheckAndRestartMonsterSpawning()
    {
        // 检查是否所有怪物都死亡且没有等待出生的怪物，且刷怪器已完成
        if (Monsters.Count == 0 && waitingBornMonsterNum == 0 && 
            StageRound.MonsterSpawner is { IsBrushFinish: { Value: true } } &&
            StageRound.MonsterSpawner.MonsterQueue.Count == 0)
        {
            // 重新从回合第一个怪物ID开始刷怪
            StageRound.MonsterSpawner.RestartFromBeginning();
        }
    }

    /// <summary>
    /// V64.0 - 将怪物添加到批量死亡处理队列
    /// </summary>
    /// <param name="monster">死亡的怪物</param>
    private void AddMonsterToBatchDeathQueue(MonsterThing monster)
    {
        if (monster == null) return;
        
        _batchDeathQueue.Enqueue(monster);
        
        // 如果没有正在处理，启动批量处理
        if (!_isBatchDeathProcessing)
        {
            ProcessBatchDeathQueue(_batchDeathCTS.Token).Forget();
        }
    }
    
    /// <summary>
    /// V64.0 - 批量处理死亡队列，分帧处理避免卡顿
    /// </summary>
    private async UniTaskVoid ProcessBatchDeathQueue(CancellationToken token)
    {
        _isBatchDeathProcessing = true;
        
        try
        {
            while (_batchDeathQueue.Count > 0 && !token.IsCancellationRequested)
            {
                int processedThisFrame = 0;
                
                // 每帧最多处理指定数量的怪物
                while (_batchDeathQueue.Count > 0 && processedThisFrame < MAX_DEATH_PROCESS_PER_FRAME)
                {
                    var monster = _batchDeathQueue.Dequeue();
                    
                    // 立即处理死亡逻辑，不播放动画
                    ProcessMonsterDeathImmediate(monster);
                    
                    processedThisFrame++;
                }
                
                // 等待下一帧
                if (_batchDeathQueue.Count > 0)
                {
                    await UniTask.NextFrame(token);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，清空队列
            _batchDeathQueue.Clear();
        }
        catch (Exception ex)
        {
            Debug.LogError($"V64.0 批量死亡处理异常: {ex.Message}");
        }
        finally
        {
            _isBatchDeathProcessing = false;
        }
    }
    
    /// <summary>
    /// V64.0 - 立即处理怪物死亡，不播放动画，直接回收
    /// </summary>
    /// <param name="monster">死亡的怪物</param>
    private void ProcessMonsterDeathImmediate(MonsterThing monster)
    {
        try
        {
            if (monster?.Monster == null) return;
            
            // 停止怪物的所有移动和攻击
            monster.StopCdExecutor();
            if (monster.Monster is MonsterBase monsterBase)
            {
                monsterBase.MonsterMoveAI?.StopMoveAI();
            }
            
            // 直接回收到对象池，跳过动画
            monster.Monster.TurnToPool().Forget();
        }
        catch (Exception ex)
        {
            Debug.LogError($"V64.0 立即处理怪物死亡异常: {ex.Message}");
        }
    }
    
    /// <summary>
    /// V30.2-死亡动画修复 播放怪物死亡动画然后回到对象池（保留用于特殊情况）
    /// </summary>
    /// <param name="monster">死亡的怪物</param>
    private async UniTaskVoid PlayMonsterDeathAnimation(MonsterThing monster)
    {
        try
        {
            if (monster?.Monster == null)
            {
                Debug.LogWarning("V30.2 怪物或怪物界面为空，直接返回");
                return;
            }

            Debug.Log($"V30.2 怪物 {monster.Monster.gameObject.name} 开始播放死亡动画 die01");

            // 停止怪物的所有移动和攻击
            monster.StopCdExecutor();
            if (monster.Monster is MonsterBase monsterBase)
            {
                monsterBase.MonsterMoveAI?.StopMoveAI();
            }

            // 播放死亡动画
            monster.Monster.PlayAnimation("die01", false, false);

            // 获取死亡动画的持续时间
            float deathAnimDuration = monster.Monster.GetAnimationDuration("die01");
            if (deathAnimDuration <= 0)
            {
                deathAnimDuration = 1.0f; // 默认1秒
                Debug.LogWarning($"V30.2 怪物 {monster.Monster.gameObject.name} 无法获取die01动画时长，使用默认1秒");
            }

            Debug.Log($"V30.2 怪物 {monster.Monster.gameObject.name} 死亡动画时长: {deathAnimDuration}秒");

            // 等待死亡动画播放完成
            await UniTask.Delay(TimeSpan.FromSeconds(deathAnimDuration));

            Debug.Log($"V30.2 怪物 {monster.Monster.gameObject.name} 死亡动画播放完成，回到对象池");

            // 死亡动画播放完成后，将怪物回到对象池
            monster.Monster.TurnToPool().Forget();
        }
        catch (System.OperationCanceledException)
        {
            // 取消操作，直接回到对象池
            Debug.Log($"V30.2 怪物死亡动画被取消，直接回到对象池");
            monster?.Monster?.TurnToPool().Forget();
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"V30.2 播放怪物死亡动画时发生异常: {ex.Message}");
            // 发生异常时，直接回到对象池
            monster?.Monster?.TurnToPool().Forget();
        }
    }

    /// <summary>
    /// V58.0 加载子弹飞行特效预制体
    /// </summary>
    /// <param name="bullet">子弹实例</param>
    /// <param name="prefabName">预制体名称</param>
    /// <param name="scale">缩放比例</param>
    private async UniTask LoadBulletPrefabAsync(BulletBase bullet, string prefabName, float scale)
    {
        try
        {
            // V58.1 构造完整的Addressable路径
            string cleanPrefabName = prefabName.EndsWith(".prefab") ? prefabName.Substring(0, prefabName.Length - 7) : prefabName;
            string fullPrefabPath = $"Assets/Temp/model/prefab/Bullet/{cleanPrefabName}.prefab";
            // Debug.Log($"V58.1 开始加载子弹预制体: {prefabName}, 清理后名称: {cleanPrefabName}, 完整路径: {fullPrefabPath}, 缩放: {scale}");
            
            // 加载预制体资源
            GameObject prefabAsset = await ResMgrAsync.LoadResAsync<GameObject>(fullPrefabPath);
            
            if (prefabAsset != null)
            {
                // 在PrefabElement下实例化预制体
                bullet.PrefabInstance = Instantiate(prefabAsset, bullet.PrefabElement.transform);
                
                // 设置预制体的初始位置和旋转（与子弹本体保持一致）
                bullet.PrefabInstance.transform.localPosition = Vector3.zero;
                bullet.PrefabInstance.transform.localRotation = Quaternion.identity;
                
                // 应用缩放
                bullet.PrefabInstance.transform.localScale = Vector3.one * scale;
                
                // 确保预制体可见
                bullet.PrefabElement.SetActive(true);
                bullet.PrefabInstance.SetActive(true);
                
                // Debug.Log($"V58.1 子弹预制体加载成功: {prefabName}");
            }
            else
            {
                // Debug.LogWarning($"V58.1 无法加载子弹预制体: {prefabName}, 路径: {fullPrefabPath}");
            }
        }
        catch (Exception ex)
        {
            // Debug.LogError($"V58.1 加载子弹预制体失败: {prefabName}, 错误: {ex.Message}");
        }
    }
}