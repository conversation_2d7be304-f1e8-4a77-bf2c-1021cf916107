--[[
********************************************************************
    created:    2024/06/27
    author :    李锦剑
    purpose:    登入界面
*********************************************************************
--]]

require 'SensitiveWord'

-- 登录UI
local luaID = ('UILogin')

---登入界面
---@class UILogin:UIWndBase
local m = {}

-------------------------------------------
--预制体自适应
-------------------------------------------
function m:AdaptScale()
	--UI适配
	HelperL.AdaptScale(m.objList.CompLogo, 6)
end

-------------------------------------------
--资源预加载
-------------------------------------------
function m.GetResourceList()
	return {
		--预设体
		"Assets/Temp/ui/Main/OverlayWaiting.prefab"
	}
end

-------------------------------------------
-- 初始化
-------------------------------------------
function m:OnCreate()
	SoundManager:PlayMusic(SoundID.Login)

	m.objList.Txt_TogLabel.text = GetGameText(luaID, 1)
	m.objList.Txt_Start.text = GetGameText(luaID, 2)
	m.objList.Txt_HealthTip1.text = GetGameText('SceneManager', 2)
	m.objList.Txt_HealthTip2.text = GetGameText('SceneManager', 3)
	m.objList.Txt_HealthTip3.text = GetGameText('SceneManager', 4)
	m.objList.Txt_Versions.text = Helper.GetBigVer()
	
	--是否勾选记住账号和密码
	m.objList.Tog_SaveName.isOn = PlayerPrefs.GetInt("SaveAccount", 0) == 1
	--获取平台
	m.platform = GameLuaAPI.GetPlatform()

	--测试微信自动登录，取消下面注释即可
	m.platform = "webgl11"
	--  m.objList.Tog_SaveName.isOn = false    ---true  或者false

	m.RegisterClickEvent()
	return true
end

-------------------------------------------
-- 窗口开启
-------------------------------------------
function m:OnOpen()
	SceneManager.islogOut = false
	m.objList.Obj_Login.gameObject:SetActive(true)
	m.objList.Obj_CreationRole.gameObject:SetActive(false)
	m.loginComplete = false
	--默认显示用户名和密码输入框界面
	m.objList.Obj_UserNameAndPassword.gameObject:SetActive(true)

	--用户账号
	local UserName = ""
	--勾选记住账号和密码
	if m.objList.Tog_SaveName.isOn == true then
		--获取记录用户名
		UserName = PlayerPrefs.GetString("UserName", '')
	end

	--SDK的Uid直接做用户名
	if GameLuaAPI.Uid ~= nil and GameLuaAPI.Uid ~= "" then
		local hideList = {
			[GameLuaAPI.eChannel.eChannel_Douyin] = true,
		}
		if hideList[GameLuaAPI.Channel] == true then
			m.objList.Obj_UserNameAndPassword.gameObject:SetActive(false)
			UserName = GameLuaAPI.Uid
		end
	end

	--微信特殊处理
	if m.platform == "webgl" then
		m.objList.Tog_SaveName.isOn = true
		m.objList.Obj_Login.gameObject:SetActive(false)
		if UserName == '' then
			local str = ""
			for i = 1, 4, 1 do
				str = str .. tostring(math.random(1, 9))
			end
			UserName = str .. tostring(HelperL.GetServerTime())
			print('-------生成随机账号 UserName=', UserName)
		end
	end

	print('-----用户账号------UserName=', UserName)
	print('-----平台------platform=', m.platform)
	m.objList.Inp_UserName.text = UserName

	--微信特殊处理自动登入
	if m.platform == "webgl" then
		m.OnClickLogin()
	end
	
	EventManager:Subscribe(EventID.LoginComplete, m.LoginCompleteHandle)
	
end

-------------------------------------------
--注册点击事件
-------------------------------------------
function m.RegisterClickEvent()
	--适龄提示
	m.objList.Btn_AgeAppropriateReminder.onClick:AddListenerEx(m.OnClickTip)
	--开始游戏
	m.objList.Btn_Start.onClick:AddListenerEx(m.OnClickLogin)
	--随机名
	m.objList.Btn_RandName.onClick:AddListenerEx(function()
		m.objList.Inp_RandName.text = m.RandName()
	end)
	--创建角色
	m.objList.Btn_CreationRole.onClick:AddListenerEx(m.OnConfirmCreateActor)
end

-------------------------------------------
--登录回调
-------------------------------------------
function m.LoginCallback()
	--取消登录回调事件
	EventManager:UnSubscribe(EventID.LoginCallback, m.LoginCallback)
	local zoneID = LoginModule:GetZoneID()
	local zoneItem = ZoneIDToItem[zoneID]
	if zoneItem and zoneItem.state == 3 then
		HelperL.ShowMessage(TipType.MsgBoxType1, zoneItem.notice,
			{ text = GetGameText(luaID, 5), callback = nil, highlight = true })
		HelperL.CloseBlackWaitingUI()
		return
	end

	local actorList = LoginModule:GetActorList()
	if not actorList then
		warn('UILogin:OnWndMsg not actorList')
		HelperL.CloseBlackWaitingUI()
		return
	end
	m.objList.Img_Fill.fillAmount = 0
	m.objList.Btn_Start.gameObject:SetActive(false)
	m.objList.Img_BG1.gameObject:SetActive(false)
	m.objList.Obj_UserNameAndPassword.gameObject:SetActive(false)
	m.objList.Progress1.gameObject:SetActive(true)
	if #actorList == 0 then
		--微信特殊处理
		if m.platform == "webgl" then
			local actorName = m.RandName()
			LoginModule:CreateActor(actorName, m.OnCreateActorCallback)
		else
			--显示创建角色界面
			m.objList.Obj_Login.gameObject:SetActive(false)
			m.objList.Obj_CreationRole.gameObject:SetActive(true)
		end
	else
		-- 自动选第一个细胞
		local actorData = actorList[1]
		LoginModule:SelectActor(actorData)
		m.frameCount = 0
		m.updateTimer = Timer.New(m.OnFrameUpdate, 0.05, -1)
		m.updateTimer:Start()
	end
	HelperL.BeginLogin = true
end

function m:OnFrameUpdate()
	if HelperL.BeginLogin == false then
		return
	end
	m.frameCount = m.frameCount + 1
	m.objList.Img_Fill.fillAmount = m.frameCount / 300
	if m.frameCount >= 300 then
		m.updateTimer:Stop()
		if m.loginComplete then
			m.LoginCompleteHandle()
		end
	end
end

-------------------------------------------
-- 点击登录按钮
-------------------------------------------
function m.OnClickLogin()
	--检测敏感词标识
	if m.objList.Inp_UserName.textComponent:GetComponent("TextAssist").HasSensitiveWords then
		HelperL.ShowMessage(TipType.FlowText, "输入文字有非法字符")
		return
	end

	local UserName = m.objList.Inp_UserName.text
	if GameLuaAPI.ChannelID == GameChannel.ChannelNone then
		--去除非字母数字的字符
		local str = string.gsub(UserName, "%s+", ""):gsub("[^%w]", "")
		if string.len(str) == 0 then
			HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 3))
			m.objList.Inp_UserName.text = ''
			return
		end
		GameLuaAPI.Uid = UserName.."101010"
		LoginModule._p_s_d = m.objList.Inp_Password.text
	end

	-- 保存为全局变量
	-- if GameLuaAPI.Uid == nil or GameLuaAPI.Uid == "" then
	-- 	GameLuaAPI.Uid = UserName
	-- end


	-- 记录用户名密码
	if m.objList.Tog_SaveName.isOn == true then
		PlayerPrefs.SetInt("SaveAccount", 1)
		PlayerPrefs.SetString("UserName", UserName)
	else
		PlayerPrefs.SetInt("SaveAccount", 0)
	end

	--取消登录回调事件
	EventManager:UnSubscribe(EventID.LoginCallback, m.LoginCallback)
	--注册登录回调事件
	EventManager:Subscribe(EventID.LoginCallback, m.LoginCallback)
	print('-----申请登录-----UserName=', GameLuaAPI.Uid)
	HelperL.ShowBlackWaitingUI(1, 30.0, nil, "登录中……")
	-- 申请登录
	LoginModule:DoLogin()
end

-------------------------------------------
--适龄提示
-------------------------------------------
function m.OnClickTip()
	UIManager:OpenWnd(WndID.SltsDialog)
end

-------------------------------------------
--随机名字
-------------------------------------------
function m.RandName()
	local actorName = ''
	local list, num
	for i = 1, 4, 1 do
		list = Schemes.RandomName.GetItemList(i)
		if list then
			num = math.random(1, #list)
			actorName = actorName .. tostring(list[num].Char)
		end
	end
	return actorName
end

-------------------------------------------
-- 点击创建细胞处理
-------------------------------------------
function m.OnConfirmCreateActor()
	--检测敏感词标识
	if m.objList.Inp_RandName.textComponent:GetComponent("TextAssist").HasSensitiveWords then
		HelperL.ShowMessage(TipType.FlowText, "输入文字有非法字符")
		m.objList.Inp_RandName.text = ""
		return
	end

	local roleName = m.objList.Inp_RandName.text
	local len = Helper.GetStringLength(roleName)
	if len == 0 then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 8))
		return
	end

	-- if len > 7 then
	-- 	HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 10))
	-- 	return
	-- end

	local _, bool = SensitiveWord.Check(roleName)
	if bool then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 23))
		m.objList.Inp_RandName.text = ""
		return
	end
	m.objList.Btn_Start.gameObject:SetActive(false)
	m.objList.Img_BG1.gameObject:SetActive(false)
	m.objList.Obj_UserNameAndPassword.gameObject:SetActive(false)
	m.objList.Progress1.gameObject:SetActive(true)
	 
	m.frameCount = 0
	m.updateTimer = Timer.New(m.OnFrameUpdate, 0.05, -1)
	m.updateTimer:Start()
	--创角角色
	LoginModule:CreateActor(roleName, m.OnCreateActorCallback)
	--HelperL.ShowBlackWaitingUI(1, 30.0, nil, "登录中……")
end

-------------------------------------------
-- 创建细胞回调处理
-------------------------------------------
function m.OnCreateActorCallback(www)
	if www == nil or www.text == nil or www.text == "" then
		warn('UILogin.OnCreateActorCallback 返回无内容')
		return
	end

	local mm = dkjsonHelper.decode(www.text) or { Result = "解析失败" }
	if mm.Result ~= nil and mm.Result ~= "" then
		HelperL.ShowMessage(TipType.FlowText, mm.Result.."。")
		HelperL.CloseBlackWaitingUI()
		return
	end
	m.objList.Obj_Login.gameObject:SetActive(true)
	m.objList.Obj_CreationRole.gameObject:SetActive(false)

	UIManager:SendWndMsg(WndID.TipManager, WndMsg.TipManager_CloseTip, TipType.MsgBoxType3)

	local newData = dkjsonHelper.decode(mm.Actor)[1]
	LoginModule:AddActorData(newData)

	-- 自动选择新细胞
	LoginModule:SelectActor(newData)

	--上报友盟
	local actorName = newData.ActorName
	local actorID = newData.ActorID
	--用户注册
	HelperL.SendUMengEvent(
		UMENG_EVENT_TYPE.USER_REGISTER,
		tostring(actorName),
		tostring(actorID),
		tostring(LoginModule.isLoginOK)
	)
	---创建角色
	HelperL.SDKSubmitRoleData(SDK_SUBMIT_ROLEDATA_EVENTTYPE.CREATE_ROLE, actorID, actorName)
end

function m.LoginCompleteHandle()
	if m.frameCount < 300 then
		m.loginComplete = true
		return
	end
	UIManager:CloseWndByID(WndID.Login)
	--关闭等待界面
	HelperL.CloseBlackWaitingUI()
	-- 打开主界面
	local flag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_GUIDE_LOG_IDFLAG1, 0) --新手引导（播放视频）
	if flag == 0 then
		if SceneManager.curSceneName ~= 'GameScene' then
			UIManager:OpenWnd(WndID.MainTitle)
		end
	else
		UIManager:OpenWnd(WndID.MainTitle)
	end
end

-------------------------------------------
-- 窗口关闭事件
-------------------------------------------
function m:OnClose()
	EventManager:UnSubscribe(EventID.LoginComplete, m.LoginCompleteHandle)
	m.objList.Inp_UserName.text = ''
	if m.updateTimer then
		m.updateTimer:Stop()
	end
end

return m
