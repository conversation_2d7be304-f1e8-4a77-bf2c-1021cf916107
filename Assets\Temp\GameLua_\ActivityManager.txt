-- 活动管理类
ActivityManager = {}

ACTVID =
{
	ACTVID_VALUE_NULL = 0,
	ACTVID_TIMESHOPPING = 40, -- 限时特购
	ACTVID_RECHARGEBUY_1 = 77, -- 充值直销1(每日特惠日礼包)
	ACTVID_RECHARGEBUY_2 = 78, -- 充值直销2(每日特惠周礼包)
	ACTVID_RECHARGEBUY_3 = 79, -- 充值直销3(每日特惠月礼包)
	MAX = 1,
}

ActivityManager.StateList = {}
ActivityManager.ActvList = {}
ActivityManager.ActvTypeList = {}
ActivityManager.LatestActv = { ActvID = 0, StageID = 0, BeginTime = 0, EndTime = 0 }

function ActivityManager:UpdateStateList(newStateList)
	for _, v in ipairs(newStateList) do
		local isExist = false
		for i = 1, #self.StateList do
			if self.StateList[i].ActvID == v.ActvID then
				self.StateList[i] = v
				isExist = true
			end
		end
		if isExist == false then
			table.insert(self.StateList, v)
		end

		if v.StageID > 0 and v.BeginTime > self.LatestActv.BeginTime then
			self.LatestActv = v
		end
	end

	self:RebuildActvList()

	EventManager:Fire(EventID.ActivityStateChange, newStateList)
end

function ActivityManager:RebuildActvList()
	self.ActvList = {}
	self.ActvTypeList = {}
	for i, v in ipairs(self.StateList) do
		self.ActvList[v.ActvID] = v
		if not self.ActvTypeList[v.ActType] then
			self.ActvTypeList[v.ActType] = { v }
		else
			table.insert(self.ActvTypeList[v.ActType], v)
		end
	end
end

function ActivityManager:GetLatestActv()
	return self.LatestActv
end

function ActivityManager:GetItemByID(actvID)
	return self.ActvList[actvID]
end

--活动是否开放
function ActivityManager:IsActivityOpen(actvID)
	local actvItem = self.ActvList[actvID]
	if not actvItem then
		return false
	end

	local now = Premier.Instance:GetServerTime()
	if actvItem.EndTime > now then
		return true
	end
	return false
end

function ActivityManager:IsActivityOpenEx(actvList)
	local now = Premier.Instance:GetServerTime()
	for i, v in pairs(actvList) do
		local item = self.ActvList[i]
		if item and item.EndTime > now then
			return true
		end
	end
	return false
end

function ActivityManager:GetStageIDByActvID(actvID)
	local actvItem = self.ActvList[actvID]
	if not actvItem then
		return 0
	end

	if actvItem.StageID > 0 then
		return actvItem.StageID
	end
	return 0
end

--获取活动结束时间
function ActivityManager:GetActivityEndTime(actvID)
	local actvItem = self.ActvList[actvID]
	if not actvItem then
		return 0
	end

	if actvItem.StageID > 0 then
		return actvItem.EndTime
	end
	return 0
end

function ActivityManager:GetActivityBeginTime(actvID)
	local actvItem = self.ActvList[actvID]
	if not actvItem then
		return 0
	end

	if actvItem.StageID > 0 then
		return actvItem.BeginTime
	end
	return 0
end

function ActivityManager:GetActivityBeginDay(actvID)
	local actvItem = self.ActvList[actvID]
	if not actvItem then
		return 0
	end

	if actvItem.StageID > 0 then
		local beginDate = os.date("*t", actvItem.BeginTime)
		local beginTime = actvItem.BeginTime - beginDate.hour * 3600 - beginDate.min * 60 - beginDate.sec
		local serverTime = Premier.Instance:GetServerTime()
		local remainingAllTime = serverTime - beginTime
		return math.floor(remainingAllTime / 86400) + 1
	end
	return 0
end

--获取活动类型(1:开服;2:合服;3:精彩活动)
function ActivityManager:GetActivityType(actvID)
	local actvItem = self.ActvList[actvID]
	if not actvItem then
		return 0
	end
	if actvItem.StageID > 0 then
		return actvItem.ActType
	end
	return 0
end

--检测此类型的活动是否存在 并在开启中
function ActivityManager:CheckActivityType(actType)
	local list = self.ActvTypeList[actType]
	if not list then
		return false
	end
	for i, v in ipairs(list) do
		if self:IsActivityOpen(v.ActvID) then
			return true
		end
	end
	return false
end
