--[[
********************************************************************
    created:    2023/07/16
    author :    李锦剑
    purpose:    装备升级界面
*********************************************************************
--]]

local luaID = ('UIEquipUpgrade')

---@class UIEquipUpgrade:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    ---@type Item_Attribute2[]
    m.Item_Attribute_List1 = {}
    ---@type Item_Attribute2[]
    m.Item_Attribute_List2 = {}
    ---@type Item_Attribute2[]
    m.Item_Attribute_List22 = {}

    m.objList.Txt_Wear.text = GetGameText(luaID, 1)
    m.objList.Txt_Demount.text = GetGameText(luaID, 2)
    --m.objList.Txt_Upgrade.text = GetGameText(luaID, 3)
    m.selectIndexList = {}
    m.selectIndexTable = {}

    m.SynthesisExpend_Item_List = {}
    for i = 1, 100, 1 do
        m.SynthesisExpend_Item_List[i] = m.Creation_Item_Eq(i)
    end

    -- 初始化装备经验相关数据
    m.equipExpList = {}  -- 存储装备经验数据
    m.maxEquipExpSlots = 5  -- 最大装备经验槽位
    m.minEquipExpSlots = 1  -- 最小装备经验槽位

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(uid)
    m.uid = uid
    m.UpdateView()

    m.UpdateEquipExpDisplay()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Demount.onClick:AddListenerEx(function()
        m:CloseSelf()
        m.EquipDemount(m.uid)
    end)
    m.objList.Btn_Wear.onClick:AddListenerEx(function()
        m:CloseSelf()
        m.EquipWear(m.uid)
    end)
    m:AddClick(m.objList.Btn_Upgrade, function()
        --m:CloseSelf()
        local entity = EntityModule:GetEntity(m.uid)
        local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local cfg = Schemes.Equipment:Get(equipID)
        local smeltID = cfg.SmeltID
        m.LuaRequestAddExp(smeltID)
    end)

    m.objList.Btn_FR.onClick:AddListenerEx(function()        
        m.UpdateEquipExpDisplay()
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local entity = EntityModule:GetEntity(m.uid)
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local cfg = Schemes.Equipment:Get(equipID)
    local smeltID = cfg.SmeltID
    m.objList.Txt_Name.text = cfg.GoodsName
    m.objList.Txt_Type.text = HelperL.GetNameByQuality(cfg.QualityLevel)

    if not m.equipItem then
        m.equipItem = _GAddSlotItem(m.objList.Obj_Equip)
        m.equipItem:EnableClick(false)
    end
    m.equipItem:SetEntity(entity)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
    m.objList.Txt_Level.text = string.format(GetGameText(luaID, 4), level, maxLevel)
    m.objList.Txt_Describe.text = HelperL.GetContenBRText(cfg.TipsDes)
    m.objList.Txt_TipsDes.text = HelperL.GetContenBRText(cfg.TipsDes)
    local starCfg = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, level)
    local nextStarCfg = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, level+1)
    if nextStarCfg and starCfg then
        local exp = LogicValue.GetEquipDataLV2(smeltID, 1)
        m.objList.Txt_Ex.text = (exp - starCfg.StarExp).."/"..(nextStarCfg.StarExp - starCfg.StarExp)
        m.objList.Img_Fill.fillAmount = (exp - starCfg.StarExp)/(nextStarCfg.StarExp - starCfg.StarExp)
    end
    
    if not starCfg then
        error(string.format('EquipSmeltStar配置错误 smeltID=%s   level=%s', smeltID, level))
        return
    end

    local num1 = SkepModule:GetGoodsCount(starCfg.CostGoodsID1)
    if starCfg.CostGoodsID1 > 0 then
        AtlasManager:AsyncGetGoodsSprite(starCfg.CostGoodsID1, m.objList.Img_Expend1)
        local goodsName = Schemes:GetGoodsConfig(starCfg.CostGoodsID1).GoodsName
        local color1 = num1 >= starCfg.CostGoodsID1Num and '#FFFFFF' or '#FF8CFF'
        m.objList.Txt_Describe1.text = string.format("%s：<color=%s>%s/%s</color>", goodsName, color1,
            HelperL.GetChangeNum(num1), HelperL.GetChangeNum(starCfg.CostGoodsID1Num))
        m.objList.Obj_Expend1.gameObject:SetActive(true)
    else
        m.objList.Obj_Expend1.gameObject:SetActive(false)
    end

    -- local num2 = SkepModule:GetGoodsCount(starCfg.CostGoodsID2)
    -- if starCfg.CostGoodsID2 > 0 then
    --     AtlasManager:AsyncGetGoodsSprite(starCfg.CostGoodsID2, m.objList.Img_Expend2)
    --     local color2 = num2 >= starCfg.CostGoodsID2Num and '#FFFFFF' or '#FF8CFF'
    --     m.objList.Txt_Expend2.text = string.format("<color=%s>%s/%s</color>", color2, HelperL.GetChangeNum(num2),
    --         HelperL.GetChangeNum(starCfg.CostGoodsID2Num))
    --     m.objList.Txt_Expend2.gameObject:SetActive(true)
    -- else
    --     m.objList.Txt_Expend2.gameObject:SetActive(false)
    -- end

    if m.IsWearEquip(m.uid) then
        if level < maxLevel then
            -- HelperL.SetImageGray(m.objList.Btn_Upgrade, num1 < starCfg.CostGoodsID1Num or num2 < starCfg.CostGoodsID2Num)
            -- m.objList.Img_RedDot.gameObject:SetActive(num1 >= starCfg.CostGoodsID1Num and
            --     num2 >= starCfg.CostGoodsID2Num)
            m.objList.Btn_Upgrade.gameObject:SetActive(true)
        else
            m.objList.Btn_Upgrade.gameObject:SetActive(false)
        end

        m.objList.Btn_Wear.gameObject:SetActive(false)
        m.objList.Btn_Demount.gameObject:SetActive(false)
    else
        m.objList.Btn_Upgrade.gameObject:SetActive(false)
        m.objList.Btn_Wear.gameObject:SetActive(true)
        m.objList.Btn_Demount.gameObject:SetActive(false)
        m.objList.Img_RedDot2.gameObject:SetActive(HelperL.IsRecommendedEquipment(entity))
    end

    local contentList = {}
    local attack = 0
    local hp = 0
    if cfg.QualityLevel < starCfg.AttackList.Length then
        attack = attack + starCfg.AttackList[cfg.QualityLevel]
    end
    if attack > 0 then
        table.insert(contentList, '攻击：' .. HelperL.GetChangeNum(attack))
    end
    if cfg.QualityLevel < starCfg.MaxHpList.Length then
        hp = hp + starCfg.MaxHpList[cfg.QualityLevel]
    end
    if hp > 0 then
        table.insert(contentList, '生命：' .. HelperL.GetChangeNum(hp))
    end
    local max1 = math.max(#contentList, #m.Item_Attribute_List1)
    for i = 1, max1, 1 do
        if not m.Item_Attribute_List1[i] then
            m.Item_Attribute_List1[i] = m.Creation_Item_Attribute(m.objList.Grid_Attribute1, i)
        end
        m.Item_Attribute_List1[i].UpdateData(3, contentList[i])
    end

    local effectIDs = HelperL.Split(cfg.EffectTipsID, ';')
    local max2 = math.max(#effectIDs, #m.Item_Attribute_List2)
    local commonProp, _type, content
    for i = 1, max2, 1 do
        if not m.Item_Attribute_List2[i] then
            m.Item_Attribute_List2[i] = m.Creation_Item_Attribute(m.objList.Grid_Attribute2, i)
        end
        content = ''
        commonProp = Schemes.CommonProp:Get(effectIDs[i])
        if commonProp then
            content = commonProp.Remark
        end
        if cfg.QualityLevel >= i + 1 then
            --激活
            _type = 1
        else
            --未激活
            _type = 2
        end
        m.Item_Attribute_List2[i].UpdateData(_type, content)
    end

    
end

--------------------------------------------------------------------
-- 是穿戴装备
---@param uid integer 实体唯一ID
---@return boolean
--------------------------------------------------------------------
function m.IsWearEquip(uid)
    local entity = EntityModule:GetEntity(uid)
    if entity then
        local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local cfg = Schemes.Equipment:Get(equipID)
        local wear_entity = EntityModule:GetEntity(SkepModule.GetEquipSkep()[cfg.SubType])
        if wear_entity then
            return uid == wear_entity.uid
        end
    end

    return false
end

--------------------------------------------------------------------
-- 创建属性框
---@param parent any
---@param index integer
---@return Item_Attribute2
--------------------------------------------------------------------
function m.Creation_Item_Attribute(parent, index)
    ---@class Item_Attribute2
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Attribute)
    item.com.Img_Type.color = HelperL.GetQualityColorRGBA(index + 1)
    --- 更新数据
    ---@param _type integer 1激活，2未激活
    ---@param content string
    item.UpdateData = function(_type, content)
        if content and content ~= '' then
            item.com.Img_Type.gameObject:SetActive(false)
            item.com.Img_Lock.gameObject:SetActive(false)

            if _type == 1 then
                --激活
                item.com.Img_Type.gameObject:SetActive(true)
            elseif _type == 2 then
                --未激活
                item.com.Img_Lock.gameObject:SetActive(true)
            elseif _type == 3 then

            end
            item.com.Txt_Des.text = content
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 装备穿戴
---@param uid integer 实体唯一ID
--------------------------------------------------------------------
function m.EquipWear(uid)
    SkepModule.PutOnEquipment(uid)

    local entity = EntityModule:GetEntity(uid)
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local cfg = Schemes.Equipment:Get(equipID)
    GamePlayerData.ActorEquipNew:SetEquipTypeQuality(equipID, cfg.QualityLevel)
end

--------------------------------------------------------------------
-- 装备脱下
---@param uid integer 实体唯一ID
--------------------------------------------------------------------
function m.EquipDemount(uid)
    SkepModule.PutOnEquipment(uid)

    local entity = EntityModule:GetEntity(uid)
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    -- local cfg = Schemes.Equipment:Get(equipID)
    GamePlayerData.ActorEquipNew:SetEquipTypeQuality(equipID, -1)
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        -- m.UpdateView()
        --播放升级特效
        HelperL.PlayVFX()
        SoundManager:PlaySound(SoundID.Upgrade)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

--------------------------------------------------------------------
-- 读取装备经验数据
---@param equipID integer 装备ID
---@return table 装备经验数据表
--------------------------------------------------------------------
function m.ReadEquipExpData(equipID)
    local cfg = Schemes.Equipment:Get(equipID)
    if not cfg then return {} end
    
    local expData = {}
    print("cfg.ModelID =============== ",cfg.ModelID)
    if cfg.ModelID and cfg.ModelID ~= "" then
        local expList = HelperL.Split(cfg.ModelID, "|")
        for _, expInfo in ipairs(expList) do
            local equipExp = HelperL.Split(expInfo, ";")
            if #equipExp == 2 then
                table.insert(expData, {
                    equipID = tonumber(equipExp[1]),
                    exp = tonumber(equipExp[2])
                })
            end
        end
    end    
    return expData
end

--------------------------------------------------------------------
-- 从背包中获取装备列表
---@param expData table 经验数据表
---@return table 背包中的装备列表
--------------------------------------------------------------------
function m.GetEquipsFromBackpack(expData)
    local backpackEquips = {}
    local skepEquips = SkepModule.GetEquipSkep()
    
    -- for _, expInfo in ipairs(expData) do
    --     if expInfo.equipID > 0 then
    --         -- 遍历背包中的装备
    --         for _, equip in pairs(skepEquips) do
    --             local entity = EntityModule:GetEntity(equip)
    --             if entity then
    --                 local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    --                 print("equipID ===========GetEquipsFromBackpack============== ",equipID)
    --                 if equipID == expInfo.equipID then
    --                     table.insert(backpackEquips, entity)
    --                 end
    --             end
    --         end
    --     end
    -- end

    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    if skepBag then
        local goodsID, entity
        for _, expInfo in ipairs(expData) do
            if expInfo.equipID > 0 then
                for i = 0, skepBag.indexMaxsize do
                    entity = EntityModule:GetEntity(skepBag[i])
                    if entity then
                        goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                        if goodsID == expInfo.equipID then
                            table.insert(backpackEquips, entity)
                        end
                    end
                end
            end
        end
    end

    
    -- 背包里的数据
    
    
    return backpackEquips
end

--------------------------------------------------------------------
-- 更新装备经验显示
---@param equipID integer 装备ID
--------------------------------------------------------------------
function m.UpdateEquipExpDisplay()

    m.addExp = 0
    for i = 1, #m.SynthesisExpend_Item_List do
        local item = m.SynthesisExpend_Item_List[i]
        item.uid = 0
        item.exp = 0
        item.com.gameObject:SetActive(false)   
    end
    
    local entity = EntityModule:GetEntity(m.uid)
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    m.equipExpList = m.ReadEquipExpData(equipID)
    print("#m.equipExpList ========= ",#m.equipExpList)
    local backpackEquips = m.GetEquipsFromBackpack(m.equipExpList)
    print("#backpackEquips ========= ",#backpackEquips)
    if #backpackEquips <= 0 then
        HelperL.ShowMessage(TipType.FlowText, "背包中没有材料！")
        return
    end

    

    for i = 1, #backpackEquips do
        if i <= #m.SynthesisExpend_Item_List then
            local equipID = backpackEquips[i]:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            local item = m.SynthesisExpend_Item_List[i]
            item.com.gameObject:SetActive(true)                
            item.slot:SetItemID(equipID)
            item.uid = backpackEquips[i].uid
            for i = 1, #m.equipExpList do
                if m.equipExpList[i].equipID == equipID then
                    m.addExp = m.addExp + m.equipExpList[i].exp
                    item.exp = m.equipExpList[i].exp
                    break
                end
            end
        end
    end
end

--------------------------------------------------------------------
function m.LuaRequestAddExp(smeltID)
    local upgradeLevel = LogicValue.GetEquipDataLV2(smeltID, 0)
    local equipSmelt = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, upgradeLevel)
    if not equipSmelt then
        return
    end
    if m.addExp == nil or m.addExp == 0 then
        HelperL.ShowMessage(TipType.FlowText, "请先放入材料！")
        return
    end

    local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(smeltID)
    if not equipSmeltList then
        error(log .. '读取 EquipSmeltStar 表失败 stageID=' .. smeltID)
        return
    end

    --最后一个配置等级为最大等级
    local maxLevel = equipSmeltList[#equipSmeltList].StarLvl
    --判断是否满级
    if upgradeLevel >= maxLevel then
        HelperL.ShowMessage(TipType.FlowText, "已满级！")
        return
    end
    
    local exp = LogicValue.GetEquipDataLV2(smeltID, 1)    

    local exValue = exp + m.addExp
    --奖励
    local goodInfo = '0'
    local costInfo = ""
    for i = 1, #m.SynthesisExpend_Item_List do
        local item = m.SynthesisExpend_Item_List[i]
        if item.exp > 0 then            
            if costInfo == "" then
                costInfo = string.format("%s;%s", item.uid, 1)
            else
                costInfo = string.format("%s|%s;%s", costInfo, item.uid, 1)
            end
        end  
    end

    --扣消耗
    HelperL.RequestDirectGiveGoodsy('0', costInfo, function(resultCode, content)
        --扣消耗--失败
        print("resultCode ======= ",resultCode)
        if resultCode ~= 0 then
            return
        end
        --保存逻辑值
        local bool = LogicValue.SetEquipDataLV2(smeltID, 1, exValue)
        if not bool then
            print("smeltID ================ ",smeltID," exValue ========== ",exValue)
            return
        end

        local level = 0
        for i = upgradeLevel, #equipSmeltList, 1 do
            if equipSmeltList[i] then
                --判断所需经验值
                if exValue >= equipSmeltList[i].StarExp then
                    level = equipSmeltList[i].StarLvl
                else
                    break
                end
            end
        end
        if level > upgradeLevel then
            LogicValue.SetEquipDataLV2(smeltID, 0, level)
        end
        m.addExp = 0
        HelperL.ShowMessage(TipType.FlowText, "吞噬成功！")
        for i = 1, #m.SynthesisExpend_Item_List do
            local item = m.SynthesisExpend_Item_List[i]
            item.com.gameObject:SetActive(false)   
        end

        m.UpdateView()
    end, false)
end


--------------------------------------------------------------------
-- 创建副本栏
--------------------------------------------------------------------
function m.Creation_Item_Eq(index)
    local item = {}
    item.index = index
    item.uid = 0
    item.exp = 0
    item.slot = nil
    item.com = m:CreateSubItem(m.objList.Grid_Eq, m.objList.Item_Equip)
    item.com.Btn_QX.onClick:AddListenerEx(function()
        item.com.gameObject:SetActive(false)
        m.addExp = m.addExp - item.exp
        item.uid = 0
        item.exp = 0
    end)    
    item.slot = _GAddSlotItem(item.com.gameObject)
    item.slot.gameObject.transform.localPosition = Vector3(0, 26, 0)
    item.com.gameObject:SetActive(false)
    -- item.UpdateView = function()
        
    -- end
    return item
end


return m