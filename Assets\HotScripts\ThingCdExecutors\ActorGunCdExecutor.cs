﻿using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using HotScripts;

using Thing;

using UnityEngine;

using View;

namespace ThingCdExecutors
{
    /// <summary>
    ///     玩家阵营的枪的执行器
    /// </summary>
    public class ActorGunCdExecutor : GunCdExecutor
    {
        /// <summary>
        ///     枪属于哪个玩家角色
        /// </summary>
        public ActorThing Actor => GunThing.Owner as ActorThing;

        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 找一个敌人作为攻击方向
            var distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy?.Thing2 == null)
            {
                return;
            }

            // V30.2-动画系统修复 有攻击目标时播放攻击动画attack01，智能链接后续动画
            Debug.Log($"V30.2 ActorGunCdExecutor 角色 {SingletonMgr.Instance.BattleMgr.PlayerActor.gameObject.name} 发射子弹，播放攻击动画 attack01");
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayAnimation("attack01", false, true);

            // 设置角色朝向：只有在待机状态（不移动）时才转身
            if (!SingletonMgr.Instance.BattleMgr.PlayerActor.PlayerMove.IsMoving)
            {
                var shootDir = (distanceEnemy.Thing2.Position - Actor.Position);
                SingletonMgr.Instance.BattleMgr.PlayerActor.SetSpineModelDirection(shootDir.x > 0 ? -1 : 1);
            }

            // 调用基类的DoShoot方法
            base.DoShoot(token).Forget();
        }

        /// <inheritdoc />
        protected override void OnAfterShoot()
        {
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayerMove.MoveDuration = 0;
        }

        /// <inheritdoc />
        public override void ClearShooter()
        {
            // 从所有怪物的 被锁列表 中移除该执行者
            _ = SingletonMgr.Instance.BattleMgr.Monsters.Select(enemy =>
            {
                enemy.LockedByShooters.Remove(this);
                return enemy;
            }).ToList();
        }
    }
}