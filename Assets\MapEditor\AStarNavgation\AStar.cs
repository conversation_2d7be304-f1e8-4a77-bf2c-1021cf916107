using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// A星算法类
/// </summary>
public class AStar
{
    private static AStar _instance;
    public static AStar Instance
    {
        get
        {
            if (_instance != null) return _instance;
            _instance = new AStar();
            return _instance;
        }
    }

    public int[,] Data = new int[10, 10]
    {
       {0,0,0,0,0,0,0,1,1,0},
       {0,0,0,0,1,1,1,1,1,0},
       {0,0,0,0,1,1,1,0,0,0},
       {0,0,0,0,0,0,1,0,0,0},
       {0,0,0,0,0,0,1,0,0,0},
       {0,0,0,1,0,0,1,0,0,0},
       {0,1,1,1,1,1,1,1,0,0},
       {0,1,1,1,1,1,1,1,1,0},
       {0,0,0,0,0,0,0,0,1,0},
       {0,0,0,1,1,1,1,1,1,0},
    };
    
    public class Vertex
    {
        public int x, y;//顶点的X,Y坐标
        public Vertex parent = null;//父节点（前驱节点）,默认无父节点
        public int F, G, H; //F = G +H，F为寻路消耗
        public int ListFlag = 0;//0是没找过的点，1是在开启表的点，2是在关闭表的点
        public Vertex(int x, int y)
        {
            this.x = x;
            this.y = y;
        }
        public Vertex()
        {

        }
    }

    /// <summary>
    /// 开启表
    /// </summary>
    private List<Vertex> openList;
    /// <summary>
    /// 关闭表
    /// </summary>
    private List<Vertex> closeList;
    //8星遍历（包括斜角点）
    //private int[,] offsetList = new int[8,2] 
    //{
    //    {-1,-1 },{-1,0 },{-1,1 },
    //    {0,-1 },        {0,1 },
    //    {1,-1 },{1,0 },{1,1 }
    //};
    //4星遍历则不包括斜角点
    private int[,] offsetList = new int[4, 2]
    {
                {-1,0 },
        {0,-1 },        {0,1 },
                {1,0 }
    };
    /// <summary>
    /// 所有顶点集合
    /// </summary>
    private Vertex[,] allVertex;
    /// <summary>
    /// 终点
    /// </summary>
    private Vertex endVertex;
    /// <summary>
    /// 路线点集合
    /// </summary>
    List<Vertex> AStarway;

    /// <summary>
    /// 更高效获取A星路线方法，先尝试做直线检测，碰到障碍格子再以直线检测最后一个非障碍格子为起点做A星检测
    /// </summary>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <param name="MapData"></param>
    /// <returns></returns>
    public List<Vertex> GetAStarWayBetter(Vertex start, Vertex end, int[,] MapData)
    {
        AStarway = new List<Vertex>();
        Vector2 dir = new Vector2(end.x, end.y) - new Vector2(start.x, start.y);
        Vertex curVertex = start;
        for (int i = 1; i < dir.magnitude + 1; i++)
        {
            Vertex nextVertex = new Vertex(Mathf.FloorToInt(start.x + dir.normalized.x*i), Mathf.FloorToInt(start.y + dir.normalized.y*i));
            // 添加边界检查，防止索引越界
            if (nextVertex.x < 0 || nextVertex.y < 0 || nextVertex.x >= MapData.GetLength(0) || nextVertex.y >= MapData.GetLength(1))
            {
                // 越界情况下，以当前位置为起点进行A星寻路
                AStarway = GetAStarWay(curVertex, end, MapData);
                break;
            }
            
            if (nextVertex.x != end.x || nextVertex.y != end.y)//不是终点格子
            {
                if (MapData[nextVertex.x, nextVertex.y] == 1)
                {
                    curVertex = nextVertex;
                }
                else
                {
                    //碰到障碍格子,以直线检测最后一个非障碍格子为起点做A星检测
                    AStarway = GetAStarWay(curVertex,end,MapData);
                    break;
                }
            }
            else
            {
                if (HasBlockAroundCell(nextVertex, MapData))
                {
                    //终点周边有障碍格子,以直线检测最后一个非障碍格子为起点做A星检测，实现拐角移动
                    AStarway = GetAStarWay(curVertex, end, MapData);
                    break;
                }
                else
                {
                    AStarway = new List<Vertex>() { end };//直线方向能检测到终点，说明可以直走，直接返回终点信息
                }
            }
        }

        //for (int i = 0; i < AStarway.Count; i++)
        //{
        //    Debug.Log(AStarway[i].x + " + " + AStarway[i].y);
        //}
        if(AStarway.Count == 0)
        {
            AStarway.Add(curVertex);
        }
        return AStarway;
    }
    /// <summary>
    /// 获取A星最短路线
    /// </summary>
    /// <param name="start">起点</param>
    /// <param name="end">终点</param>
    /// <param name="MapData">地图块行走逻辑数据，0是障碍块，1是可行走块</param>
    /// <returns>路线点集合</returns>
    public List<Vertex> GetAStarWay(Vertex start,Vertex end,int[,]MapData)
    {
        openList = new List<Vertex>();
        closeList = new List<Vertex>();
        AStarway = new List<Vertex>();
        endVertex = end;
        InitAllVertex(MapData);
        CalculateAStarWayByVertex(start,MapData);
        AStarway.Reverse();//因为是移父节点往回找，所以拿到的路径列表是倒序的，要倒回来
        
        //for (int i = 0; i < AStarway.Count; i++)
        //{
        //    Debug.Log(AStarway[i].x + " + " + AStarway[i].y);
        //}

        return AStarway;
    }

    /// <summary>
    /// 初始化所有顶点的列表
    /// </summary>
    /// <param name="mapData"></param>
    private void InitAllVertex(int[,] mapData)
    {
        allVertex = new Vertex[mapData.GetLength(0),mapData.GetLength(1)];
        for (int i = 0; i < mapData.GetLength(0); i++)
        {
            for (int j = 0; j < mapData.GetLength(1); j++)
            {
                Vertex vertex = new Vertex(i,j);
                allVertex[i, j] = vertex;
            }
        }
    }

    /// <summary>
    /// 根据传入起点信息，递归遍历各个点，通过开启表和关闭表筛选出最短路线点集合
    /// </summary>
    /// <param name="vertex"></param>
    /// <param name="mapData"></param>
    private void CalculateAStarWayByVertex(Vertex vertex,int[,]mapData)
    {
        for (int i = 0; i < offsetList.GetLength(0); i++)
        {           
            int[] offsetVertex = { vertex.x + offsetList[i,0], vertex.y + offsetList[i,1] };
            //Debug.Log(offsetVertex[0] + "  " + offsetVertex[1] +"  "+ (mapData.GetLength(0) - 1) +"  "+ mapData.GetLength(1));
            if (offsetVertex[0] < 0 || offsetVertex[0] > mapData.GetLength(0)-1 || offsetVertex[1] < 0 || offsetVertex[1] > mapData.GetLength(1) - 1)
            {
                int a = mapData.GetLength(0) - 1;
                int b = mapData.GetLength(1) - 1;
                //下标越界,找下一个点
                continue;
            }

            if (mapData[offsetVertex[0],offsetVertex[1]] == 0)
            {
                //障碍点，找下一个点
                continue;
            }

            if (allVertex[offsetVertex[0], offsetVertex[1]].ListFlag != 0)
            {
                //已经找过的点
                continue;
            }

            Vertex getVertex = allVertex[offsetVertex[0], offsetVertex[1]];
            getVertex.parent = vertex;
            //斜对角的+14,临边+10
            getVertex.G =(offsetList[i, 0] != 0 && offsetList[i, 1] != 0)? vertex.G + 14 : vertex.G + 10;
            getVertex.H = Mathf.Abs(getVertex.x - endVertex.x)*10 + Mathf.Abs(getVertex.y - endVertex.y)*10;
            getVertex.F = getVertex.G + getVertex.H;
            openList.Add(getVertex);//放入开启表
            allVertex[offsetVertex[0], offsetVertex[1]].ListFlag = 1;
        }

        int indexMin = 0;
        for (int i = 1; i < openList.Count; i++)
        {
            if (openList[indexMin].F > openList[i].F)
            {
                indexMin = i;
            }
        }
        Vertex minVertex = openList[indexMin];
        //放入关闭表
        closeList.Add(openList[indexMin]);
        openList.RemoveAt(indexMin);

        if (minVertex.x != endVertex.x || minVertex.y != endVertex.y)
        {
            //没找到终点，则以下一个寻路消耗最小点递归遍历
            CalculateAStarWayByVertex(minVertex,mapData);
        }
        else
        {
            //找到了终点，从终点开始，以关联父节点回溯出最短路线所有点
            AStarway = new List<Vertex>();
            Vertex v = minVertex;
            while (v.parent != null)
            {
                AStarway.Add(v);
                v = v.parent;
            }
        }
    }

    /// <summary>
    /// 检测一个单元上下左右是否有障碍单元
    /// </summary>
    /// <param name="vertex"></param>
    /// <param name="mapData"></param>
    /// <returns></returns>
    private bool HasBlockAroundCell(Vertex vertex, int[,] mapData)
    {
        for (int i = 0; i < offsetList.GetLength(0); i++)
        {
            int[] offsetVertex = { vertex.x + offsetList[i, 0], vertex.y + offsetList[i, 1] };
            if (offsetVertex[0] < 0 || offsetVertex[0] > mapData.GetLength(0) - 1 || offsetVertex[1] < 0 || offsetVertex[1] > mapData.GetLength(1) - 1)
            {
                //下标越界,找下一个点
                continue;
            }

            if (mapData[offsetVertex[0], offsetVertex[1]] == 0)
            {
                //障碍点
                return true;
            }
        }
        return false;
    }
}

