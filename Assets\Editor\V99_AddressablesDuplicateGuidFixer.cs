using UnityEngine;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using System.Collections.Generic;
using System.Linq;
using System.IO;

namespace V99AddressableFixer
{
    public class V99_AddressablesDuplicateGuidFixer : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<DuplicateGuidInfo> duplicateGuids = new List<DuplicateGuidInfo>();
        
        [System.Serializable]
        public class DuplicateGuidInfo
        {
            public string problemGuid;
            public List<AssetInfo> conflictingAssets = new List<AssetInfo>();
            
            public DuplicateGuidInfo(string guid)
            {
                this.problemGuid = guid;
            }
        }
        
        [System.Serializable]
        public class AssetInfo
        {
            public string groupName;
            public string address;
            public string realGuid;
            public string assetPath;
            
            public AssetInfo(string groupName, string address, string realGuid, string assetPath)
            {
                this.groupName = groupName;
                this.address = address;
                this.realGuid = realGuid;
                this.assetPath = assetPath;
            }
        }

        [MenuItem("Tools/V99.0 Addressables重复GUID修复工具")]
        public static void ShowWindow()
        {
            var window = GetWindow<V99_AddressablesDuplicateGuidFixer>("V99.0 重复GUID修复");
            window.minSize = new Vector2(700, 500);
            window.ScanForDuplicateGuids();
        }

        void OnGUI()
        {
            EditorGUILayout.BeginVertical();
            
            GUILayout.Label("V99.0 Addressables重复GUID修复工具", EditorStyles.boldLabel);
            GUILayout.Space(10);

            EditorGUILayout.HelpBox("这个工具专门修复错误:\n\"ArgumentException: An item with the same key has already been added\"", MessageType.Info);
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("🔍 扫描重复GUID问题", GUILayout.Height(30)))
            {
                ScanForDuplicateGuids();
            }

            GUILayout.Space(10);

            if (duplicateGuids.Count > 0)
            {
                EditorGUILayout.HelpBox($"⚠️ 发现 {duplicateGuids.Count} 个重复GUID问题", MessageType.Warning);
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                
                foreach (var duplicate in duplicateGuids)
                {
                    DrawDuplicateGuidInfo(duplicate);
                }
                
                EditorGUILayout.EndScrollView();
                
                GUILayout.Space(10);
                
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("🔧 修复所有问题", GUILayout.Height(35)))
                {
                    FixAllDuplicateGuids();
                }
                
                if (GUILayout.Button("🧹 清理缓存", GUILayout.Height(35)))
                {
                    ClearAddressablesCache();
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("✅ 未发现重复GUID问题", MessageType.Info);
            }

            GUILayout.Space(20);
            DrawInstructions();
            
            EditorGUILayout.EndVertical();
        }

        void DrawDuplicateGuidInfo(DuplicateGuidInfo duplicate)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("❌ 问题GUID:", duplicate.problemGuid, EditorStyles.boldLabel);
            
            EditorGUILayout.LabelField($"冲突资源数量: {duplicate.conflictingAssets.Count}");
            
            for (int i = 0; i < duplicate.conflictingAssets.Count; i++)
            {
                var asset = duplicate.conflictingAssets[i];
                
                EditorGUILayout.BeginVertical("helpbox");
                EditorGUILayout.LabelField($"组: {asset.groupName}");
                EditorGUILayout.LabelField($"地址: {asset.address}");
                EditorGUILayout.LabelField($"资源路径: {asset.assetPath}");
                EditorGUILayout.LabelField($"正确GUID: {asset.realGuid}");
                
                if (GUILayout.Button($"🔧 修复这个条目", GUILayout.Height(25)))
                {
                    FixSingleEntry(duplicate, i);
                }
                EditorGUILayout.EndVertical();
                
                if (i < duplicate.conflictingAssets.Count - 1)
                {
                    GUILayout.Space(5);
                }
            }
            
            EditorGUILayout.EndVertical();
            GUILayout.Space(10);
        }

        void DrawInstructions()
        {
            EditorGUILayout.LabelField("📋 使用说明:", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox(
                "1. 点击'扫描重复GUID问题'检测所有问题\n" +
                "2. 每个问题显示冲突的资源信息\n" +
                "3. 可以单独修复每个条目，或批量修复所有问题\n" +
                "4. 修复完成后建议重启Unity编辑器\n" +
                "5. 如果问题持续存在，点击'清理缓存'", 
                MessageType.None);
        }

        void ScanForDuplicateGuids()
        {
            Debug.Log("=== V99.0 开始扫描Addressables重复GUID问题 ===");
            duplicateGuids.Clear();
            
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
            {
                EditorUtility.DisplayDialog("错误", "无法找到Addressables设置", "确定");
                return;
            }

            Dictionary<string, List<(AddressableAssetGroup group, AddressableAssetEntry entry)>> guidUsage = 
                new Dictionary<string, List<(AddressableAssetGroup, AddressableAssetEntry)>>();

            // 收集所有GUID使用情况
            foreach (var group in settings.groups)
            {
                if (group == null) continue;
                
                foreach (var entry in group.entries)
                {
                    if (!guidUsage.ContainsKey(entry.guid))
                    {
                        guidUsage[entry.guid] = new List<(AddressableAssetGroup, AddressableAssetEntry)>();
                    }
                    guidUsage[entry.guid].Add((group, entry));
                }
            }

            // 查找重复使用的GUID
            foreach (var kvp in guidUsage)
            {
                if (kvp.Value.Count > 1)
                {
                    var duplicateInfo = new DuplicateGuidInfo(kvp.Key);
                    
                    foreach (var (group, entry) in kvp.Value)
                    {
                        // 通过地址推断真实资源路径
                        string realAssetPath = entry.address;
                        if (!realAssetPath.StartsWith("Assets/"))
                        {
                            realAssetPath = "Assets/" + realAssetPath;
                        }
                        
                        // 尝试找到真实的GUID
                        string realGuid = kvp.Key;
                        if (File.Exists(realAssetPath))
                        {
                            realGuid = AssetDatabase.AssetPathToGUID(realAssetPath);
                        }
                        else
                        {
                            // 如果直接路径不存在，尝试搜索文件名
                            string fileName = Path.GetFileNameWithoutExtension(realAssetPath);
                            string[] foundGuids = AssetDatabase.FindAssets(fileName);
                            
                            foreach (string foundGuid in foundGuids)
                            {
                                string foundPath = AssetDatabase.GUIDToAssetPath(foundGuid);
                                if (foundPath.Contains(fileName))
                                {
                                    realGuid = foundGuid;
                                    realAssetPath = foundPath;
                                    break;
                                }
                            }
                        }
                        
                        var assetInfo = new AssetInfo(group.name, entry.address, realGuid, realAssetPath);
                        duplicateInfo.conflictingAssets.Add(assetInfo);
                    }
                    
                    duplicateGuids.Add(duplicateInfo);
                    Debug.LogWarning($"V99.0 发现重复GUID: {kvp.Key}，被 {kvp.Value.Count} 个资源使用");
                }
            }

            Debug.Log($"=== V99.0 扫描完成，发现 {duplicateGuids.Count} 个重复GUID问题 ===");
            Repaint();
        }

        void FixSingleEntry(DuplicateGuidInfo duplicate, int assetIndex)
        {
            var asset = duplicate.conflictingAssets[assetIndex];
            Debug.Log($"=== V99.0 修复单个条目: {asset.groupName} - {asset.address} ===");
            
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            var group = settings.FindGroup(asset.groupName);
            
            if (group != null)
            {
                var entry = group.entries.FirstOrDefault(e => e.guid == duplicate.problemGuid && e.address == asset.address);
                if (entry != null)
                {
                    if (asset.realGuid != duplicate.problemGuid)
                    {
                        // 移除错误的条目
                        group.RemoveAssetEntry(entry);
                        
                        // 创建正确的条目
                        var newEntry = settings.CreateOrMoveEntry(asset.realGuid, group);
                        newEntry.address = asset.address;
                        
                        Debug.Log($"V99.0 已修复: {asset.address} -> 正确GUID: {asset.realGuid}");
                    }
                    else
                    {
                        Debug.Log($"V99.0 条目GUID已正确: {asset.address}");
                    }
                }
            }
            
            // 保存设置
            EditorUtility.SetDirty(settings);
            AssetDatabase.SaveAssets();
            
            // 重新扫描
            ScanForDuplicateGuids();
        }

        void FixAllDuplicateGuids()
        {
            Debug.Log("=== V99.0 开始修复所有重复GUID问题 ===");
            
            int fixedCount = 0;
            foreach (var duplicate in duplicateGuids.ToList())
            {
                for (int i = 0; i < duplicate.conflictingAssets.Count; i++)
                {
                    FixSingleEntry(duplicate, i);
                    fixedCount++;
                }
            }
            
            Debug.Log($"=== V99.0 修复完成，共修复 {fixedCount} 个条目 ===");
            EditorUtility.DisplayDialog("修复完成", 
                $"成功修复 {fixedCount} 个重复GUID问题\n\n请重新启动Unity编辑器确保完全生效", "确定");
        }

        void ClearAddressablesCache()
        {
            Debug.Log("=== V99.0 清理Addressables缓存 ===");
            
            try
            {
                // 清理Addressables相关缓存
                string[] cachePaths = {
                    Path.Combine(Application.dataPath, "../Library/com.unity.addressables"),
                    Path.Combine(Application.dataPath, "../Library/ArtifactDB"),
                    Path.Combine(Application.dataPath, "../Library/SourceAssetDB"),
                    Path.Combine(Application.dataPath, "../AddressableAssetsData/Android"),
                    Path.Combine(Application.dataPath, "../AddressableAssetsData/WebGL")
                };

                foreach (string cachePath in cachePaths)
                {
                    if (Directory.Exists(cachePath))
                    {
                        Directory.Delete(cachePath, true);
                        Debug.Log($"V99.0 已删除缓存目录: {cachePath}");
                    }
                    else if (File.Exists(cachePath))
                    {
                        File.Delete(cachePath);
                        Debug.Log($"V99.0 已删除缓存文件: {cachePath}");
                    }
                }

                AssetDatabase.Refresh();
                Debug.Log("=== V99.0 缓存清理完成 ===");
                EditorUtility.DisplayDialog("缓存清理完成", "Addressables缓存已清理\n建议重新启动Unity编辑器", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"V99.0 清理缓存失败: {e.Message}");
                EditorUtility.DisplayDialog("清理失败", $"清理缓存时出错:\n{e.Message}", "确定");
            }
        }
    }
} 