﻿using System.Collections.Generic;

using JsonStructure;

using Newtonsoft.Json;

/// <summary>
/// 对接Lua的DataService服务接口
/// </summary>
public class LuaDataSrvClient : Singleton<LuaDataSrvClient>
{
	protected override void InitializeSingleton()
	{
		DontDestroyOnLoad(Instance);
	}

	public long GetActorID()
	{
		var rtn = LuaManager.Instance.LuaState_.Invoke<int>(
			"DataService.GetActorID", true);
		return rtn;
	}

	public string GetActorName()
	{
		var rtn = LuaManager.Instance.LuaState_.Invoke<string>(
			"DataService.GetRoleName", true);
		return rtn;
	}

	/// <summary>
	/// 获取角色属性
	/// </summary>
	public ActorProp GetPlayerProp()
	{
		var json = LuaManager.Instance.LuaState_.Invoke<string>(
			"DataService.GetPlayerProp", true);
		return JsonConvert.DeserializeObject<ActorProp>(json);
	}

	/// <summary>
	/// 读取角色的装备属性
	/// </summary>
	public EquipProps ReadEquipProp(int goodsId)
	{
		var json = LuaManager.Instance.LuaState_.Invoke<int, string>(
			"DataService.ReadEquipProp", goodsId, true);
		return JsonConvert.DeserializeObject<EquipProps>(json);
	}

	/// <summary>
	/// 获取进入的关卡类型
	/// </summary>
	public int GetStageType()
	{
		return LuaManager.Instance.LuaState_.Invoke<int>(
			"DataService.GetStageType", true);
	}

	/// <summary>
	/// 获取进入的关卡Id
	/// </summary>
	public int GetStageId()
	{
		return LuaManager.Instance.LuaState_.Invoke<int>(
			"DataService.GetStageId", true);
	}

	/// <summary>
	/// 获取出战的宠物列表(物品ID)
	/// </summary>
	public List<int> GetPetsOnStage()
	{
		var json = LuaManager.Instance.LuaState_.Invoke<string>(
			"DataService.GetPetsOnStage", true);
		return JsonConvert.DeserializeObject<List<int>>(json);
	}

	/// <summary>
	/// 读取装备的属性配置
	/// </summary>
	/// <param name="goodsID">物品ID</param>
	/// <param name="starCount">星量</param>
	/// <returns>装备属性</returns>
	public CreaturePropBase ReadStarProp(int goodsID, int starCount)
	{
		var json = LuaManager.Instance.LuaState_.Invoke<int, int, string>(
			"DataService.ReadStarProp", goodsID, starCount, true);
		return JsonConvert.DeserializeObject<CreaturePropBase>(json);
	}

	/// <summary>
	/// 按物品ID读取角色的武器属性
	/// </summary>
	/// <param name="goodsID">物品ID</param>
	/// <param name="needWear">需要佩戴(出战)时,如果角色没有佩戴指定的武器,返回null</param>
	/// <returns>武器属性</returns>
	public EquipProps GetWeaponProp(int goodsID, bool needWear = false)
	{
		var json = LuaManager.Instance.LuaState_.Invoke<int, bool, string>(
			"DataService.GetWeaponProp", goodsID, needWear, true);
		return JsonConvert.DeserializeObject<EquipProps>(json);
	}

	/// <summary>
	/// 获取角色的武器列表
	/// </summary>
	/// <param name="onlyWear">仅佩戴(出战)的,否则所有拥有的武器</param>
	/// <returns>武器的装备属性列表</returns>
	public List<EquipProps> GetWeapons(bool onlyWear)
	{
		var json = LuaManager.Instance.LuaState_.Invoke<bool, string>(
			"DataService.GetWeapons", onlyWear, true);
		return JsonConvert.DeserializeObject<List<EquipProps>>(json);
	}

	/// <summary>
	/// 获取角色主线进度(通过的最大关卡)
	/// </summary>
	public int GetMaxMainStage()
	{
		return LuaManager.Instance.LuaState_.Invoke<int>(
			"DataService.GetMaxMainStage", true);
	}

	public int GetCurEquipID()
	{
		return LuaManager.Instance.LuaState_.Invoke<int>(
			"DataService.GetCurEquipID", true);
	}

	/// <summary>
	/// 获取Web应用根Url
	/// </summary>
	/// <param name="key">SrvUrlRoot的成员名称</param>
	/// <returns>Web应用根Url</returns>
	public string GetSrvUrlRoot(string key)
	{
		return LuaManager.Instance.LuaState_.Invoke<string, string>(
			"DataService.GetSrvUrlRoot", key, true);
	}

    public int GetEquipStarExp(int equipID)
    {
		return LuaManager.Instance.InvokeLuaFunction<int,int>("DataService.GetEquipStarExp", equipID);
    }

    public int GetAdRefreshCount(int adId)
    {
        return LuaManager.Instance.InvokeLuaFunction<int, int>("DataService.GetAdRefreshCount", adId);
    }

    public int GetAdRefreshAllCount(int adId)
    {
        return LuaManager.Instance.InvokeLuaFunction<int, int>("DataService.GetAdRefreshAllCount", adId);
    }

    /// <summary>
	/// 获取玩家拥有物品数量
	/// </summary>
	/// <param name="goodsId"></param>
	/// <returns></returns>
    public int GetGoodsCount(int goodsId)
    {
        return LuaManager.Instance.InvokeLuaFunction<int, int>("DataService.GetGoodsCount", goodsId);
    }

    /// <summary>
    /// 消耗物品
    /// </summary>
    /// <param name="goodInfo"></param>
    /// <param name="costInfo"></param>
    public void RequestDirectGiveGoodsy(string goodInfo, string costInfo)
    {
        LuaManager.Instance.RunLuaFunction<string, string>("DataService.RequestDirectGiveGoodsy", goodInfo, costInfo);
    }
}
