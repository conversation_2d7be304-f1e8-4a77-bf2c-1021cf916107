using UnityEngine;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using System.Collections.Generic;
using System.Linq;
using System.IO;

namespace AddressableFixTools
{
    public class FixAddressablesDuplicateKeys : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<DuplicateEntry> duplicateEntries = new List<DuplicateEntry>();
        
        [System.Serializable]
        public class DuplicateEntry
        {
            public string guid;
            public List<string> groupNames = new List<string>();
            public List<string> addresses = new List<string>();
            public string realGuid;
            public string assetPath;
            
            public DuplicateEntry(string guid)
            {
                this.guid = guid;
            }
        }

        [MenuItem("Tools/V99.0 修复Addressables重复GUID问题")]
        public static void ShowWindow()
        {
            var window = GetWindow<FixAddressablesDuplicateKeys>("V99.0 修复Addressables重复GUID");
            window.minSize = new Vector2(600, 400);
            window.ScanForDuplicates();
        }

        void OnGUI()
        {
            GUILayout.Label("Addressables重复GUID检测和修复工具", EditorStyles.boldLabel);
            GUILayout.Space(10);

            if (GUILayout.Button("重新扫描重复GUID", GUILayout.Height(30)))
            {
                ScanForDuplicates();
            }

            GUILayout.Space(10);

            if (duplicateEntries.Count > 0)
            {
                EditorGUILayout.HelpBox($"发现 {duplicateEntries.Count} 个重复GUID问题", MessageType.Warning);
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                
                foreach (var duplicate in duplicateEntries)
                {
                    EditorGUILayout.BeginVertical("box");
                    
                    // 显示问题GUID
                    EditorGUILayout.LabelField("问题GUID:", duplicate.guid, EditorStyles.boldLabel);
                    
                    // 显示资源路径
                    if (!string.IsNullOrEmpty(duplicate.assetPath))
                    {
                        EditorGUILayout.LabelField("真实资源路径:", duplicate.assetPath);
                        EditorGUILayout.LabelField("真实GUID:", duplicate.realGuid);
                    }
                    
                    // 显示所有使用这个GUID的组
                    EditorGUILayout.LabelField("出现在以下组中:");
                    for (int i = 0; i < duplicate.groupNames.Count; i++)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField($"  • {duplicate.groupNames[i]}: {duplicate.addresses[i]}");
                        if (GUILayout.Button("修复", GUILayout.Width(60)))
                        {
                            FixSpecificEntry(duplicate, i);
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                    
                    EditorGUILayout.Space(5);
                    if (GUILayout.Button($"修复所有使用 {duplicate.guid} 的条目", GUILayout.Height(25)))
                    {
                        FixAllEntriesForGuid(duplicate);
                    }
                    
                    EditorGUILayout.EndVertical();
                    EditorGUILayout.Space(10);
                }
                
                EditorGUILayout.EndScrollView();
                
                EditorGUILayout.Space(10);
                if (GUILayout.Button("修复所有重复GUID问题", GUILayout.Height(40)))
                {
                    FixAllDuplicates();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("未发现重复GUID问题", MessageType.Info);
            }

            GUILayout.Space(20);
            EditorGUILayout.LabelField("使用说明:", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("1. 点击'重新扫描'检测重复GUID\n2. 对于每个重复项，可以单独修复或批量修复\n3. 修复后重新启动Unity确保生效", MessageType.None);
        }

        void ScanForDuplicates()
        {
            Debug.Log("=== V99.0 开始扫描Addressables重复GUID问题 ===");
            duplicateEntries.Clear();
            
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
            {
                Debug.LogError("无法找到Addressables设置");
                return;
            }

            Dictionary<string, List<(AddressableAssetGroup group, AddressableAssetEntry entry)>> guidMap = 
                new Dictionary<string, List<(AddressableAssetGroup, AddressableAssetEntry)>>();

            // 收集所有条目
            foreach (var group in settings.groups)
            {
                if (group == null) continue;
                
                foreach (var entry in group.entries)
                {
                    if (!guidMap.ContainsKey(entry.guid))
                    {
                        guidMap[entry.guid] = new List<(AddressableAssetGroup, AddressableAssetEntry)>();
                    }
                    guidMap[entry.guid].Add((group, entry));
                }
            }

            // 查找重复项
            foreach (var kvp in guidMap)
            {
                if (kvp.Value.Count > 1)
                {
                    var duplicate = new DuplicateEntry(kvp.Key);
                    
                    // 获取真实的资源信息
                    string assetPath = AssetDatabase.GUIDToAssetPath(kvp.Key);
                    if (!string.IsNullOrEmpty(assetPath))
                    {
                        duplicate.assetPath = assetPath;
                        duplicate.realGuid = kvp.Key;
                    }
                    
                    foreach (var (group, entry) in kvp.Value)
                    {
                        duplicate.groupNames.Add(group.name);
                        duplicate.addresses.Add(entry.address);
                    }
                    
                    duplicateEntries.Add(duplicate);
                    Debug.LogWarning($"V99.0 发现重复GUID: {kvp.Key}，出现在 {kvp.Value.Count} 个条目中");
                }
            }

            Debug.Log($"=== V99.0 扫描完成，发现 {duplicateEntries.Count} 个重复GUID问题 ===");
            Repaint();
        }

        void FixSpecificEntry(DuplicateEntry duplicate, int entryIndex)
        {
            Debug.Log($"=== V99.0 修复特定条目: {duplicate.groupNames[entryIndex]} - {duplicate.addresses[entryIndex]} ===");
            
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            var group = settings.FindGroup(duplicate.groupNames[entryIndex]);
            
            if (group != null)
            {
                var entry = group.entries.FirstOrDefault(e => e.guid == duplicate.guid && e.address == duplicate.addresses[entryIndex]);
                if (entry != null)
                {
                    // 检查资源是否真的存在
                    string realPath = AssetDatabase.GUIDToAssetPath(duplicate.guid);
                    if (string.IsNullOrEmpty(realPath))
                    {
                        // GUID对应的资源不存在，尝试通过地址找到正确的资源
                        string[] guids = AssetDatabase.FindAssets(Path.GetFileNameWithoutExtension(duplicate.addresses[entryIndex]));
                        string correctGuid = null;
                        
                        foreach (string guid in guids)
                        {
                            string path = AssetDatabase.GUIDToAssetPath(guid);
                            if (path.EndsWith(duplicate.addresses[entryIndex].Replace("Assets/", "")))
                            {
                                correctGuid = guid;
                                break;
                            }
                        }
                        
                        if (!string.IsNullOrEmpty(correctGuid))
                        {
                            Debug.Log($"V99.0 找到正确的GUID: {correctGuid} for {duplicate.addresses[entryIndex]}");
                            
                            // 移除旧条目
                            group.RemoveAssetEntry(entry);
                            
                            // 添加新条目
                            var newEntry = settings.CreateOrMoveEntry(correctGuid, group);
                            newEntry.address = duplicate.addresses[entryIndex];
                            
                            Debug.Log($"V99.0 已修复条目: {duplicate.addresses[entryIndex]}");
                        }
                        else
                        {
                            Debug.LogError($"V99.0 无法找到资源的正确GUID: {duplicate.addresses[entryIndex]}");
                        }
                    }
                }
            }
            
            // 保存设置
            EditorUtility.SetDirty(settings);
            AssetDatabase.SaveAssets();
            
            // 重新扫描
            ScanForDuplicates();
        }

        void FixAllEntriesForGuid(DuplicateEntry duplicate)
        {
            Debug.Log($"=== V99.0 修复GUID {duplicate.guid} 的所有条目 ===");
            
            for (int i = 0; i < duplicate.groupNames.Count; i++)
            {
                FixSpecificEntry(duplicate, i);
            }
        }

        void FixAllDuplicates()
        {
            Debug.Log("=== V99.0 开始修复所有重复GUID问题 ===");
            
            foreach (var duplicate in duplicateEntries.ToList())
            {
                FixAllEntriesForGuid(duplicate);
            }
            
            Debug.Log("=== V99.0 所有重复GUID问题修复完成 ===");
            EditorUtility.DisplayDialog("修复完成", "所有重复GUID问题已修复\n请重新启动Unity编辑器以确保完全生效", "确定");
        }
    }
} 