﻿// ReSharper disable IdentifierTypo

using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using Spine.Unity;

using Thing;

using UnityEngine;

namespace View
{
	/// <summary>
	/// 生物界面(玩家、怪物、宠物)
	/// </summary>
	public abstract class CreatureBase : ThingBehaviour
	{
		/// <summary>
		/// 生物物件(数据)
		/// </summary>
		public CreatureThing CreatureThing => Thing as CreatureThing;

		/// <summary>
		/// 生物的刚体
		/// </summary>
		public Rigidbody2D Rigidbody { get; set; }

		public override void Awake()
		{
            base.Awake();
            
			Rigidbody = gameObject.GetOrAddComponent<Rigidbody2D>();
			Rigidbody.isKinematic = true;
			Rigidbody.gravityScale = 0;
		}

        /// <inheritdoc />
        public override void DoSyncToThing()
        {
            base.DoSyncToThing();

            #region 覆盖区域

            if (CreatureThing != null)
            {
                var circularArea2D = new CircularArea2D
                {
                    Center = transform.position, Radius = CreatureThing.TotalProp_Radius,
                };

                CreatureThing.Area2D.Replace(circularArea2D);
            }

            #endregion
        }

        /// <summary>
        /// 生物的骨骼动画
        /// </summary>
        private SkeletonAnimation SkeAni { get; set; }

        /// <summary>
        /// 播放动画
        /// </summary>
        /// <param name="aniName">动画名字</param>
        /// <param name="loop">是否循环</param>
        /// <param name="isBackToIdle">当前动画结束是否要回到idle状态</param>
        // 添加攻击冷却时间变量
        private float lastAttackTime = -10f;
        private const float AttackCooldown = 1f; // 1秒攻击冷却，比1111项目短
        
        public void PlayAnimation(string aniName, bool loop = false, bool isBackToIdle = true)
        {
            Debug.Log($"V30.2 调用PlayAnimation: aniName={aniName}, loop={loop}, isBackToIdle={isBackToIdle}");
            
            if (SkeAni == null) 
            {
                SkeAni = GetComponentInChildren<SkeletonAnimation>();
            }
            
            if (SkeAni == null || SkeAni.state == null) 
            {
                return;
            }
            
            if (SkeAni.skeleton.Data.FindAnimation(aniName) == null) 
            {
                return;
            }
            
            // 记录攻击时间
            if (aniName == "attack01")
            {
                lastAttackTime = Time.time;
                Debug.Log($"V30.2 攻击动画开始，记录时间: {lastAttackTime}");
            }

            SkeAni.state.SetAnimation(0, aniName, loop);

            // 攻击动画结束后的智能处理
            if (isBackToIdle && !loop && aniName == "attack01")
            {
                Debug.Log("V30.2 攻击动画结束，智能判断下一个动画");
                
                // V30.2-动画系统修复 根据实际状态决定攻击后播放什么动画
                var playerActor = this as PlayerActor;
                if (playerActor != null && playerActor.PlayerMove != null && playerActor.PlayerMove.IsMoving)
                {
                    // 如果角色正在移动，攻击后继续播放移动动画
                    if (SkeAni.skeleton.Data.FindAnimation("move01") != null)
                    {
                        SkeAni.state.AddAnimation(0, "move01", true, 0);
                        Debug.Log("V30.2 角色正在移动，攻击后播放移动动画 move01");
                    }
                }
                else
                {
                    // 如果角色不在移动，攻击后播放待机动画
                    if (SkeAni.skeleton.Data.FindAnimation("idle01") != null)
                    {
                        SkeAni.state.AddAnimation(0, "idle01", true, 0);
                        Debug.Log("V30.2 角色不在移动，攻击后播放待机动画 idle01");
                    }
                }
                
                return; // 跳过后续处理
            }
            
            // 非攻击动画的智能链接处理
            if (isBackToIdle && !loop && aniName != "attack01")
            {
                // 优先判断是否应该播放移动动画
                var playerActor = this as PlayerActor;
                if (playerActor != null && playerActor.PlayerMove != null && playerActor.PlayerMove.IsMoving)
                {
                    if (SkeAni.skeleton.Data.FindAnimation("move01") != null)
                    {
                        SkeAni.state.AddAnimation(0, "move01", true, 0);
                        Debug.Log("V30.2 动画结束后，角色在移动，播放move01");
                    }
                }
                else
                {
                    if (SkeAni.skeleton.Data.FindAnimation("idle01") != null)
                    {
                        SkeAni.state.AddAnimation(0, "idle01", true, 0);
                        Debug.Log("V30.2 动画结束后，角色不在移动，播放idle01");
                    }
                }
            }
        }

        /// <summary>
        /// 获取动画时长
        /// </summary>
        /// <param name="aniName">动画名称</param>
        /// <returns>动画时长（秒）</returns>
        public float GetAnimationDuration(string aniName)
        {
            float duration = 0f;
            if (SkeAni == null) 
            {
                SkeAni = GetComponentInChildren<SkeletonAnimation>();
            }
            
            if (SkeAni == null || SkeAni.state == null) 
            {
                return duration;
            }
            
            Spine.Animation animation = SkeAni.skeleton.Data.FindAnimation(aniName);
            if (animation != null)
            {
                duration = animation.Duration;
            }
            
            return duration;
        }

        /// <summary>
        /// 设置Spine动画层级
        /// </summary>
        public void SetSpineSortingOrderBySelfPosition()
        {
            if (SkeAni == null) 
            {
                SkeAni = GetComponentInChildren<SkeletonAnimation>();
            }
            
            if (SkeAni != null)
            {
                var meshRenderer = SkeAni.GetComponent<MeshRenderer>();
                if (meshRenderer != null)
                {
                    meshRenderer.sortingOrder = Mathf.RoundToInt(-transform.position.y * 100);
                }
            }
        }
	}
}
