using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace HotScripts
{
    public class AudioPlayer: Singleton<AudioPlayer>
    {
        public List<AudioSource> list = new List<AudioSource>();
        protected override void InitializeSingleton()
        {
            DontDestroyOnLoad(this);
            base.InitializeSingleton();
        }

        /// <summary>
        /// 播放声音
        /// </summary>
        public virtual async UniTaskVoid PlaySound(string file, float delay = 0)
        {
            if(string.IsNullOrWhiteSpace(file)) return;
            
            if(file == "0") return;
            
            if (delay > 0)
            {
                await UniTask.Delay(System.TimeSpan.FromSeconds(delay));
            }
            
            if (PlayerPrefs.HasKey("xmSound"))
            {
                if (PlayerPrefs.GetInt("xmSound") != 0)
                {
                    return;
                }
            }

            if (list.Count(x => (x.clip.name == file.Split('.')[0])) >=3 )
            {
                return;
            }

            var clip = await ResMgr.LoadResAsyncHandle<AudioClip>("Assets/Temp/sound/" + file).Task;// HotResManager.ReadSound(file);
            if (clip != null)
            {
                AudioSource source = gameObject.AddComponent<AudioSource>();
                source.playOnAwake = false;
                source.clip = clip;
                source.time = 0;
                source.volume = 1.0f;
                source.rolloffMode = AudioRolloffMode.Linear;
                source.spread = 360;
                source.loop = false;
                source.Play();
                list.Add(source);
                await UniTask.Delay(System.TimeSpan.FromSeconds(clip.length),true);
                list.Remove(source);
                Destroy(source);
            }
        }
    }
}